# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class IFRSStatementLine(models.Model):
    _name = 'ifrs.statement.line'
    _description = 'IFRS Statement Line'
    _order = 'sequence, id'

    statement_id = fields.Many2one(
        'ifrs.financial.statement',
        string='Financial Statement',
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    name = fields.Char(string='Line Description', required=True)
    
    line_type = fields.Selection([
        ('header', 'Header'),
        ('line', 'Line Item'),
        ('subtotal', 'Subtotal'),
        ('total', 'Total'),
        ('note', 'Note Reference')
    ], string='Line Type', required=True, default='line')
    
    statement_section = fields.Selection([
        # Balance Sheet Sections
        ('assets_current', 'Current Assets'),
        ('assets_non_current', 'Non-Current Assets'),
        ('liabilities_current', 'Current Liabilities'),
        ('liabilities_non_current', 'Non-Current Liabilities'),
        ('equity', 'Equity'),
        
        # Income Statement Sections
        ('revenue', 'Revenue'),
        ('cost_of_sales', 'Cost of Sales'),
        ('operating_expenses', 'Operating Expenses'),
        ('finance_costs', 'Finance Costs'),
        ('other_income', 'Other Income'),
        ('tax_expense', 'Tax Expense'),
        
        # Cash Flow Sections
        ('operating_activities', 'Operating Activities'),
        ('investing_activities', 'Investing Activities'),
        ('financing_activities', 'Financing Activities'),
        
        # Equity Changes
        ('share_capital', 'Share Capital'),
        ('retained_earnings', 'Retained Earnings'),
        ('other_reserves', 'Other Reserves'),

        # Notes to Financial Statements
        ('accounting_policies', 'Accounting Policies'),
        ('ppe_details', 'Property, Plant and Equipment Details'),
        ('receivables_details', 'Trade and Other Receivables Details'),
        ('share_capital_details', 'Share Capital Details'),
        ('revenue_details', 'Revenue Details'),
        ('subsequent_events', 'Subsequent Events'),
    ], string='Statement Section')
    
    account_ids = fields.Many2many(
        'account.account',
        string='Related Accounts',
        help='Accounts that contribute to this line item'
    )
    
    # Financial Amounts
    current_amount = fields.Monetary(
        string='Current Period',
        currency_field='currency_id',
        default=0.0
    )
    
    comparative_amount = fields.Monetary(
        string='Comparative Period',
        currency_field='currency_id',
        default=0.0
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        related='statement_id.currency_id',
        store=True
    )
    
    # IFRS Specific Fields
    ifrs_reference = fields.Char(
        string='IFRS Reference',
        help='Reference to specific IFRS standard (e.g., IAS 1.54)'
    )
    
    note_number = fields.Char(
        string='Note Number',
        help='Reference to note in financial statements'
    )
    
    is_calculated = fields.Boolean(
        string='Calculated Line',
        default=False,
        help='This line is calculated from other lines'
    )
    
    calculation_formula = fields.Text(
        string='Calculation Formula',
        help='Formula for calculated lines'
    )
    
    # Presentation
    bold = fields.Boolean(string='Bold', default=False)
    italic = fields.Boolean(string='Italic', default=False)
    underline = fields.Boolean(string='Underline', default=False)
    indent_level = fields.Integer(string='Indent Level', default=0)
    
    # Compliance and Audit
    requires_disclosure = fields.Boolean(
        string='Requires Disclosure',
        default=False,
        help='This item requires additional disclosure in notes'
    )
    
    disclosure_note = fields.Text(string='Disclosure Note')
    
    @api.depends('current_amount', 'comparative_amount')
    def _compute_variance(self):
        for line in self:
            if line.comparative_amount:
                line.variance_amount = line.current_amount - line.comparative_amount
                line.variance_percentage = ((line.current_amount - line.comparative_amount) / 
                                          abs(line.comparative_amount)) * 100 if line.comparative_amount else 0
            else:
                line.variance_amount = line.current_amount
                line.variance_percentage = 0
    
    variance_amount = fields.Monetary(
        string='Variance Amount',
        compute='_compute_variance',
        currency_field='currency_id'
    )
    
    variance_percentage = fields.Float(
        string='Variance %',
        compute='_compute_variance',
        digits=(16, 2)
    )
    
    @api.model
    def create_balance_sheet_structure(self, statement_id):
        """Create standard IFRS balance sheet structure"""
        lines = [
            # ASSETS
            {'name': 'ASSETS', 'line_type': 'header', 'sequence': 10},
            
            # Current Assets
            {'name': 'Current Assets', 'line_type': 'subtotal', 'sequence': 20, 'statement_section': 'assets_current'},
            {'name': 'Cash and Cash Equivalents', 'line_type': 'line', 'sequence': 30, 'statement_section': 'assets_current', 'ifrs_reference': 'IAS 7'},
            {'name': 'Trade and Other Receivables', 'line_type': 'line', 'sequence': 40, 'statement_section': 'assets_current', 'ifrs_reference': 'IFRS 9'},
            {'name': 'Inventories', 'line_type': 'line', 'sequence': 50, 'statement_section': 'assets_current', 'ifrs_reference': 'IAS 2'},
            {'name': 'Prepaid Expenses', 'line_type': 'line', 'sequence': 60, 'statement_section': 'assets_current'},
            {'name': 'Total Current Assets', 'line_type': 'total', 'sequence': 70, 'statement_section': 'assets_current', 'bold': True},
            
            # Non-Current Assets
            {'name': 'Non-Current Assets', 'line_type': 'subtotal', 'sequence': 80, 'statement_section': 'assets_non_current'},
            {'name': 'Property, Plant and Equipment', 'line_type': 'line', 'sequence': 90, 'statement_section': 'assets_non_current', 'ifrs_reference': 'IAS 16'},
            {'name': 'Intangible Assets', 'line_type': 'line', 'sequence': 100, 'statement_section': 'assets_non_current', 'ifrs_reference': 'IAS 38'},
            {'name': 'Investment Property', 'line_type': 'line', 'sequence': 110, 'statement_section': 'assets_non_current', 'ifrs_reference': 'IAS 40'},
            {'name': 'Total Non-Current Assets', 'line_type': 'total', 'sequence': 120, 'statement_section': 'assets_non_current', 'bold': True},
            
            {'name': 'TOTAL ASSETS', 'line_type': 'total', 'sequence': 130, 'bold': True, 'underline': True},
            
            # LIABILITIES AND EQUITY
            {'name': 'LIABILITIES AND EQUITY', 'line_type': 'header', 'sequence': 140},
            
            # Current Liabilities
            {'name': 'Current Liabilities', 'line_type': 'subtotal', 'sequence': 150, 'statement_section': 'liabilities_current'},
            {'name': 'Trade and Other Payables', 'line_type': 'line', 'sequence': 160, 'statement_section': 'liabilities_current'},
            {'name': 'Short-term Borrowings', 'line_type': 'line', 'sequence': 170, 'statement_section': 'liabilities_current', 'ifrs_reference': 'IFRS 9'},
            {'name': 'Current Tax Liabilities', 'line_type': 'line', 'sequence': 180, 'statement_section': 'liabilities_current', 'ifrs_reference': 'IAS 12'},
            {'name': 'Total Current Liabilities', 'line_type': 'total', 'sequence': 190, 'statement_section': 'liabilities_current', 'bold': True},
            
            # Non-Current Liabilities
            {'name': 'Non-Current Liabilities', 'line_type': 'subtotal', 'sequence': 200, 'statement_section': 'liabilities_non_current'},
            {'name': 'Long-term Borrowings', 'line_type': 'line', 'sequence': 210, 'statement_section': 'liabilities_non_current', 'ifrs_reference': 'IFRS 9'},
            {'name': 'Deferred Tax Liabilities', 'line_type': 'line', 'sequence': 220, 'statement_section': 'liabilities_non_current', 'ifrs_reference': 'IAS 12'},
            {'name': 'Total Non-Current Liabilities', 'line_type': 'total', 'sequence': 230, 'statement_section': 'liabilities_non_current', 'bold': True},
            
            {'name': 'Total Liabilities', 'line_type': 'total', 'sequence': 240, 'bold': True},
            
            # Equity
            {'name': 'Equity', 'line_type': 'subtotal', 'sequence': 250, 'statement_section': 'equity'},
            {'name': 'Share Capital', 'line_type': 'line', 'sequence': 260, 'statement_section': 'equity', 'ifrs_reference': 'IAS 32'},
            {'name': 'Retained Earnings', 'line_type': 'line', 'sequence': 270, 'statement_section': 'equity'},
            {'name': 'Other Reserves', 'line_type': 'line', 'sequence': 280, 'statement_section': 'equity'},
            {'name': 'Total Equity', 'line_type': 'total', 'sequence': 290, 'statement_section': 'equity', 'bold': True},
            
            {'name': 'TOTAL LIABILITIES AND EQUITY', 'line_type': 'total', 'sequence': 300, 'bold': True, 'underline': True},
        ]
        
        for line_data in lines:
            line_data['statement_id'] = statement_id
            self.create(line_data)
