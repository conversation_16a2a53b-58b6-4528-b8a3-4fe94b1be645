"""
Management command to load chart of accounts from Odoo data
"""

import json
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from accounting.models import AccountAccount
from core.models import Company, Country, Currency

User = get_user_model()


class Command(BaseCommand):
    help = 'Load chart of accounts from Odoo data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing accounts',
        )
        parser.add_argument(
            '--company',
            type=str,
            help='Company name to associate accounts with',
            default='Default Company'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading chart of accounts from Odoo data...'))
        
        # Load accounts from extracted JSON file
        json_file = 'accounts_data.json'
        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(
                    f'Accounts data file {json_file} not found. '
                    'Please run: python extract_odoo_data.py first'
                )
            )
            return
        
        with open(json_file, 'r') as f:
            accounts_data = json.load(f)

        created_count = 0
        updated_count = 0
        
        # Get or create admin user for create_uid/write_uid
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )
        
        # Get default country (US)
        try:
            default_country = Country.objects.get(code='US')
        except Country.DoesNotExist:
            # If US doesn't exist, get the first available country
            default_country = Country.objects.first()
            if not default_country:
                self.stdout.write(
                    self.style.ERROR('No countries found. Please load countries first.')
                )
                return

        # Get default currency (USD)
        try:
            default_currency = Currency.objects.get(name='USD')
        except Currency.DoesNotExist:
            # If USD doesn't exist, get the first available currency
            default_currency = Currency.objects.first()
            if not default_currency:
                self.stdout.write(
                    self.style.ERROR('No currencies found. Please load currencies first.')
                )
                return

        # Get or create default company
        company, created = Company.objects.get_or_create(
            name=options['company'],
            defaults={
                'country_id': default_country,
                'currency_id': default_currency,
                'create_uid': admin_user,
                'write_uid': admin_user,
            }
        )
        
        if created:
            self.stdout.write(f'Created company: {company.name}')
        
        with transaction.atomic():
            for account_data in accounts_data:
                code = account_data['code']
                
                # Extract name from nested structure
                if isinstance(account_data['name'], dict):
                    name = account_data['name'].get('en_US', code)
                else:
                    name = account_data['name']
                
                # Prepare clean data for Django model
                clean_data = {
                    'code': code,
                    'name': name,
                    'reconcile': account_data.get('reconcile', False),
                    'deprecated': account_data.get('deprecated', False),
                    'company_id': company,
                    'create_uid': admin_user,
                    'write_uid': admin_user,
                }
                
                try:
                    account = AccountAccount.objects.get(code=code, company_id=company)
                    if options['update']:
                        # Update existing account
                        for field, value in clean_data.items():
                            if field not in ['create_uid']:  # Don't update create_uid
                                setattr(account, field, value)
                        account.save()
                        updated_count += 1
                        self.stdout.write(f'Updated account: {code} - {name}')
                    else:
                        self.stdout.write(f'Account already exists: {code} - {name}')
                        
                except AccountAccount.DoesNotExist:
                    # Create new account
                    account = AccountAccount.objects.create(**clean_data)
                    created_count += 1
                    self.stdout.write(f'Created account: {code} - {name}')

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nChart of accounts loading completed!\n'
                f'Created: {created_count} accounts\n'
                f'Updated: {updated_count} accounts\n'
                f'Total accounts in system: {AccountAccount.objects.count()}\n'
                f'Company: {company.name}'
            )
        )
        
        # Show account categories
        self.stdout.write('\nAccount structure by code ranges:')
        ranges = [
            ('100000-199999', 'Assets'),
            ('200000-299999', 'Liabilities'),
            ('300000-399999', 'Equity'),
            ('400000-499999', 'Revenue'),
            ('500000-599999', 'Expenses'),
        ]
        
        for code_range, category in ranges:
            start_code = code_range.split('-')[0]
            end_code = code_range.split('-')[1]
            count = AccountAccount.objects.filter(
                code__gte=start_code,
                code__lte=end_code,
                company_id=company
            ).count()
            if count > 0:
                self.stdout.write(f'  {category}: {count} accounts ({code_range})')
