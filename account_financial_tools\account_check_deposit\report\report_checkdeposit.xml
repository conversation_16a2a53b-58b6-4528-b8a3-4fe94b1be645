<?xml version="1.0" encoding="utf-8" ?>
<!--
    Copyright 2014-2022 Akretion France (www.akretion.com)
    @author: <PERSON> <<EMAIL>>
    The licence is in the file __manifest__.py
-->
<odoo>
    <template id="report_checkdeposit">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.external_layout">
                    <div class="page">
                        <h1>Check Deposit n°<span t-field="o.name" /></h1>
                        <h3>Bank:</h3>
                        <p>
                            <span
                                t-field="o.bank_journal_id.bank_account_id.bank_id.name"
                            />
                            <br />
                            <span
                                t-field="o.bank_journal_id.bank_account_id.bank_id.street"
                            />
                            <br />
                            <span
                                t-field="o.bank_journal_id.bank_account_id.bank_id.zip"
                            />
                            <span
                                t-field="o.bank_journal_id.bank_account_id.bank_id.city"
                            />
                        </p>
                        <h3>Beneficiary:</h3>
                        <div
                            t-field="o.company_id.partner_id"
                            t-options='{"widget": "contact", "fields": ["address", "name", "phone", "email"], "no_marker": true, "phone_icons": True}'
                        />
                        <p>
                            <b>Bank Account Number to Credit:</b>
                            <span
                                t-field="o.bank_journal_id.bank_account_id.acc_number"
                            />
                        </p>
                        <p>
                            <b>Check Currency:</b>
                            <span t-field="o.currency_id.name" />
                        </p>
                        <p>
                            <b>Date:</b>
                            <span t-field="o.deposit_date" />
                        </p>
                        <p>
                            <b>Number of checks:</b>
                            <span t-field="o.check_count" />
                        </p>
                        <h3>List of checks:</h3>
                        <table class="table table-condensed">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th class="text-start">Reference</th>
                                    <th class="text-start">Debtor</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="o.check_payment_ids" t-as="move_line">
                                    <tr>
                                        <td class="text-center">
                                            <span t-field="move_line.date" />
                                        </td>
                                        <td>
                                            <span t-field="move_line.ref" />
                                        </td>
                                        <td>
                                            <span t-field="move_line.partner_id.name" />
                                        </td>
                                        <td class="text-end">
                                            <t
                                                t-if="o.currency_id == o.company_id.currency_id"
                                            >
                                                <span t-field="move_line.debit" />
                                            </t>
                                            <t
                                                t-if="o.currency_id != o.company_id.currency_id"
                                            >
                                                <span
                                                    t-field="move_line.amount_currency"
                                                />
                                            </t>
                                        </td>
                                    </tr>
                                </t>
                                <tr>
                                    <td />
                                    <td />
                                    <td class="text-end">
                                        <b>Total:</b>
                                    </td>
                                    <td class="text-end">
                                        <b>
                                            <span t-field="o.total_amount" />
                                        </b>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
