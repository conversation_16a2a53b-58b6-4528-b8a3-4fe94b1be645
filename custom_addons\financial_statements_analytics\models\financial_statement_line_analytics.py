# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class FinancialStatementLineAnalytics(models.Model):
    _name = 'financial.statement.line.analytics'
    _description = 'Financial Statement Line with Analytics'
    _order = 'sequence, id'

    statement_id = fields.Many2one(
        'financial.statement.analytics',
        string='Financial Statement',
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    name = fields.Char(string='Line Description', required=True)
    
    line_type = fields.Selection([
        ('header', 'Header'),
        ('line', 'Line Item'),
        ('subtotal', 'Subtotal'),
        ('total', 'Total'),
        ('note', 'Note Reference')
    ], string='Line Type', required=True, default='line')
    
    line_section = fields.Selection([
        # Balance Sheet Sections
        ('assets', 'Assets'),
        ('current_assets', 'Current Assets'),
        ('non_current_assets', 'Non-Current Assets'),
        ('liabilities', 'Liabilities'),
        ('current_liabilities', 'Current Liabilities'),
        ('non_current_liabilities', 'Non-Current Liabilities'),
        ('equity', 'Equity'),
        
        # Income Statement Sections
        ('revenue', 'Revenue'),
        ('cost_of_sales', 'Cost of Sales'),
        ('gross_profit', 'Gross Profit'),
        ('operating_expenses', 'Operating Expenses'),
        ('operating_income', 'Operating Income'),
        ('finance_costs', 'Finance Costs'),
        ('net_income', 'Net Income'),
        
        # Cash Flow Sections
        ('operating_activities', 'Operating Activities'),
        ('investing_activities', 'Investing Activities'),
        ('financing_activities', 'Financing Activities'),
        ('net_cash_change', 'Net Cash Change'),
        
        # Equity Changes
        ('share_capital', 'Share Capital'),
        ('retained_earnings', 'Retained Earnings'),
        ('other_comprehensive_income', 'Other Comprehensive Income'),
        ('total_equity', 'Total Equity'),
    ], string='Statement Section')
    
    indent_level = fields.Integer(
        string='Indent Level',
        default=0,
        help='Indentation level for hierarchical display'
    )
    
    account_ids = fields.Many2many(
        'account.account',
        string='Related Accounts',
        help='Accounts that contribute to this line item'
    )
    
    # Financial Amounts
    current_amount = fields.Monetary(
        string='Current Period',
        currency_field='currency_id',
        default=0.0
    )
    
    comparative_amount = fields.Monetary(
        string='Comparative Period',
        currency_field='currency_id',
        default=0.0
    )
    
    variance_amount = fields.Monetary(
        string='Variance',
        compute='_compute_variance',
        store=True,
        currency_field='currency_id'
    )
    
    variance_percentage = fields.Float(
        string='Variance %',
        compute='_compute_variance',
        store=True,
        digits=(12, 2)
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        related='statement_id.currency_id',
        store=True
    )
    
    # Analytics Fields
    is_expandable = fields.Boolean(
        string='Expandable',
        default=False,
        help='This line can be expanded to show detail'
    )
    
    is_expanded = fields.Boolean(
        string='Expanded',
        default=False,
        help='This line is currently expanded'
    )
    
    parent_line_id = fields.Many2one(
        'financial.statement.line.analytics',
        string='Parent Line',
        help='Parent line for hierarchical structure'
    )
    
    child_line_ids = fields.One2many(
        'financial.statement.line.analytics',
        'parent_line_id',
        string='Child Lines'
    )
    
    # Calculation Fields
    is_calculated = fields.Boolean(
        string='Calculated Line',
        default=False,
        help='This line is calculated from other lines'
    )
    
    calculation_formula = fields.Text(
        string='Calculation Formula',
        help='Formula for calculated lines'
    )
    
    # Analytics Data
    trend_data = fields.Text(
        string='Trend Data',
        help='JSON data for trend analysis'
    )
    
    drill_down_data = fields.Text(
        string='Drill Down Data',
        help='JSON data for drill-down functionality'
    )
    
    # Display Properties
    bold = fields.Boolean(string='Bold', default=False)
    italic = fields.Boolean(string='Italic', default=False)
    underline = fields.Boolean(string='Underline', default=False)
    
    font_size = fields.Selection([
        ('small', 'Small'),
        ('normal', 'Normal'),
        ('large', 'Large')
    ], string='Font Size', default='normal')
    
    text_color = fields.Char(
        string='Text Color',
        default='#000000',
        help='Hex color code for text'
    )
    
    background_color = fields.Char(
        string='Background Color',
        help='Hex color code for background'
    )
    
    # Notes and References
    note_number = fields.Char(
        string='Note Number',
        help='Reference to note in financial statements'
    )
    
    description = fields.Text(
        string='Description',
        help='Detailed description of the line item'
    )
    
    @api.depends('current_amount', 'comparative_amount')
    def _compute_variance(self):
        """Compute variance amount and percentage"""
        for record in self:
            record.variance_amount = record.current_amount - record.comparative_amount
            
            if record.comparative_amount:
                record.variance_percentage = (record.variance_amount / abs(record.comparative_amount)) * 100
            else:
                record.variance_percentage = 0.0
    
    def action_drill_down(self):
        """Open drill-down view for this line"""
        if not self.is_expandable:
            return
        
        # Get related account moves
        domain = []
        if self.account_ids:
            domain.append(('account_id', 'in', self.account_ids.ids))
        
        domain.extend([
            ('date', '>=', self.statement_id.date_from),
            ('date', '<=', self.statement_id.date_to),
            ('company_id', '=', self.statement_id.company_id.id),
        ])
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Account Moves - %s') % self.name,
            'res_model': 'account.move.line',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {
                'search_default_group_by_account': 1,
                'search_default_group_by_date': 1,
            }
        }
    
    def action_expand_collapse(self):
        """Toggle expand/collapse state"""
        self.is_expanded = not self.is_expanded
        
        # Show/hide child lines
        for child in self.child_line_ids:
            child.write({'sequence': child.sequence if self.is_expanded else -child.sequence})
    
    def get_formatted_amount(self, amount_field='current_amount'):
        """Get formatted amount with proper currency symbol"""
        amount = getattr(self, amount_field, 0.0)
        return self.currency_id.format(amount)
    
    def get_css_classes(self):
        """Get CSS classes for styling"""
        classes = []
        
        if self.line_type == 'header':
            classes.append('financial-header')
        elif self.line_type == 'total':
            classes.append('financial-total')
        elif self.line_type == 'subtotal':
            classes.append('financial-subtotal')
        
        if self.bold:
            classes.append('font-weight-bold')
        if self.italic:
            classes.append('font-italic')
        if self.underline:
            classes.append('text-decoration-underline')
        
        classes.append(f'indent-level-{self.indent_level}')
        
        return ' '.join(classes)
    
    def get_inline_styles(self):
        """Get inline CSS styles"""
        styles = []
        
        if self.text_color and self.text_color != '#000000':
            styles.append(f'color: {self.text_color}')
        
        if self.background_color:
            styles.append(f'background-color: {self.background_color}')
        
        if self.font_size == 'small':
            styles.append('font-size: 0.9em')
        elif self.font_size == 'large':
            styles.append('font-size: 1.2em')
        
        return '; '.join(styles)
    
    @api.model
    def create_sample_lines(self, statement_id):
        """Create sample lines for testing"""
        sample_lines = [
            {
                'statement_id': statement_id,
                'name': 'ASSETS',
                'line_type': 'header',
                'sequence': 10,
                'line_section': 'assets',
                'bold': True,
                'font_size': 'large',
            },
            {
                'statement_id': statement_id,
                'name': 'Current Assets',
                'line_type': 'header',
                'sequence': 20,
                'line_section': 'current_assets',
                'indent_level': 1,
                'bold': True,
                'current_amount': 1766516.20,
                'comparative_amount': 1500000.00,
            },
            {
                'statement_id': statement_id,
                'name': 'Cash and Cash Equivalents',
                'line_type': 'line',
                'sequence': 30,
                'line_section': 'current_assets',
                'indent_level': 2,
                'current_amount': 96130.37,
                'comparative_amount': 85000.00,
                'is_expandable': True,
            },
            {
                'statement_id': statement_id,
                'name': 'Trade Receivables',
                'line_type': 'line',
                'sequence': 40,
                'line_section': 'current_assets',
                'indent_level': 2,
                'current_amount': 1670385.83,
                'comparative_amount': 1415000.00,
                'is_expandable': True,
            },
        ]
        
        for line_data in sample_lines:
            self.create(line_data)
    
    @api.constrains('parent_line_id')
    def _check_parent_recursion(self):
        """Check for recursive parent relationships"""
        for record in self:
            if record.parent_line_id:
                current = record.parent_line_id
                while current:
                    if current == record:
                        raise ValidationError(_('You cannot create recursive parent relationships.'))
                    current = current.parent_line_id
