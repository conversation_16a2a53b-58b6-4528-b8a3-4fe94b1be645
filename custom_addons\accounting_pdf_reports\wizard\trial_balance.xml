<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_report_balance_view" model="ir.ui.view">
        <field name="name">Trial Balance</field>
        <field name="model">account.balance.report</field>
        <field name="inherit_id" ref="accounting_pdf_reports.account_common_report_view"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//field[@name='target_move']" position="after">
                    <field name="display_account" widget="radio"/>
                    <newline/>
                </xpath>
                <xpath expr="//field[@name='journal_ids']" position="after">
                    <field name="analytic_account_ids" widget="many2many_tags"
                           invisible="1"
                           options="{'no_open': True, 'no_create': True}"/>
                </xpath>
            </data>
        </field>
    </record>

    <record id="action_account_balance_menu" model="ir.actions.act_window">
        <field name="name">Trial Balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.balance.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_report_balance_view"/>
        <field name="target">new</field>
        <field name="binding_model_id" ref="account.model_account_account" />
        <field name="binding_type">report</field>
    </record>

    <menuitem id="menu_general_balance_report"
              name="Trial Balance"
              sequence="20"
              parent="menu_finance_audit_reports"
              action="action_account_balance_menu"
              groups="account.group_account_user,account.group_account_manager"/>

</odoo>
