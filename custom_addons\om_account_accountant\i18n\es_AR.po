# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_accountant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-21 19:42+0000\n"
"PO-Revision-Date: 2024-03-21 19:42+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_group_action
#: model:ir.ui.menu,name:om_account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Grupos de cuentas"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_account_tag
#: model:ir.ui.menu,name:om_account_accountant.menu_account_tag
msgid "Account Tags"
msgstr "Etiquetas de cuenta"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_partner_property_form
msgid "Accounting"
msgstr "Contabilidad"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Anglo-Saxon Accounting"
msgstr "Contabilidad Anglosajona"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_bank
msgid "Bank Statements"
msgstr "Extracto bancario"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_bank_and_cash
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_bank_cash
msgid "Bank and Cash"
msgstr "Banco y Efectivo"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_cash
msgid "Cash Registers"
msgstr "Cajas Registradoras"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Group By"
msgstr "Agrupar por"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_finance_entries_accounting_journals
msgid "Journals"
msgstr "Diarios"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_misc
msgid "Miscellaneous"
msgstr "Misceláneas"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Method"
msgstr "Método de pago"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_payment_method
#: model:ir.ui.menu,name:om_account_accountant.menu_account_payment_method
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_tree
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Type"
msgstr "Tipos de pago"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_purchase
msgid "Purchases"
msgstr "Compras"

#. module: om_account_accountant
#: model:ir.actions.server,name:om_account_accountant.action_account_reconciliation
msgid "Reconcile"
msgstr "Conciliación"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid ""
"Record the cost of a good as an expense when this good is\n"
"                                invoiced to a final customer (instead of recording the cost as soon\n"
"                                as the product is received in stock)."
msgstr ""
"Registrar el costo de un bien como gasto cuando este bien se\n"
"\t\t\t\t facture a un cliente final (en lugar de registrar el costo tan pronto\n"
"\t\t\t\t como el producto se recibe en stock)."

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid ""
"Record the cost of a good as an expense when this good is invoiced to a "
"final customer."
msgstr ""
"Registrar el costo de un bien como gasto cuando este bien se factura a "
"cliente final."

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_sales
msgid "Sales"
msgstr "Ventas"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_templates
msgid "Templates"
msgstr "Plantillas"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Usar Contabilidad Anglosajona"
