<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_move_form" model="ir.ui.view">
        <field name="name">account.move.form.account.asset.management</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form" />
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
                <button
                    name="action_view_assets"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-cube"
                    invisible="asset_count == 0"
                    groups="account.group_account_invoice"
                >
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="asset_count" />
                        </span>
                        <span class="o_stat_text"> Asset(s)</span>
                    </div>
                </button>
            </xpath>
            <xpath
                expr="//field[@name='invoice_line_ids']/tree/field[@name='quantity']"
                position="before"
            >
                <field
                    name="asset_profile_id"
                    column_invisible="parent.move_type not in ('in_invoice', 'in_refund')"
                    optional="show"
                    groups="account.group_account_invoice"
                />
                <field
                    name="asset_id"
                    column_invisible="parent.move_type not in ('out_invoice', 'out_refund')"
                    groups="account.group_account_manager,account.group_account_invoice"
                    optional="show"
                />
            </xpath>
            <xpath
                expr="//field[@name='invoice_line_ids']/form/sheet/group/field[@name='quantity']"
                position="before"
            >
                <field
                    name="asset_profile_id"
                    invisible="parent.move_type not in ('in_invoice', 'in_refund')"
                />
                <field
                    name="asset_id"
                    invisible="parent.move_type not in ('out_invoice', 'out_refund')"
                    groups="account.group_account_manager"
                />
            </xpath>
            <xpath
                expr="//notebook//field[@name='line_ids']/tree/field[@name='date_maturity']"
                position="after"
            >
                <field
                    name="asset_profile_id"
                    domain="[('company_id','=', parent.company_id)]"
                    optional="hide"
                    groups="account.group_account_invoice"
                />
                <field
                    name="asset_id"
                    groups="account.group_account_manager,account.group_account_invoice"
                    optional="show"
                />
            </xpath>
        </field>
    </record>
</odoo>
