<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Financial Statement Analytics Report Template -->
        <template id="report_financial_statement_analytics_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <!-- Header -->
                            <div class="row">
                                <div class="col-12 text-center">
                                    <h2><strong t-field="doc.company_id.name"/></h2>
                                    <h3 t-field="doc.name"/>
                                    <p>
                                        <strong>Period:</strong> 
                                        <span t-field="doc.date_from"/> to <span t-field="doc.date_to"/>
                                    </p>
                                    <p t-if="doc.comparative_period">
                                        <strong>Comparative Period:</strong> 
                                        <span t-field="doc.comparative_date_from"/> to <span t-field="doc.comparative_date_to"/>
                                    </p>
                                </div>
                            </div>

                            <!-- Key Financial Metrics -->
                            <div class="row mt-4" t-if="doc.state != 'draft'">
                                <div class="col-12">
                                    <h4>Key Financial Metrics</h4>
                                    <table class="table table-bordered">
                                        <tr>
                                            <td><strong>Total Assets</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.total_assets" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Liabilities</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.total_liabilities" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Equity</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.total_equity" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Net Income</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.net_income" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Financial Statement Lines -->
                            <div class="row mt-4" t-if="doc.statement_line_ids">
                                <div class="col-12">
                                    <h4>Financial Statement Details</h4>
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Description</th>
                                                <th class="text-right">Current Period</th>
                                                <th class="text-right" t-if="doc.comparative_period">Comparative Period</th>
                                                <th class="text-right" t-if="doc.comparative_period">Variance</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="doc.statement_line_ids" t-as="line">
                                                <tr t-att-class="'font-weight-bold' if line.line_type == 'header' else 'font-weight-bold border-top border-bottom' if line.line_type == 'total' else ''">
                                                    <td t-att-style="'padding-left: %spx;' % (line.indent_level * 20)">
                                                        <span t-field="line.name"/>
                                                        <span t-if="line.note_number" class="text-muted"> (Note <span t-field="line.note_number"/>)</span>
                                                    </td>
                                                    <td class="text-right">
                                                        <span t-if="line.current_amount != 0" t-field="line.current_amount" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                                    </td>
                                                    <td class="text-right" t-if="doc.comparative_period">
                                                        <span t-if="line.comparative_amount != 0" t-field="line.comparative_amount" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                                    </td>
                                                    <td class="text-right" t-if="doc.comparative_period">
                                                        <span t-if="line.variance_amount != 0" t-field="line.variance_amount" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                                        <span t-if="line.variance_percentage != 0" class="text-muted">
                                                            (<span t-field="line.variance_percentage"/>%)
                                                        </span>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Key Financial Ratios -->
                            <div class="row mt-4" t-if="doc.enable_analytics and doc.ratio_analysis_ids">
                                <div class="col-12">
                                    <h4>Key Financial Ratios</h4>
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Ratio Category</th>
                                                <th>Ratio Name</th>
                                                <th class="text-right">Value</th>
                                                <th class="text-right">Benchmark</th>
                                                <th class="text-center">Interpretation</th>
                                                <th class="text-center">Trend</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="doc.ratio_analysis_ids" t-as="ratio">
                                                <tr>
                                                    <td><span t-field="ratio.ratio_category"/></td>
                                                    <td><span t-field="ratio.ratio_name"/></td>
                                                    <td class="text-right"><span t-field="ratio.ratio_value"/></td>
                                                    <td class="text-right"><span t-field="ratio.benchmark_value"/></td>
                                                    <td class="text-center">
                                                        <span t-field="ratio.interpretation" 
                                                              t-att-class="'badge badge-success' if ratio.interpretation == 'excellent' else 'badge badge-info' if ratio.interpretation == 'good' else 'badge badge-warning' if ratio.interpretation == 'needs_attention' else 'badge badge-danger'"/>
                                                    </td>
                                                    <td class="text-center">
                                                        <span t-field="ratio.trend_direction"
                                                              t-att-class="'badge badge-success' if ratio.trend_direction == 'improving' else 'badge badge-warning' if ratio.trend_direction == 'stable' else 'badge badge-danger'"/>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Ratio Analysis Summary -->
                            <div class="row mt-4" t-if="doc.enable_analytics">
                                <div class="col-6">
                                    <h5>Liquidity Analysis</h5>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Current Ratio</td>
                                            <td class="text-right"><span t-field="doc.current_ratio"/></td>
                                        </tr>
                                        <tr>
                                            <td>Quick Ratio</td>
                                            <td class="text-right"><span t-field="doc.quick_ratio"/></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-6">
                                    <h5>Profitability Analysis</h5>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Return on Equity</td>
                                            <td class="text-right"><span t-field="doc.return_on_equity"/>%</td>
                                        </tr>
                                        <tr>
                                            <td>Return on Assets</td>
                                            <td class="text-right"><span t-field="doc.return_on_assets"/>%</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="row mt-5">
                                <div class="col-6">
                                    <p>
                                        <strong>Prepared by:</strong> <span t-field="doc.prepared_by.name"/><br/>
                                        <strong>Date:</strong> <span t-field="doc.prepared_date" t-options="{'widget': 'date'}"/>
                                    </p>
                                </div>
                                <div class="col-6" t-if="doc.reviewed_by">
                                    <p>
                                        <strong>Reviewed by:</strong> <span t-field="doc.reviewed_by.name"/><br/>
                                        <strong>Date:</strong> <span t-field="doc.reviewed_date" t-options="{'widget': 'date'}"/>
                                    </p>
                                </div>
                            </div>

                            <!-- Page Break for Multi-page Reports -->
                            <div style="page-break-before: always;" t-if="doc.statement_type == 'complete_set'"/>

                        </div>
                    </t>
                </t>
            </t>
        </template>

        <!-- Dashboard Report Template -->
        <template id="report_financial_analytics_dashboard_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="row">
                                <div class="col-12 text-center">
                                    <h2>Financial Analytics Dashboard</h2>
                                    <h3 t-field="doc.statement_id.name"/>
                                    <p><span t-field="doc.statement_id.company_id.name"/></p>
                                </div>
                            </div>

                            <!-- KPI Summary -->
                            <div class="row mt-4">
                                <div class="col-3 text-center">
                                    <div class="border p-3">
                                        <h6>Total Assets</h6>
                                        <h4 t-field="doc.total_assets" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                    </div>
                                </div>
                                <div class="col-3 text-center">
                                    <div class="border p-3">
                                        <h6>Total Equity</h6>
                                        <h4 t-field="doc.total_equity" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                    </div>
                                </div>
                                <div class="col-3 text-center">
                                    <div class="border p-3">
                                        <h6>Net Income</h6>
                                        <h4 t-field="doc.net_income" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                    </div>
                                </div>
                                <div class="col-3 text-center">
                                    <div class="border p-3">
                                        <h6>Current Ratio</h6>
                                        <h4 t-field="doc.current_ratio"/>
                                    </div>
                                </div>
                            </div>

                            <!-- Ratio Summary -->
                            <div class="row mt-4">
                                <div class="col-6">
                                    <h5>Liquidity Ratios</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <td>Current Ratio</td>
                                            <td class="text-right"><span t-field="doc.current_ratio"/></td>
                                        </tr>
                                        <tr>
                                            <td>Quick Ratio</td>
                                            <td class="text-right"><span t-field="doc.quick_ratio"/></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-6">
                                    <h5>Profitability Ratios</h5>
                                    <table class="table table-bordered">
                                        <tr>
                                            <td>Return on Equity</td>
                                            <td class="text-right"><span t-field="doc.return_on_equity"/>%</td>
                                        </tr>
                                        <tr>
                                            <td>Return on Assets</td>
                                            <td class="text-right"><span t-field="doc.return_on_assets"/>%</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                        </div>
                    </t>
                </t>
            </t>
        </template>

        <!-- Ratio Analysis Report Template -->
        <template id="report_financial_ratio_analysis_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="row">
                                <div class="col-12 text-center">
                                    <h2>Financial Ratio Analysis</h2>
                                    <h3 t-field="doc.statement_id.name"/>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Ratio Name</th>
                                                <th>Formula</th>
                                                <th class="text-right">Value</th>
                                                <th class="text-right">Benchmark</th>
                                                <th>Interpretation</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span t-field="doc.ratio_category"/></td>
                                                <td><span t-field="doc.ratio_name"/></td>
                                                <td><span t-field="doc.formula"/></td>
                                                <td class="text-right"><span t-field="doc.ratio_value"/></td>
                                                <td class="text-right"><span t-field="doc.benchmark_value"/></td>
                                                <td><span t-field="doc.interpretation"/></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-4" t-if="doc.interpretation_text">
                                <div class="col-12">
                                    <h5>Analysis</h5>
                                    <p t-field="doc.interpretation_text"/>
                                </div>
                            </div>

                            <div class="row mt-4" t-if="doc.recommendations">
                                <div class="col-12">
                                    <h5>Recommendations</h5>
                                    <p t-field="doc.recommendations"/>
                                </div>
                            </div>

                        </div>
                    </t>
                </t>
            </t>
        </template>

    </data>
</odoo>
