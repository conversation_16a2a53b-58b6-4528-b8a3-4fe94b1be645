<?xml version='1.0' encoding='utf-8' ?>
<!-- # <AUTHOR> <EMAIL>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="account_asset_view_form_inherit" model="ir.ui.view">
        <field name="name">account.asset.form.inherit</field>
        <field name="model">account.asset</field>
        <field
            name="inherit_id"
            ref="account_asset_management.account_asset_view_form"
        />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='group_ids']" position="after">
                <field name="account_asset_id" invisible="not profile_id" />
                <field
                    name="account_depreciation_id"
                    readonly="state in ['open', 'close', 'removed']"
                    invisible="not profile_id"
                />
                <field
                    name="account_expense_depreciation_id"
                    readonly="state in ['open', 'close', 'removed']"
                    invisible="not profile_id"
                />
            </xpath>
        </field>
    </record>
</odoo>
