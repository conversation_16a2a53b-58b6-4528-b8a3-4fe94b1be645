# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* report_substitute
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-05-24 14:39+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: none\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: report_substitute
#: model:ir.model,name:report_substitute.model_ir_actions_report_substitution_rule
msgid "Action Report Substitution Rule"
msgstr "Regola sostituzione azione resoconto"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__create_date
msgid "Created on"
msgstr "Creato il"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__domain
msgid "Domain"
msgstr "Dominio"

#. module: report_substitute
#: model:ir.model,name:report_substitute.model_mail_thread
msgid "Email Thread"
msgstr "Discussione e-mail"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__id
msgid "ID"
msgstr "ID"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__model
msgid "Model Name"
msgstr "Nome modello"

#. module: report_substitute
#: model:ir.model,name:report_substitute.model_ir_actions_report
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__action_report_id
msgid "Report Action"
msgstr "Azione resoconto"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: report_substitute
#: model:ir.actions.report,name:report_substitute.substitution_report_print_2
msgid "Substitution 2 For Technical guide"
msgstr "Sostituzione 2 per guida tecnica"

#. module: report_substitute
#: model:ir.actions.report,name:report_substitute.substitution_report_print
msgid "Substitution For Technical guide"
msgstr "Sostituzione per guida tecnica"

#. module: report_substitute
#: model_terms:ir.ui.view,arch_db:report_substitute.substitution_report
msgid "Substitution Report"
msgstr "Resoconto sostituzione"

#. module: report_substitute
#: model_terms:ir.ui.view,arch_db:report_substitute.substitution_report_2
msgid "Substitution Report 2"
msgstr "Resoconto 2 sostituzione"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report_substitution_rule__substitution_action_report_id
msgid "Substitution Report Action"
msgstr "Azione resoconto sostituzione"

#. module: report_substitute
#: model:ir.model.fields,field_description:report_substitute.field_ir_actions_report__action_report_substitution_rule_ids
#: model_terms:ir.ui.view,arch_db:report_substitute.ir_actions_report_form_view
msgid "Substitution Rules"
msgstr "Regole sostituzione"

#. module: report_substitute
#. odoo-python
#: code:addons/report_substitute/models/ir_actions_report_substitution_rule.py:0
#, python-format
msgid "Substitution infinite loop detected"
msgstr "Rilevato ciclo infinito sostituzione"
