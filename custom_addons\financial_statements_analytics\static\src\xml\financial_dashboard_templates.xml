<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- Financial Analytics Dashboard Template -->
    <t t-name="financial_statements_analytics.FinancialAnalyticsDashboard" owl="1">
        <div class="financial-analytics-dashboard">
            <div class="container-fluid">
                
                <!-- Loading Spinner -->
                <div t-if="state.loading" class="text-center p-5">
                    <div class="loading-spinner"></div>
                    <p class="mt-3">Loading dashboard data...</p>
                </div>
                
                <!-- Dashboard Content -->
                <div t-else="" class="dashboard-content fade-in">
                    
                    <!-- KPI Cards Row -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="kpi-box text-center">
                                <h4>Total Assets</h4>
                                <h2 class="text-primary">
                                    <t t-esc="formatCurrency(state.dashboardData.total_assets)"/>
                                </h2>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="kpi-box text-center">
                                <h4>Total Equity</h4>
                                <h2 class="text-success">
                                    <t t-esc="formatCurrency(state.dashboardData.total_equity)"/>
                                </h2>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="kpi-box text-center">
                                <h4>Net Income</h4>
                                <h2 class="text-info">
                                    <t t-esc="formatCurrency(state.dashboardData.net_income)"/>
                                </h2>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="kpi-box text-center">
                                <h4>Current Ratio</h4>
                                <h2 class="text-warning">
                                    <t t-esc="state.dashboardData.current_ratio"/>
                                </h2>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Charts Row -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Asset Composition
                                </div>
                                <div class="dashboard-card-body">
                                    <canvas id="asset_composition_chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Capital Structure
                                </div>
                                <div class="dashboard-card-body">
                                    <canvas id="capital_structure_chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ratios Summary Row -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Liquidity Ratios
                                </div>
                                <div class="dashboard-card-body">
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>Current Ratio:</strong></div>
                                        <div class="col-6"><t t-esc="state.dashboardData.current_ratio"/></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>Quick Ratio:</strong></div>
                                        <div class="col-6"><t t-esc="state.dashboardData.quick_ratio"/></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Profitability Ratios
                                </div>
                                <div class="dashboard-card-body">
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>ROE:</strong></div>
                                        <div class="col-6"><t t-esc="formatPercentage(state.dashboardData.return_on_equity)"/></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6"><strong>ROA:</strong></div>
                                        <div class="col-6"><t t-esc="formatPercentage(state.dashboardData.return_on_assets)"/></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Charts Row -->
                    <div class="row mb-4" t-if="state.selectedView === 'overview'">
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Ratio Performance
                                </div>
                                <div class="dashboard-card-body">
                                    <canvas id="ratio_radar_chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Financial Trends
                                </div>
                                <div class="dashboard-card-body">
                                    <canvas id="trend_line_chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="dashboard-card">
                                <div class="dashboard-card-header">
                                    Quick Actions
                                </div>
                                <div class="dashboard-card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <button class="btn btn-primary btn-block" t-on-click="refreshDashboard">
                                                <i class="fa fa-refresh"></i> Refresh Data
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-info btn-block" t-on-click="exportDashboard">
                                                <i class="fa fa-download"></i> Export PDF
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-success btn-block" t-on-click="() => changeDashboardView('ratios')">
                                                <i class="fa fa-chart-bar"></i> Ratio Analysis
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-warning btn-block" t-on-click="() => changeDashboardView('trends')">
                                                <i class="fa fa-line-chart"></i> Trend Analysis
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </t>

</templates>
