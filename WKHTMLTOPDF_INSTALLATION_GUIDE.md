# 📄 wkhtmltopdf Installation Guide for Odoo PDF Reports

## 🎯 Why You Need This
Your IFRS Financial Statements module needs `wkhtmltopdf` to generate professional PDF reports. Without it, reports will only show in HTML format.

## 🔧 Installation Methods

### Method 1: Manual Download (Recommended)

#### Step 1: Download
- Go to: https://wkhtmltopdf.org/downloads.html
- Download: **wkhtmltox-0.12.6-1.msvc2015-win64.exe**
- Or direct link: https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.msvc2015-win64.exe

#### Step 2: Install
1. **Right-click** the downloaded file
2. **Select "Run as administrator"**
3. **Follow the installation wizard**
4. **Install to default location**: `C:\Program Files\wkhtmltopdf`

#### Step 3: Add to System PATH
**Option A: Using Command Prompt (Recommended)**
1. **Open Command Prompt as Administrator**
2. **Run this command**:
   ```cmd
   setx PATH "%PATH%;C:\Program Files\wkhtmltopdf\bin" /M
   ```

**Option B: Using System Properties**
1. **Press Win + R**, type `sysdm.cpl`, press Enter
2. **Click "Environment Variables"**
3. **Under "System Variables"**, find and select **"Path"**
4. **Click "Edit"**
5. **Click "New"**
6. **Add**: `C:\Program Files\wkhtmltopdf\bin`
7. **Click "OK"** on all dialogs

#### Step 4: Verify Installation
1. **Open a NEW Command Prompt**
2. **Run**: `wkhtmltopdf --version`
3. **Should show version information**

#### Step 5: Restart Odoo
1. **Stop Odoo** (Ctrl+C in terminal)
2. **Start Odoo**: `py -3.13 odoo-bin --config=odoo.conf`
3. **Test PDF generation**

### Method 2: Using Chocolatey (If Available)

If you have Chocolatey installed:
```powershell
# Run PowerShell as Administrator
choco install wkhtmltopdf -y
```

### Method 3: Portable Installation

If you can't install system-wide:
1. **Download the installer**
2. **Extract using 7-Zip** (don't run installer)
3. **Copy `wkhtmltopdf.exe`** to your Odoo directory
4. **Update Odoo config** to point to the executable

## 🧪 Testing PDF Generation

After installation:

1. **Access your IFRS module**
2. **Create a financial statement**
3. **Click "Generate Statement"**
4. **Click "Export PDF"**
5. **Should generate proper PDF instead of HTML**

## 🔍 Troubleshooting

### Issue: "wkhtmltopdf not found"
**Solution**: Check PATH environment variable

### Issue: "Access denied"
**Solution**: Run installation as Administrator

### Issue: "Still showing HTML"
**Solution**: Restart Odoo server completely

### Issue: "Command not found"
**Solution**: Open NEW command prompt after PATH update

## 🎯 Quick Verification Commands

```cmd
# Check if wkhtmltopdf is accessible
wkhtmltopdf --version

# Check PATH variable
echo %PATH%

# Test basic PDF generation
wkhtmltopdf http://google.com test.pdf
```

## 📋 What You'll Get After Installation

✅ **Professional PDF Reports** - Instead of HTML
✅ **Proper Formatting** - Corporate-ready documents
✅ **Print-Ready Output** - For regulatory filing
✅ **Consistent Layout** - Across different systems

## 🚀 Alternative: Use HTML Reports (Temporary)

While installing wkhtmltopdf:
1. **HTML reports work fine** for viewing
2. **Use browser's "Print to PDF"** for PDF conversion
3. **Copy/paste content** to Word/Excel if needed

---

**Once wkhtmltopdf is installed, your IFRS Financial Statements module will generate beautiful, professional PDF reports perfect for regulatory filing and stakeholder distribution!** 📊✨
