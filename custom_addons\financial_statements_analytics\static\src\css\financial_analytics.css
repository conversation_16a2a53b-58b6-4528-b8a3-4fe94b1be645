/* Financial Analytics Styles */

/* Dashboard KPI Boxes */
.kpi-box {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.kpi-box:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.kpi-box h4 {
    color: #6c757d;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.kpi-box h2 {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
}

/* Financial Statement Lines Styling */
.financial-header {
    font-weight: bold;
    font-size: 16px;
    background-color: #f8f9fa;
    border-top: 2px solid #007bff;
}

.financial-total {
    font-weight: bold;
    border-top: 2px solid #000;
    border-bottom: 1px solid #000;
    background-color: #e9ecef;
}

.financial-subtotal {
    font-weight: 600;
    border-top: 1px solid #6c757d;
    background-color: #f8f9fa;
}

/* Indentation Levels */
.indent-level-0 { padding-left: 0px; }
.indent-level-1 { padding-left: 20px; }
.indent-level-2 { padding-left: 40px; }
.indent-level-3 { padding-left: 60px; }
.indent-level-4 { padding-left: 80px; }

/* Balance Sheet Professional Styling */
.balance-sheet-container {
    font-family: 'Arial', sans-serif;
    line-height: 1.4;
}

.balance-sheet-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #000;
    padding-bottom: 15px;
}

.balance-sheet-section {
    margin-bottom: 25px;
}

.balance-sheet-line {
    display: flex;
    justify-content: space-between;
    padding: 3px 0;
    border-bottom: 1px solid #eee;
}

.balance-sheet-line.header {
    font-weight: bold;
    font-size: 14px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ccc;
}

.balance-sheet-line.total {
    font-weight: bold;
    border-top: 2px solid #000;
    border-bottom: 1px solid #000;
    background-color: #e9ecef;
}

.balance-sheet-amount {
    text-align: right;
    min-width: 120px;
    font-family: 'Courier New', monospace;
}

/* Ratio Analysis Cards */
.ratio-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 15px 0;
    overflow: hidden;
}

.ratio-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
}

.ratio-card-body {
    padding: 20px;
}

.ratio-value {
    font-size: 24px;
    font-weight: 700;
    margin: 10px 0;
}

.ratio-interpretation {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
}

.ratio-interpretation.excellent {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ratio-interpretation.good {
    background-color: #cce7ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.ratio-interpretation.needs_attention {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.ratio-interpretation.poor {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
}

.chart-title {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin: 20px 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.dashboard-card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    font-size: 18px;
    font-weight: 600;
}

.dashboard-card-body {
    padding: 25px;
}

/* Trend Indicators */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.trend-indicator.improving {
    background-color: #d4edda;
    color: #155724;
}

.trend-indicator.improving::before {
    content: "↗";
    margin-right: 4px;
    font-size: 14px;
}

.trend-indicator.stable {
    background-color: #fff3cd;
    color: #856404;
}

.trend-indicator.stable::before {
    content: "→";
    margin-right: 4px;
    font-size: 14px;
}

.trend-indicator.declining {
    background-color: #f8d7da;
    color: #721c24;
}

.trend-indicator.declining::before {
    content: "↘";
    margin-right: 4px;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .kpi-box {
        margin: 5px 0;
        padding: 15px;
    }
    
    .kpi-box h2 {
        font-size: 22px;
    }
    
    .chart-container {
        height: 250px;
        padding: 15px;
    }
    
    .dashboard-card-header {
        padding: 15px;
        font-size: 16px;
    }
    
    .dashboard-card-body {
        padding: 20px;
    }
}

/* Print Styles */
@media print {
    .kpi-box,
    .dashboard-card,
    .ratio-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .chart-container {
        page-break-inside: avoid;
    }
    
    .financial-header {
        background-color: #f0f0f0 !important;
    }
    
    .financial-total {
        background-color: #e0e0e0 !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
.financial-statement-container::-webkit-scrollbar {
    width: 8px;
}

.financial-statement-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.financial-statement-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.financial-statement-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
