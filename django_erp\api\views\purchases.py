from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

# Placeholder for purchase API views
# TODO: Implement full purchase API

class PurchaseOrderViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class PurchaseOrderLineViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None
