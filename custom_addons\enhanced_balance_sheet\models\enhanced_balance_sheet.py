# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, date
from dateutil.relativedelta import relativedelta


class EnhancedBalanceSheet(models.Model):
    _name = 'enhanced.balance.sheet'
    _description = 'Enhanced Balance Sheet Report'
    _order = 'date_to desc, id desc'

    name = fields.Char(string='Report Name', required=True, default='Balance Sheet')
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        required=True,
        default=lambda self: self.env.company
    )
    
    currency_id = fields.Many2one(
        related='company_id.currency_id',
        string='Currency',
        readonly=True
    )
    
    date_from = fields.Date(
        string='Start Date',
        required=True,
        default=lambda self: fields.Date.today().replace(month=1, day=1)
    )
    
    date_to = fields.Date(
        string='End Date',
        required=True,
        default=fields.Date.today
    )
    
    # Comparative Period
    comparative_period = fields.Boolean(
        string='Show Comparative Period',
        default=False
    )
    
    comparative_date_from = fields.Date(string='Comparative Start Date')
    comparative_date_to = fields.Date(string='Comparative End Date')
    
    # Report Options
    show_zero_balance = fields.Boolean(
        string='Show Zero Balance Accounts',
        default=False
    )
    
    target_move = fields.Selection([
        ('posted', 'All Posted Entries'),
        ('all', 'All Entries'),
    ], string='Target Moves', required=True, default='posted')
    
    # Report Lines
    line_ids = fields.One2many(
        'enhanced.balance.sheet.line',
        'balance_sheet_id',
        string='Balance Sheet Lines'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generated', 'Generated'),
    ], string='State', default='draft')
    
    # Totals for verification
    total_assets = fields.Monetary(
        string='Total Assets',
        currency_field='currency_id',
        compute='_compute_totals',
        store=True
    )
    
    total_liabilities = fields.Monetary(
        string='Total Liabilities',
        currency_field='currency_id',
        compute='_compute_totals',
        store=True
    )
    
    total_equity = fields.Monetary(
        string='Total Equity',
        currency_field='currency_id',
        compute='_compute_totals',
        store=True
    )
    
    balance_check = fields.Monetary(
        string='Balance Check (Should be 0)',
        currency_field='currency_id',
        compute='_compute_totals',
        store=True
    )
    
    @api.depends('line_ids.current_amount')
    def _compute_totals(self):
        for record in self:
            assets_lines = record.line_ids.filtered(lambda l: l.section == 'assets' and l.line_type == 'total')
            liabilities_lines = record.line_ids.filtered(lambda l: l.section == 'liabilities' and l.line_type == 'total')
            equity_lines = record.line_ids.filtered(lambda l: l.section == 'equity' and l.line_type == 'total')
            
            record.total_assets = sum(assets_lines.mapped('current_amount'))
            record.total_liabilities = sum(liabilities_lines.mapped('current_amount'))
            record.total_equity = sum(equity_lines.mapped('current_amount'))
            record.balance_check = record.total_assets - (record.total_liabilities + record.total_equity)
    
    @api.onchange('comparative_period')
    def _onchange_comparative_period(self):
        if self.comparative_period and not self.comparative_date_to:
            # Set comparative period to previous year
            if self.date_to:
                self.comparative_date_to = self.date_to - relativedelta(years=1)
                self.comparative_date_from = self.date_from - relativedelta(years=1) if self.date_from else None
    
    def action_generate_report(self):
        """Generate the balance sheet report"""
        self.ensure_one()
        
        # Clear existing lines
        self.line_ids.unlink()
        
        # Generate new lines
        self._generate_balance_sheet_structure()
        self._populate_balance_sheet_data()
        
        self.state = 'generated'
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Balance Sheet Report'),
            'res_model': 'enhanced.balance.sheet',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def _generate_balance_sheet_structure(self):
        """Generate the balance sheet structure"""
        lines = [
            # ASSETS
            {'name': 'ASSETS', 'line_type': 'header', 'section': 'assets', 'sequence': 10, 'bold': True, 'underline': True},
            
            # Current Assets
            {'name': 'Current Assets', 'line_type': 'subtotal', 'section': 'assets', 'sequence': 20, 'bold': True},
            {'name': 'Cash and Cash Equivalents', 'line_type': 'line', 'section': 'assets', 'sequence': 30, 'account_types': ['asset_cash']},
            {'name': 'Trade and Other Receivables', 'line_type': 'line', 'section': 'assets', 'sequence': 40, 'account_types': ['asset_receivable']},
            {'name': 'Inventories', 'line_type': 'line', 'section': 'assets', 'sequence': 50, 'account_types': ['asset_current']},
            {'name': 'Prepayments', 'line_type': 'line', 'section': 'assets', 'sequence': 60, 'account_types': ['asset_prepayments']},
            {'name': 'Total Current Assets', 'line_type': 'total', 'section': 'assets', 'sequence': 70, 'bold': True},
            
            # Non-Current Assets
            {'name': 'Non-Current Assets', 'line_type': 'subtotal', 'section': 'assets', 'sequence': 80, 'bold': True},
            {'name': 'Property, Plant and Equipment', 'line_type': 'line', 'section': 'assets', 'sequence': 90, 'account_types': ['asset_fixed']},
            {'name': 'Intangible Assets', 'line_type': 'line', 'section': 'assets', 'sequence': 100, 'account_types': ['asset_non_current']},
            {'name': 'Total Non-Current Assets', 'line_type': 'total', 'section': 'assets', 'sequence': 110, 'bold': True},
            
            {'name': 'TOTAL ASSETS', 'line_type': 'total', 'section': 'assets', 'sequence': 120, 'bold': True, 'underline': True},

            # LIABILITIES
            {'name': 'LIABILITIES', 'line_type': 'header', 'section': 'liabilities', 'sequence': 200, 'bold': True, 'underline': True},

            # Current Liabilities
            {'name': 'Current Liabilities', 'line_type': 'subtotal', 'section': 'liabilities', 'sequence': 210, 'bold': True},
            {'name': 'Trade and Other Payables', 'line_type': 'line', 'section': 'liabilities', 'sequence': 220, 'account_types': ['liability_payable']},
            {'name': 'Short-term Borrowings', 'line_type': 'line', 'section': 'liabilities', 'sequence': 230, 'account_types': ['liability_current']},
            {'name': 'Current Tax Liabilities', 'line_type': 'line', 'section': 'liabilities', 'sequence': 240, 'account_types': ['liability_current']},
            {'name': 'Total Current Liabilities', 'line_type': 'total', 'section': 'liabilities', 'sequence': 250, 'bold': True},

            # Non-Current Liabilities
            {'name': 'Non-Current Liabilities', 'line_type': 'subtotal', 'section': 'liabilities', 'sequence': 260, 'bold': True},
            {'name': 'Long-term Borrowings', 'line_type': 'line', 'section': 'liabilities', 'sequence': 270, 'account_types': ['liability_non_current']},
            {'name': 'Deferred Tax Liabilities', 'line_type': 'line', 'section': 'liabilities', 'sequence': 280, 'account_types': ['liability_non_current']},
            {'name': 'Total Non-Current Liabilities', 'line_type': 'total', 'section': 'liabilities', 'sequence': 290, 'bold': True},

            {'name': 'TOTAL LIABILITIES', 'line_type': 'total', 'section': 'liabilities', 'sequence': 300, 'bold': True, 'underline': True},

            # EQUITY (Separate Section)
            {'name': 'EQUITY', 'line_type': 'header', 'section': 'equity', 'sequence': 400, 'bold': True, 'underline': True},
            {'name': 'Share Capital', 'line_type': 'line', 'section': 'equity', 'sequence': 410, 'account_types': ['equity']},
            {'name': 'Retained Earnings', 'line_type': 'line', 'section': 'equity', 'sequence': 420, 'account_types': ['equity_unaffected']},
            {'name': 'Other Reserves', 'line_type': 'line', 'section': 'equity', 'sequence': 430, 'account_types': ['equity']},
            {'name': 'TOTAL EQUITY', 'line_type': 'total', 'section': 'equity', 'sequence': 440, 'bold': True, 'underline': True},

            {'name': 'TOTAL LIABILITIES AND EQUITY', 'line_type': 'total', 'section': 'total', 'sequence': 500, 'bold': True, 'underline': True},
        ]

        # Create lines
        for line_data in lines:
            line_data['balance_sheet_id'] = self.id
            # Convert account_types list to string for storage
            if 'account_types' in line_data:
                line_data['account_types'] = str(line_data['account_types'])
            self.env['enhanced.balance.sheet.line'].create(line_data)
    
    def _populate_balance_sheet_data(self):
        """Populate balance sheet with actual data"""
        for line in self.line_ids:
            if line.line_type == 'line' and line.account_types:
                line._compute_amounts()
            elif line.line_type in ['subtotal', 'total']:
                line._compute_totals()
    
    def action_print_report(self):
        """Print the balance sheet report"""
        return self.env.ref('enhanced_balance_sheet.action_report_balance_sheet').report_action(self)
    
    def action_export_excel(self):
        """Export balance sheet to Excel"""
        # This would be implemented with xlsxwriter or similar
        pass
