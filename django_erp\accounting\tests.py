from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from core.models import Company, Country, Currency, Partner
from .models import (
    AccountAccount, AccountJournal, AccountMove, AccountMoveLine,
    AccountTax, AccountGroup
)

class AccountingModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Company',
            code='TEST',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create partner
        self.partner = Partner.objects.create(
            name='Test Customer',
            customer_rank=1,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create account group
        self.account_group = AccountGroup.objects.create(
            name='Assets',
            code_prefix_start='1',
            code_prefix_end='1999',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create accounts
        self.cash_account = AccountAccount.objects.create(
            name='Cash',
            code='1001',
            account_type='asset_cash',
            company_id=self.company,
            group_id=self.account_group,
            create_uid=self.user,
            write_uid=self.user
        )

        self.receivable_account = AccountAccount.objects.create(
            name='Accounts Receivable',
            code='1200',
            account_type='asset_receivable',
            reconcile=True,
            company_id=self.company,
            group_id=self.account_group,
            create_uid=self.user,
            write_uid=self.user
        )

        self.revenue_account = AccountAccount.objects.create(
            name='Revenue',
            code='4000',
            account_type='income',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create journal
        self.sales_journal = AccountJournal.objects.create(
            name='Sales Journal',
            code='SAL',
            type='sale',
            company_id=self.company,
            default_account_id=self.revenue_account,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create tax
        self.sales_tax = AccountTax.objects.create(
            name='Sales Tax 10%',
            type_tax_use='sale',
            amount_type='percent',
            amount=Decimal('10.0'),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

    def test_account_creation(self):
        """Test account creation and validation"""
        # Test successful creation
        account = AccountAccount.objects.create(
            name='Test Account',
            code='9999',
            account_type='expense',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        self.assertEqual(str(account), '9999 Test Account')

        # Test receivable account must be reconcilable
        with self.assertRaises(ValidationError):
            account = AccountAccount(
                name='Bad Receivable',
                code='1299',
                account_type='asset_receivable',
                reconcile=False,
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            account.full_clean()

    def test_journal_entry_creation(self):
        """Test journal entry creation and posting"""
        # Create a simple journal entry
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            partner_id=self.partner,
            move_type='out_invoice',
            create_uid=self.user,
            write_uid=self.user
        )

        # Add lines
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.receivable_account,
            partner_id=self.partner,
            debit=Decimal('110.0'),
            name='Customer Invoice',
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            credit=Decimal('100.0'),
            name='Revenue',
            create_uid=self.user,
            write_uid=self.user
        )

        # Tax line
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,  # In real scenario, this would be tax account
            credit=Decimal('10.0'),
            name='Sales Tax',
            tax_line_id=self.sales_tax,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test that move is balanced
        self.assertTrue(move._check_balanced())

        # Test posting
        move.action_post()
        self.assertEqual(move.state, 'posted')
        self.assertTrue(move.posted_before)
        self.assertIsNotNone(move.name)

        # Test amounts computation
        self.assertEqual(move.amount_untaxed, Decimal('100.0'))
        self.assertEqual(move.amount_tax, Decimal('10.0'))
        self.assertEqual(move.amount_total, Decimal('110.0'))

    def test_move_line_validation(self):
        """Test move line validation"""
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test that line cannot have both debit and credit
        with self.assertRaises(ValidationError):
            line = AccountMoveLine(
                move_id=move,
                account_id=self.cash_account,
                debit=Decimal('100.0'),
                credit=Decimal('50.0'),
                name='Invalid Line',
                create_uid=self.user,
                write_uid=self.user
            )
            line.full_clean()

    def test_tax_creation(self):
        """Test tax creation and validation"""
        # Test unique constraint
        with self.assertRaises(ValidationError):
            duplicate_tax = AccountTax(
                name='Sales Tax 10%',
                type_tax_use='sale',
                tax_scope='consu',
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            duplicate_tax.full_clean()

    def test_account_code_validation(self):
        """Test account code validation"""
        with self.assertRaises(ValidationError):
            account = AccountAccount(
                name='Invalid Code Account',
                code='12@34',  # Invalid characters
                account_type='expense',
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            account.full_clean()
