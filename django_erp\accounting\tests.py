from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from core.models import Company, Country, Currency, Partner
from .models import (
    AccountAccount, AccountJournal, AccountMove, AccountMoveLine,
    AccountTax, AccountGroup, AccountPayment
)

class AccountingModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Company',
            code='TEST',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create partner
        self.partner = Partner.objects.create(
            name='Test Customer',
            customer_rank=1,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create account group
        self.account_group = AccountGroup.objects.create(
            name='Assets',
            code_prefix_start='1',
            code_prefix_end='1999',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create accounts
        self.cash_account = AccountAccount.objects.create(
            name='Cash',
            code='1001',
            account_type='asset_cash',
            company_id=self.company,
            group_id=self.account_group,
            create_uid=self.user,
            write_uid=self.user
        )

        self.receivable_account = AccountAccount.objects.create(
            name='Accounts Receivable',
            code='1200',
            account_type='asset_receivable',
            reconcile=True,
            company_id=self.company,
            group_id=self.account_group,
            create_uid=self.user,
            write_uid=self.user
        )

        self.revenue_account = AccountAccount.objects.create(
            name='Revenue',
            code='4000',
            account_type='income',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create journal
        self.sales_journal = AccountJournal.objects.create(
            name='Sales Journal',
            code='SAL',
            type='sale',
            company_id=self.company,
            default_account_id=self.revenue_account,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create tax
        self.sales_tax = AccountTax.objects.create(
            name='Sales Tax 10%',
            type_tax_use='sale',
            amount_type='percent',
            amount=Decimal('10.0'),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

    def test_account_creation(self):
        """Test account creation and validation"""
        # Test successful creation
        account = AccountAccount.objects.create(
            name='Test Account',
            code='9999',
            account_type='expense',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        self.assertEqual(str(account), '9999 Test Account')

        # Test receivable account must be reconcilable
        with self.assertRaises(ValidationError):
            account = AccountAccount(
                name='Bad Receivable',
                code='1299',
                account_type='asset_receivable',
                reconcile=False,
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            account.full_clean()

    def test_journal_entry_creation(self):
        """Test journal entry creation and posting"""
        # Create a simple journal entry
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            partner_id=self.partner,
            move_type='out_invoice',
            create_uid=self.user,
            write_uid=self.user
        )

        # Add lines
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.receivable_account,
            partner_id=self.partner,
            debit=Decimal('110.0'),
            name='Customer Invoice',
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            credit=Decimal('100.0'),
            name='Revenue',
            create_uid=self.user,
            write_uid=self.user
        )

        # Tax line
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,  # In real scenario, this would be tax account
            credit=Decimal('10.0'),
            name='Sales Tax',
            tax_line_id=self.sales_tax,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test that move is balanced
        self.assertTrue(move._check_balanced())

        # Test posting
        move.action_post()
        self.assertEqual(move.state, 'posted')
        self.assertTrue(move.posted_before)
        self.assertIsNotNone(move.name)

        # Test amounts computation
        self.assertEqual(move.amount_untaxed, Decimal('100.0'))
        self.assertEqual(move.amount_tax, Decimal('10.0'))
        self.assertEqual(move.amount_total, Decimal('110.0'))

    def test_move_line_validation(self):
        """Test move line validation"""
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test that line cannot have both debit and credit
        with self.assertRaises(ValidationError):
            line = AccountMoveLine(
                move_id=move,
                account_id=self.cash_account,
                debit=Decimal('100.0'),
                credit=Decimal('50.0'),
                name='Invalid Line',
                create_uid=self.user,
                write_uid=self.user
            )
            line.full_clean()

    def test_tax_creation(self):
        """Test tax creation and validation"""
        # Test unique constraint
        with self.assertRaises(ValidationError):
            duplicate_tax = AccountTax(
                name='Sales Tax 10%',
                type_tax_use='sale',
                tax_scope='consu',
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            duplicate_tax.full_clean()

    def test_account_code_validation(self):
        """Test account code validation"""
        with self.assertRaises(ValidationError):
            account = AccountAccount(
                name='Invalid Code Account',
                code='12@34',  # Invalid characters
                account_type='expense',
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            account.full_clean()

    def test_move_unpost_validation(self):
        """Test move unpost validation"""
        # Create and post a move
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add lines
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.receivable_account,
            name='Test Debit',
            debit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            name='Test Credit',
            credit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Post the move
        move.action_post()

        # Should be able to reset to draft
        move.button_draft()
        self.assertEqual(move.state, 'draft')

        # Test that move without posted_before cannot be reset
        new_move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        with self.assertRaises(ValidationError):
            new_move.button_draft()

    def test_move_reversal(self):
        """Test move reversal functionality"""
        # Create and post a move
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add lines
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.receivable_account,
            name='Original Debit',
            debit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            name='Original Credit',
            credit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Post the move
        move.action_post()

        # Reverse the move
        reverse_move = move._reverse_moves()

        # Check reverse move
        self.assertEqual(reverse_move.state, 'posted')
        self.assertTrue(reverse_move.ref.startswith('Reversal of:'))

        # Check reverse lines
        reverse_lines = reverse_move.line_ids.all()
        self.assertEqual(len(reverse_lines), 2)

        # Original debit should become credit in reverse
        debit_line = reverse_lines.filter(account_id=self.receivable_account).first()
        self.assertEqual(debit_line.credit, Decimal('100.0'))
        self.assertEqual(debit_line.debit, Decimal('0.0'))

    def test_move_deletion_validation(self):
        """Test move deletion validation"""
        # Create and post a move
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create balanced journal entry (debit + credit)
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.receivable_account,
            name='Test Debit Line',
            debit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            name='Test Credit Line',
            credit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Post the move
        move.action_post()

        # Should not be able to delete posted move
        with self.assertRaises(ValidationError):
            move.delete()

    def test_reconciliation_validation(self):
        """Test reconciliation validation rules"""
        # Create move with non-reconcilable account
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        line = AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,  # Non-reconcilable
            name='Revenue Line',
            credit=Decimal('100.0'),
            date=timezone.now().date(),  # Required field
            create_uid=self.user,
            write_uid=self.user
        )

        # Should not be able to reconcile non-reconcilable account
        with self.assertRaises(ValidationError):
            line.reconcile()

    def test_hash_integrity(self):
        """Test hash integrity for posted moves"""
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check hash integrity
        self.assertTrue(move._check_hash_integrity())

    def test_enhanced_invoice_posting(self):
        """Test enhanced invoice posting logic"""
        # Create a customer invoice
        invoice = AccountMove.objects.create(
            journal_id=self.sales_journal,
            move_type='out_invoice',
            partner_id=self.partner,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add invoice lines
        AccountMoveLine.objects.create(
            move_id=invoice,
            account_id=self.revenue_account,
            name='Product Sale',
            credit=Decimal('1000.00'),
            debit=Decimal('0.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=invoice,
            account_id=self.receivable_account,
            partner_id=self.partner,
            name='Customer Invoice',
            debit=Decimal('1000.00'),
            credit=Decimal('0.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Test posting
        self.assertEqual(invoice.state, 'draft')
        invoice.action_post()
        self.assertEqual(invoice.state, 'posted')
        self.assertTrue(invoice.posted_before)
        self.assertIsNotNone(invoice.name)

        # Test validation methods were called
        self.assertTrue(invoice._check_balanced())

    def test_posting_validations(self):
        """Test posting validation logic"""
        # Create an unbalanced move
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add unbalanced lines
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            name='Unbalanced line',
            credit=Decimal('1000.00'),
            debit=Decimal('0.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Should fail to post
        with self.assertRaises(ValidationError):
            move.action_post()

    def test_duplicate_posting_prevention(self):
        """Test that already posted moves cannot be posted again"""
        move = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add balanced lines
        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.revenue_account,
            name='Test line',
            credit=Decimal('500.00'),
            debit=Decimal('0.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move,
            account_id=self.receivable_account,
            name='Test line 2',
            debit=Decimal('500.00'),
            credit=Decimal('0.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Post the move
        move.action_post()
        self.assertEqual(move.state, 'posted')

        # Try to post again - should fail
        with self.assertRaises(ValidationError):
            move.action_post()

    def test_payment_creation_and_posting(self):
        """Test payment creation and posting"""
        # Create a customer payment
        payment = AccountPayment.objects.create(
            payment_type='inbound',
            partner_type='customer',
            partner_id=self.partner,
            amount=Decimal('500.00'),
            currency_id=self.currency,
            journal_id=self.sales_journal,
            destination_account_id=self.receivable_account,
            company_id=self.company,
            ref='Customer Payment',
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertEqual(payment.state, 'draft')
        self.assertIsNone(payment.move_id)

        # Post the payment
        payment.action_post()

        # Test posted state
        self.assertEqual(payment.state, 'posted')
        self.assertIsNotNone(payment.move_id)
        self.assertEqual(payment.move_id.state, 'posted')

        # Test journal entry lines
        lines = payment.move_id.line_ids.all()
        self.assertEqual(len(lines), 2)

        # Check debit line (bank account)
        debit_line = lines.filter(debit__gt=0).first()
        self.assertEqual(debit_line.account_id, self.sales_journal.default_account_id)
        self.assertEqual(debit_line.debit, Decimal('500.00'))

        # Check credit line (receivable account)
        credit_line = lines.filter(credit__gt=0).first()
        self.assertEqual(credit_line.account_id, self.receivable_account)
        self.assertEqual(credit_line.credit, Decimal('500.00'))

    def test_payment_reconciliation(self):
        """Test payment reconciliation with invoices"""
        # Create an invoice first
        invoice = AccountMove.objects.create(
            journal_id=self.sales_journal,
            move_type='out_invoice',
            partner_id=self.partner,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add invoice lines
        AccountMoveLine.objects.create(
            move_id=invoice,
            account_id=self.revenue_account,
            name='Product Sale',
            credit=Decimal('500.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=invoice,
            account_id=self.receivable_account,
            partner_id=self.partner,
            name='Customer Invoice',
            debit=Decimal('500.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Post the invoice
        invoice.action_post()

        # Create a payment for the same amount
        payment = AccountPayment.objects.create(
            payment_type='inbound',
            partner_type='customer',
            partner_id=self.partner,
            amount=Decimal('500.00'),
            currency_id=self.currency,
            journal_id=self.sales_journal,
            destination_account_id=self.receivable_account,
            company_id=self.company,
            ref='Customer Payment',
            create_uid=self.user,
            write_uid=self.user
        )

        # Post the payment (should auto-reconcile)
        payment.action_post()

        # Check reconciliation
        self.assertIn(invoice, payment.reconciled_invoice_ids.all())

    def test_line_reconciliation(self):
        """Test manual line reconciliation"""
        # Create two moves that should reconcile
        move1 = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        line1 = AccountMoveLine.objects.create(
            move_id=move1,
            account_id=self.receivable_account,
            partner_id=self.partner,
            name='Debit Line',
            debit=Decimal('100.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move1,
            account_id=self.revenue_account,
            name='Credit Line',
            credit=Decimal('100.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        move2 = AccountMove.objects.create(
            journal_id=self.sales_journal,
            date=timezone.now().date(),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        line2 = AccountMoveLine.objects.create(
            move_id=move2,
            account_id=self.receivable_account,
            partner_id=self.partner,
            name='Credit Line',
            credit=Decimal('100.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        AccountMoveLine.objects.create(
            move_id=move2,
            account_id=self.revenue_account,
            name='Debit Line',
            debit=Decimal('100.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Post both moves
        move1.action_post()
        move2.action_post()

        # Reconcile the lines
        lines_to_reconcile = AccountMoveLine.objects.filter(id__in=[line1.id, line2.id])
        lines_to_reconcile.reconcile()

        # Check reconciliation
        line1.refresh_from_db()
        line2.refresh_from_db()
        self.assertTrue(line1.reconciled)
        self.assertTrue(line2.reconciled)
        self.assertEqual(line1.full_reconcile_id, line2.full_reconcile_id)

    def test_invoice_approval_workflow(self):
        """Test invoice approval workflow"""
        # Create invoice
        invoice = AccountMove.objects.create(
            move_type='out_invoice',
            partner_id=self.partner,
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertEqual(invoice.approval_status, 'draft')

        # Request approval
        invoice.action_request_approval()
        self.assertEqual(invoice.approval_status, 'pending')

        # Approve invoice
        invoice.action_approve(self.user)
        self.assertEqual(invoice.approval_status, 'approved')
        self.assertEqual(invoice.approved_by, self.user)
        self.assertIsNotNone(invoice.approved_date)

        # Test rejection workflow
        rejection_invoice = AccountMove.objects.create(
            move_type='out_invoice',
            partner_id=self.partner,
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        rejection_invoice.action_request_approval()
        rejection_invoice.action_reject(self.user, "Incorrect amount")
        self.assertEqual(rejection_invoice.approval_status, 'rejected')

    def test_invoice_sent_tracking(self):
        """Test invoice sent tracking"""
        # Create invoice
        invoice = AccountMove.objects.create(
            move_type='out_invoice',
            partner_id=self.partner,
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertFalse(invoice.invoice_sent)
        self.assertIsNone(invoice.invoice_sent_date)

        # Mark as sent
        invoice.action_invoice_sent()
        self.assertTrue(invoice.invoice_sent)
        self.assertIsNotNone(invoice.invoice_sent_date)

    def test_recurring_invoice_creation(self):
        """Test recurring invoice creation"""
        # Create recurring invoice template
        recurring_invoice = AccountMove.objects.create(
            move_type='out_invoice',
            partner_id=self.partner,
            journal_id=self.sales_journal,
            company_id=self.company,
            currency_id=self.currency,
            is_recurring=True,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add invoice line
        AccountMoveLine.objects.create(
            move_id=recurring_invoice,
            account_id=self.receivable_account,  # Use existing account
            name='Recurring Service',
            debit=0,
            credit=100.0,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create recurring copy
        new_invoice = recurring_invoice.create_recurring_invoice()

        # Test that new invoice was created
        self.assertIsNotNone(new_invoice)
        self.assertNotEqual(new_invoice.id, recurring_invoice.id)
        self.assertEqual(new_invoice.recurring_source_id, recurring_invoice)
        self.assertEqual(new_invoice.partner_id, recurring_invoice.partner_id)
        self.assertEqual(new_invoice.line_ids.count(), recurring_invoice.line_ids.count())

    def test_payment_provider_creation(self):
        """Test payment provider creation"""
        from accounting.models import PaymentProvider

        provider = PaymentProvider.objects.create(
            name='Test PayPal',
            code='paypal',
            state='test',
            company_id=self.company,
            is_published=True,
            maximum_amount=10000.0,
            api_key='test_key',
            api_secret='test_secret',
            create_uid=self.user,
            write_uid=self.user
        )

        self.assertEqual(provider.name, 'Test PayPal')
        self.assertEqual(provider.code, 'paypal')
        self.assertEqual(provider.state, 'test')
        self.assertTrue(provider.is_published)

    def test_payment_transaction_workflow(self):
        """Test payment transaction workflow"""
        from accounting.models import PaymentProvider, PaymentTransaction

        # Create payment provider
        provider = PaymentProvider.objects.create(
            name='Test Stripe',
            code='stripe',
            state='enabled',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create payment transaction
        transaction = PaymentTransaction.objects.create(
            reference='TXN-001',
            amount=500.0,
            currency_id=self.currency,
            provider_id=provider,
            partner_id=self.partner,
            partner_name=self.partner.name,
            partner_email='<EMAIL>',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertEqual(transaction.state, 'draft')
        self.assertIsNone(transaction.payment_id)

        # Simulate payment confirmation
        transaction.state = 'pending'
        transaction.save()

        # Confirm transaction
        transaction.action_confirm()

        # Test final state
        self.assertEqual(transaction.state, 'done')
        self.assertIsNotNone(transaction.payment_id)

        # Test that payment was created
        payment = transaction.payment_id
        self.assertEqual(payment.amount, transaction.amount)
        self.assertEqual(payment.partner_id, transaction.partner_id)
