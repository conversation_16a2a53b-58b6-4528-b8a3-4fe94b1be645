<?xml version="1.0" encoding="utf-8" ?>
<odoo>

    <record id="action_account_reconciliation" model="ir.actions.server">
		<field name="name">Reconcile</field>
		<field name="model_id" ref="account.model_account_move_line"/>
		<field name="binding_model_id" ref="account.model_account_move_line"/>
		<field name="state">code</field>
		<field name="code">action = records.reconcile()</field>
		<field name="groups_id" eval="[(4, ref('account.group_account_user'))]" />
	</record>

</odoo>
