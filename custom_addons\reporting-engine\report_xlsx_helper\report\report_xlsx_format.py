# Global Formatting
XLS_HEADERS = {"xls_headers": "", "xls_footers": ""}
FORMATS = {
    "format_ws_title": False,
    "format_left": False,
    "format_center": False,
    "format_right": False,
    "format_amount_left": False,
    "format_amount_center": False,
    "format_amount_right": False,
    "format_amount_conditional_left": False,
    "format_amount_conditional_center": False,
    "format_amount_conditional_right": False,
    "format_percent_left": False,
    "format_percent_center": False,
    "format_percent_right": False,
    "format_percent_conditional_left": False,
    "format_percent_conditional_center": False,
    "format_percent_conditional_right": False,
    "format_integer_left": False,
    "format_integer_center": False,
    "format_integer_right": False,
    "format_integer_conditional_left": False,
    "format_integer_conditional_center": False,
    "format_integer_conditional_right": False,
    "format_date_left": False,
    "format_date_center": False,
    "format_date_right": False,
    "format_left_bold": False,
    "format_center_bold": False,
    "format_right_bold": False,
    "format_amount_left_bold": False,
    "format_amount_center_bold": False,
    "format_amount_right_bold": False,
    "format_amount_conditional_left_bold": False,
    "format_amount_conditional_center_bold": False,
    "format_amount_conditional_right_bold": False,
    "format_percent_left_bold": False,
    "format_percent_center_bold": False,
    "format_percent_right_bold": False,
    "format_percent_conditional_left_bold": False,
    "format_percent_conditional_center_bold": False,
    "format_percent_conditional_right_bold": False,
    "format_integer_left_bold": False,
    "format_integer_center_bold": False,
    "format_integer_right_bold": False,
    "format_integer_conditional_left_bold": False,
    "format_integer_conditional_center_bold": False,
    "format_integer_conditional_right_bold": False,
    "format_date_left_bold": False,
    "format_date_center_bold": False,
    "format_date_right_bold": False,
    "format_theader_grey_left": False,
    "format_theader_grey_center": False,
    "format_theader_grey_right": False,
    "format_theader_grey_amount_left": False,
    "format_theader_grey_amount_center": False,
    "format_theader_grey_amount_right": False,
    "format_theader_grey_amount_conditional_left": False,
    "format_theader_grey_amount_conditional_center": False,
    "format_theader_grey_amount_conditional_right": False,
    "format_theader_grey_percent_left": False,
    "format_theader_grey_percent_center": False,
    "format_theader_grey_percent_right": False,
    "format_theader_grey_percent_conditional_left": False,
    "format_theader_grey_percent_conditional_center": False,
    "format_theader_grey_percent_conditional_right": False,
    "format_theader_grey_integer_left": False,
    "format_theader_grey_integer_center": False,
    "format_theader_grey_integer_right": False,
    "format_theader_grey_integer_conditional_left": False,
    "format_theader_grey_integer_conditional_center": False,
    "format_theader_grey_integer_conditional_right": False,
    "format_theader_yellow_left": False,
    "format_theader_yellow_center": False,
    "format_theader_yellow_right": False,
    "format_theader_yellow_amount_left": False,
    "format_theader_yellow_amount_center": False,
    "format_theader_yellow_amount_right": False,
    "format_theader_yellow_amount_conditional_left": False,
    "format_theader_yellow_amount_conditional_center": False,
    "format_theader_yellow_amount_conditional_right": False,
    "format_theader_yellow_percent_left": False,
    "format_theader_yellow_percent_center": False,
    "format_theader_yellow_percent_right": False,
    "format_theader_yellow_percent_conditional_left": False,
    "format_theader_yellow_percent_conditional_center": False,
    "format_theader_yellow_percent_conditional_right": False,
    "format_theader_yellow_integer_left": False,
    "format_theader_yellow_integer_center": False,
    "format_theader_yellow_integer_right": False,
    "format_theader_yellow_integer_conditional_left": False,
    "format_theader_yellow_integer_conditional_center": False,
    "format_theader_yellow_integer_conditional_right": False,
    "format_theader_blue_left": False,
    "format_theader_blue_center": False,
    "format_theader_blue_right": False,
    "format_theader_blue_amount_left": False,
    "format_theader_blue_amount_center": False,
    "format_theader_blue_amount_right": False,
    "format_theader_blue_amount_conditional_left": False,
    "format_theader_blue_amount_conditional_center": False,
    "format_theader_blue_amount_conditional_right": False,
    "format_theader_blue_percent_left": False,
    "format_theader_blue_percent_center": False,
    "format_theader_blue_percent_right": False,
    "format_theader_blue_percent_conditional_left": False,
    "format_theader_blue_percent_conditional_center": False,
    "format_theader_blue_percent_conditional_right": False,
    "format_theader_blue_integer_left": False,
    "format_theader_blue_integer_center": False,
    "format_theader_blue_integer_right": False,
    "format_theader_blue_integer_conditional_left": False,
    "format_theader_blue_integer_conditional_center": False,
    "format_theader_blue_integer_conditional_right": False,
    "format_tcell_left": False,
    "format_tcell_center": False,
    "format_tcell_right": False,
    "format_tcell_amount_left": False,
    "format_tcell_amount_center": False,
    "format_tcell_amount_right": False,
    "format_tcell_amount_conditional_left": False,
    "format_tcell_amount_conditional_center": False,
    "format_tcell_amount_conditional_right": False,
    "format_tcell_percent_left": False,
    "format_tcell_percent_center": False,
    "format_tcell_percent_right": False,
    "format_tcell_percent_conditional_left": False,
    "format_tcell_percent_conditional_center": False,
    "format_tcell_percent_conditional_right": False,
    "format_tcell_integer_left": False,
    "format_tcell_integer_center": False,
    "format_tcell_integer_right": False,
    "format_tcell_integer_conditional_left": False,
    "format_tcell_integer_conditional_center": False,
    "format_tcell_integer_conditional_right": False,
    "format_tcell_date_left": False,
    "format_tcell_date_center": False,
    "format_tcell_date_right": False,
    "format_tcell_left_bold": False,
    "format_tcell_center_bold": False,
    "format_tcell_right_bold": False,
    "format_tcell_amount_left_bold": False,
    "format_tcell_amount_center_bold": False,
    "format_tcell_amount_right_bold": False,
    "format_tcell_amount_conditional_left_bold": False,
    "format_tcell_amount_conditional_center_bold": False,
    "format_tcell_amount_conditional_right_bold": False,
    "format_tcell_percent_left_bold": False,
    "format_tcell_percent_center_bold": False,
    "format_tcell_percent_right_bold": False,
    "format_tcell_percent_conditional_left_bold": False,
    "format_tcell_percent_conditional_center_bold": False,
    "format_tcell_percent_conditional_right_bold": False,
    "format_tcell_integer_left_bold": False,
    "format_tcell_integer_center_bold": False,
    "format_tcell_integer_right_bold": False,
    "format_tcell_integer_conditional_left_bold": False,
    "format_tcell_integer_conditional_center_bold": False,
    "format_tcell_integer_conditional_right_bold": False,
    "format_tcell_date_left_bold": False,
    "format_tcell_date_center_bold": False,
    "format_tcell_date_right_bold": False,
}
