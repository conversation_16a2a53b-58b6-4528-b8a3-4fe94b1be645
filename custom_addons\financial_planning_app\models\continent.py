# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class Continent(models.Model):
    _name = 'financial.planning.continent'
    _description = 'Continent for Financial Planning'
    _order = 'name'

    name = fields.Char(string='Continent Name', required=True, translate=True)
    code = fields.Char(string='Continent Code', size=2, required=True)
    population = fields.Float(string='Total Population', help='Total population in millions')
    area_km2 = fields.Float(string='Area (km²)', help='Total area in square kilometers')
    
    # Relationships
    country_ids = fields.One2many('financial.planning.country', 'continent_id', string='Countries')
    
    # Computed fields
    country_count = fields.Integer(string='Number of Countries', compute='_compute_country_count', store=True)
    total_gdp = fields.Monetary(string='Total GDP', compute='_compute_total_gdp', currency_field='currency_id')
    currency_id = fields.Many2one('res.currency', string='Display Currency', default=lambda self: self.env.company.currency_id)
    
    # Financial planning fields
    growth_potential = fields.Selection([
        ('low', 'Low Growth Potential'),
        ('medium', 'Medium Growth Potential'),
        ('high', 'High Growth Potential'),
        ('very_high', 'Very High Growth Potential')
    ], string='Growth Potential', default='medium')
    
    market_maturity = fields.Selection([
        ('emerging', 'Emerging Market'),
        ('developing', 'Developing Market'),
        ('developed', 'Developed Market')
    ], string='Market Maturity', default='developing')
    
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)

    @api.depends('country_ids')
    def _compute_country_count(self):
        for continent in self:
            continent.country_count = len(continent.country_ids)

    @api.depends('country_ids.gdp')
    def _compute_total_gdp(self):
        for continent in self:
            continent.total_gdp = sum(continent.country_ids.mapped('gdp'))

    def name_get(self):
        result = []
        for continent in self:
            name = f"{continent.name} ({continent.code})"
            result.append((continent.id, name))
        return result

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if name:
            # Search by name or code
            continents = self.search([
                '|',
                ('name', operator, name),
                ('code', operator, name)
            ] + args, limit=limit)
            return continents.name_get()
        return super()._name_search(name, args, operator, limit, name_get_uid)

    def action_view_countries(self):
        """Action to view countries in this continent"""
        self.ensure_one()
        return {
            'name': _('Countries in %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.country',
            'view_mode': 'tree,form',
            'domain': [('continent_id', '=', self.id)],
            'context': {'default_continent_id': self.id},
        }

    def get_population_summary(self):
        """Get population summary for this continent"""
        self.ensure_one()
        return {
            'continent_name': self.name,
            'total_population': self.population,
            'country_count': self.country_count,
            'average_population_per_country': self.population / self.country_count if self.country_count > 0 else 0,
            'total_gdp': self.total_gdp,
        }
