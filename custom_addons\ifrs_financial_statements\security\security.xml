<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- IFRS Financial Statements Category -->
        <record id="module_category_ifrs" model="ir.module.category">
            <field name="name">IFRS Financial Statements</field>
            <field name="description">Manage IFRS compliant financial statements</field>
            <field name="sequence">10</field>
        </record>

        <!-- IFRS User Group -->
        <record id="group_ifrs_user" model="res.groups">
            <field name="name">IFRS User</field>
            <field name="category_id" ref="module_category_ifrs"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">User can view and create IFRS financial statements</field>
        </record>

        <!-- IFRS Manager Group -->
        <record id="group_ifrs_manager" model="res.groups">
            <field name="name">IFRS Manager</field>
            <field name="category_id" ref="module_category_ifrs"/>
            <field name="implied_ids" eval="[(4, ref('group_ifrs_user'))]"/>
            <field name="comment">Manager can approve and publish IFRS financial statements</field>
        </record>

        <!-- IFRS Auditor Group -->
        <record id="group_ifrs_auditor" model="res.groups">
            <field name="name">IFRS Auditor</field>
            <field name="category_id" ref="module_category_ifrs"/>
            <field name="implied_ids" eval="[(4, ref('group_ifrs_user'))]"/>
            <field name="comment">Auditor can review compliance checks and audit trails</field>
        </record>

        <!-- Record Rules -->
        
        <!-- IFRS Financial Statement Rules -->
        <record id="ifrs_financial_statement_user_rule" model="ir.rule">
            <field name="name">IFRS Financial Statement: User Access</field>
            <field name="model_id" ref="model_ifrs_financial_statement"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_ifrs_user'))]"/>
        </record>

        <record id="ifrs_financial_statement_manager_rule" model="ir.rule">
            <field name="name">IFRS Financial Statement: Manager Access</field>
            <field name="model_id" ref="model_ifrs_financial_statement"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_ifrs_manager'))]"/>
        </record>

        <!-- IFRS Statement Line Rules -->
        <record id="ifrs_statement_line_user_rule" model="ir.rule">
            <field name="name">IFRS Statement Line: User Access</field>
            <field name="model_id" ref="model_ifrs_statement_line"/>
            <field name="domain_force">[('statement_id.company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_ifrs_user'))]"/>
        </record>

        <!-- IFRS Compliance Check Rules -->
        <record id="ifrs_compliance_check_user_rule" model="ir.rule">
            <field name="name">IFRS Compliance Check: User Access</field>
            <field name="model_id" ref="model_ifrs_compliance_check"/>
            <field name="domain_force">[('statement_id.company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_ifrs_user'))]"/>
        </record>

    </data>
</odoo>
