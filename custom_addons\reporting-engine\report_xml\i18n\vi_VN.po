# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_xml
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: reporting-engine (8.0)\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-07 05:41+0000\n"
"PO-Revision-Date: 2016-03-11 15:46+0000\n"
"Last-Translator: <>\n"
"Language-Team: Vietnamese (Viet Nam) (http://www.transifex.com/oca/OCA-"
"reporting-engine-8-0/language/vi_VN/)\n"
"Language: vi_VN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: report_xml
#: model:ir.model,name:report_xml.model_report_report_xml_abstract
msgid "Abstract XML Report"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,help:report_xml.field_ir_actions_report__xml_declaration
msgid ""
"Add `<?xml encoding=\"...\" version=\"...\"?>` at the start of final report "
"file."
msgstr ""

#. module: report_xml
#: model:ir.actions.report,name:report_xml.demo_xml_report
msgid "Demo xml report"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,help:report_xml.field_ir_actions_report__xml_encoding
msgid ""
"Encoding for XML reports. If nothing is selected, then UTF-8 will be applied."
msgstr ""

#. module: report_xml
#: model:ir.model.fields,help:report_xml.field_ir_actions_report__xml_extension
msgid "Extension for XML Reports, by default is `xml`"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,help:report_xml.field_ir_actions_report__xsd_schema
msgid ""
"File with XSD Schema for checking content of result report. Can be empty if "
"validation is not required."
msgstr ""

#. module: report_xml
#: model:ir.model,name:report_xml.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,field_description:report_xml.field_ir_actions_report__report_type
msgid "Report Type"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,help:report_xml.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""

#. module: report_xml
#: model:ir.model.fields.selection,name:report_xml.selection__ir_actions_report__xml_encoding__utf-8
msgid "UTF-8"
msgstr ""

#. module: report_xml
#: model:ir.model.fields.selection,name:report_xml.selection__ir_actions_report__report_type__qweb-xml
msgid "XML"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,field_description:report_xml.field_ir_actions_report__xml_declaration
msgid "XML Declaration"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,field_description:report_xml.field_ir_actions_report__xml_encoding
msgid "XML Encoding"
msgstr ""

#. module: report_xml
#: model_terms:ir.ui.view,arch_db:report_xml.ir_actions_report_view_form_report_xml
msgid "XML Rreport Settings"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,field_description:report_xml.field_ir_actions_report__xsd_schema
msgid "XSD Validation Schema"
msgstr ""

#. module: report_xml
#: model:ir.model.fields,field_description:report_xml.field_ir_actions_report__xml_extension
msgid "Xml Extension"
msgstr ""

#~ msgid "ID"
#~ msgstr "ID"
