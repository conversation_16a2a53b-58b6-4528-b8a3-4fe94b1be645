# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ResCompany(models.Model):
    _inherit = 'res.company'

    # Financial planning preferences
    default_planning_currency_id = fields.Many2one('res.currency', string='Default Planning Currency',
                                                  help='Default currency for financial planning')
    default_planning_horizon_months = fields.Integer(string='Default Planning Horizon (Months)', default=60,
                                                    help='Default planning horizon in months')
    default_historical_months = fields.Integer(string='Default Historical Period (Months)', default=6,
                                              help='Default historical analysis period in months')
    
    # Market and demographic preferences
    primary_market_continent_id = fields.Many2one('financial.planning.continent', string='Primary Market Continent')
    primary_market_country_ids = fields.Many2many('financial.planning.country', 
                                                  'company_primary_country_rel', 'company_id', 'country_id',
                                                  string='Primary Market Countries')
    
    # Financial planning settings
    default_revenue_growth_rate = fields.Float(string='Default Revenue Growth Rate (%)', digits=(5, 2), default=15.0)
    default_market_penetration_rate = fields.Float(string='Default Market Penetration Rate (%)', digits=(5, 2), default=5.0)
    default_customer_acquisition_cost = fields.Monetary(string='Default Customer Acquisition Cost', 
                                                        currency_field='currency_id')
    
    # Risk and compliance settings
    default_risk_level = fields.Selection([
        ('very_low', 'Very Low Risk'),
        ('low', 'Low Risk'),
        ('medium', 'Medium Risk'),
        ('high', 'High Risk'),
        ('very_high', 'Very High Risk')
    ], string='Default Risk Level', default='medium')
    
    # Reporting preferences
    financial_planning_dashboard_enabled = fields.Boolean(string='Enable Financial Planning Dashboard', default=True)
    population_analytics_enabled = fields.Boolean(string='Enable Population Analytics', default=True)
    
    def get_company_planning_defaults(self):
        """Get company's financial planning defaults"""
        self.ensure_one()
        return {
            'planning_currency_id': self.default_planning_currency_id.id or self.currency_id.id,
            'planning_horizon_months': self.default_planning_horizon_months,
            'historical_months': self.default_historical_months,
            'revenue_growth_rate': self.default_revenue_growth_rate,
            'market_penetration_rate': self.default_market_penetration_rate,
            'customer_acquisition_cost': self.default_customer_acquisition_cost,
            'risk_level': self.default_risk_level,
            'primary_market_continent': self.primary_market_continent_id.id if self.primary_market_continent_id else False,
            'primary_market_countries': self.primary_market_country_ids.ids,
        }
