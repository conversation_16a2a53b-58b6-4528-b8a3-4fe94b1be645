# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime, date, timedelta


class BudgetVsActualReportWizard(models.TransientModel):
    _name = 'budget.vs.actual.report.wizard'
    _description = 'Budget vs Actual Report Wizard'

    name = fields.Char(string='Report Name', default='Budget vs Actual Report')
    date_from = fields.Date(string='Start Date', required=True, default=lambda self: date.today().replace(day=1))
    date_to = fields.Date(string='End Date', required=True, default=lambda self: date.today())
    budget_id = fields.Many2one('budget.budget', string='Budget', help='Leave empty to include all budgets')
    account_ids = fields.Many2many('account.account', string='Accounts', help='Leave empty to include all accounts with budget or actual data')
    analytic_account_ids = fields.Many2many('account.analytic.account', string='Analytic Accounts', help='Filter by specific analytic accounts')
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company, required=True)
    report_type = fields.Selection([
        ('summary', 'Summary Report'),
        ('detailed', 'Detailed Report'),
        ('variance_analysis', 'Variance Analysis')
    ], string='Report Type', default='summary', required=True)
    
    # Filter options
    show_zero_budget = fields.Boolean(string='Show Zero Budget Accounts', default=False)
    show_zero_actual = fields.Boolean(string='Show Zero Actual Accounts', default=False)
    variance_threshold = fields.Float(string='Variance Threshold (%)', default=0.0, help='Only show accounts with variance above this percentage')

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise UserError(_('Start Date must be before End Date.'))

    def action_generate_report(self):
        """Generate and display the budget vs actual report"""
        self.ensure_one()
        
        # Create report record
        report_vals = {
            'name': self.name or f'Budget vs Actual Report - {self.date_from} to {self.date_to}',
            'date_from': self.date_from,
            'date_to': self.date_to,
            'budget_id': self.budget_id.id if self.budget_id else False,
            'account_ids': [(6, 0, self.account_ids.ids)] if self.account_ids else False,
            'analytic_account_ids': [(6, 0, self.analytic_account_ids.ids)] if self.analytic_account_ids else False,
            'company_id': self.company_id.id,
        }
        
        report = self.env['budget.vs.actual.report'].create(report_vals)
        report.generate_report_data()
        
        # Filter report lines based on wizard settings
        lines_to_remove = self.env['budget.vs.actual.report.line']
        
        for line in report.line_ids:
            should_remove = False
            
            # Filter zero budget accounts
            if not self.show_zero_budget and line.budget_amount == 0:
                should_remove = True
            
            # Filter zero actual accounts
            if not self.show_zero_actual and line.actual_amount == 0:
                should_remove = True
            
            # Filter by variance threshold
            if abs(line.variance_percent) < self.variance_threshold:
                should_remove = True
            
            if should_remove:
                lines_to_remove |= line
        
        lines_to_remove.unlink()
        
        # Return action to display the report
        return {
            'name': _('Budget vs Actual Report'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.vs.actual.report',
            'res_id': report.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'report_type': self.report_type}
        }

    def action_print_report(self):
        """Print the budget vs actual report"""
        self.ensure_one()
        
        # Generate report data first
        action = self.action_generate_report()
        report_id = action['res_id']
        
        # Return print action
        return self.env.ref('budget_vs_actual_report.action_budget_vs_actual_report_pdf').report_action(
            self.env['budget.vs.actual.report'].browse(report_id)
        )

    def action_export_excel(self):
        """Export the budget vs actual report to Excel"""
        self.ensure_one()
        
        # Generate report data first
        action = self.action_generate_report()
        report_id = action['res_id']
        report = self.env['budget.vs.actual.report'].browse(report_id)
        
        # Create Excel export (this would require xlsxwriter or similar)
        # For now, return a message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Export to Excel'),
                'message': _('Excel export functionality will be available in the next version.'),
                'type': 'info',
            }
        }

    @api.onchange('budget_id')
    def _onchange_budget_id(self):
        """Update date range when budget is selected"""
        if self.budget_id:
            self.date_from = self.budget_id.date_from
            self.date_to = self.budget_id.date_to

    def action_quick_monthly_report(self):
        """Generate quick monthly report for current month"""
        today = date.today()
        self.date_from = today.replace(day=1)
        # Get last day of current month
        if today.month == 12:
            self.date_to = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            self.date_to = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        self.name = f'Monthly Budget Report - {today.strftime("%B %Y")}'
        return self.action_generate_report()

    def action_quick_quarterly_report(self):
        """Generate quick quarterly report for current quarter"""
        today = date.today()
        quarter = (today.month - 1) // 3 + 1
        
        # Calculate quarter start and end dates
        quarter_start_month = (quarter - 1) * 3 + 1
        self.date_from = today.replace(month=quarter_start_month, day=1)
        
        if quarter == 4:
            self.date_to = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            next_quarter_start = quarter_start_month + 3
            self.date_to = today.replace(month=next_quarter_start, day=1) - timedelta(days=1)
        
        self.name = f'Q{quarter} Budget Report - {today.year}'
        return self.action_generate_report()

    def action_quick_yearly_report(self):
        """Generate quick yearly report for current year"""
        today = date.today()
        self.date_from = today.replace(month=1, day=1)
        self.date_to = today.replace(month=12, day=31)
        self.name = f'Annual Budget Report - {today.year}'
        return self.action_generate_report()
