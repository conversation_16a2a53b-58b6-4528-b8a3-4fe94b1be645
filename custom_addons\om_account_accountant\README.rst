==================
Odoo 18 Accounting
==================

This Module will adds following accounting features to Odoo 18 community edition:
Financial Reports, Asset Management, Budget Management, Bank Statement Import,
Daily Reports, Customer Follow Ups and Recurring Payments


Common FAQ
==========
1. How to skip in payment status and directly move to paid status ?
a) https://www.youtube.com/watch?v=eWxfy86Byog

Installation
============

To install this module, you need to:

Download the module and add it to your Odoo addons folder. Afterward, log on to
your Odoo server and go to the Apps menu. Trigger the debug mode and update the
list by clicking on the "Update Apps List" link. Now install the module by
clicking on the install button.

Upgrade
============

To upgrade this module, you need to:

Download the module and add it to your Odoo addons folder. Restart the server
and log on to your Odoo server. Select the Apps menu and upgrade the module by
clicking on the upgrade button.


Configuration
=============

There is Nothing to Configure


Credits
=======

Contributors
------------

* Odoo Mates <<EMAIL>>
* Walnut Software Solutions <<EMAIL>>


Author & Maintainer
-------------------

This module is maintained by the Odoo Mates