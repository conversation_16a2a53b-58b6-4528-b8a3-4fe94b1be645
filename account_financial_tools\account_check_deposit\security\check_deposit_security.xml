<?xml version="1.0" encoding="utf-8" ?>
<!--
    Copyright (C) 2014-2020 Akretion France (http://www.akretion.com/)
    <AUTHOR> <<EMAIL>>
    The licence is in the file __manifest__.py
-->
<odoo noupdate="1">
    <record id="check_deposit_rule" model="ir.rule">
        <field name="name">Check Deposit multi-company</field>
        <field name="model_id" ref="model_account_check_deposit" />
        <field name="domain_force">
            ['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]
        </field>
    </record>
</odoo>
