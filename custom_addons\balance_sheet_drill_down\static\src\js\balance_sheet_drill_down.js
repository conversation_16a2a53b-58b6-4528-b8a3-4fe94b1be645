/** @odoo-module **/

/**
 * Balance Sheet Drill-Down Handler
 *
 * Simple JavaScript to add drill-down functionality to Balance Sheet amounts.
 * When users click on amounts, it opens detailed views showing account entries.
 */

// Initialize drill-down functionality when page loads
function initializeDrillDown() {
    const drillDownElements = document.querySelectorAll('.drill-down-amount');

    drillDownElements.forEach(element => {
        // Style the clickable elements
        element.style.cursor = 'pointer';
        element.style.color = '#007cba';
        element.title = 'Click to drill down to account details';

        // Add click handler
        element.addEventListener('click', async (event) => {
            event.preventDefault();
            event.stopPropagation();

            const accountId = element.dataset.accountId;
            const accountCode = element.dataset.accountCode;
            const accountName = element.dataset.accountName;
            const dateFrom = element.dataset.dateFrom;
            const dateTo = element.dataset.dateTo;

            if (!accountId) {
                alert('No account data available for drill-down');
                return;
            }

            try {
                // Create URL for account move lines
                const baseUrl = window.location.origin;
                const domain = encodeURIComponent(JSON.stringify([
                    ['account_id', '=', parseInt(accountId)],
                    ['date', '>=', dateFrom],
                    ['date', '<=', dateTo],
                    ['move_id.state', '=', 'posted']
                ]));

                const url = `${baseUrl}/web#action=account.action_account_moves_all_a&model=account.move.line&view_type=list&domain=${domain}&context=%7B%22search_default_account_id%22%3A${accountId}%7D`;

                // Open in new window
                window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            } catch (error) {
                console.error('Drill-down error:', error);
                alert('Error opening account details');
            }
        });

        // Add hover effects
        element.addEventListener('mouseenter', (event) => {
            event.target.style.backgroundColor = '#f0f8ff';
            event.target.style.textDecoration = 'underline';
        });

        element.addEventListener('mouseleave', (event) => {
            event.target.style.backgroundColor = '';
            event.target.style.textDecoration = '';
        });
    });
}

// Wait for DOM to be ready and initialize
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeDrillDown, 1000);
});

// Also initialize when content is dynamically loaded
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                setTimeout(initializeDrillDown, 500);
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}


