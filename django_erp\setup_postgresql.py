#!/usr/bin/env python
"""
PostgreSQL Setup Script for Django ERP
Creates database, user, and runs initial migrations
"""

import os
import sys
import subprocess
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def run_command(command, check=True):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {command}")
        print(f"Error: {e.stderr}")
        return None, e.stderr

def check_postgresql_service():
    """Check if PostgreSQL service is running"""
    print("🔍 Checking PostgreSQL service...")
    
    # Check if PostgreSQL is running (Windows)
    stdout, stderr = run_command('sc query postgresql-x64-14', check=False)
    if stdout and 'RUNNING' in stdout:
        print("✅ PostgreSQL service is running")
        return True
    
    # Try alternative service names
    for service_name in ['postgresql-x64-13', 'postgresql-x64-12', 'PostgreSQL']:
        stdout, stderr = run_command(f'sc query {service_name}', check=False)
        if stdout and 'RUNNING' in stdout:
            print(f"✅ PostgreSQL service ({service_name}) is running")
            return True
    
    print("❌ PostgreSQL service is not running")
    print("Please start PostgreSQL service manually:")
    print("1. Open Services (services.msc)")
    print("2. Find PostgreSQL service")
    print("3. Right-click and select 'Start'")
    return False

def create_database_and_user():
    """Create PostgreSQL database and user"""
    print("\n🔧 Setting up PostgreSQL database and user...")
    
    # Database configuration
    db_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',  # Connect to default database first
        'user': 'postgres',
        'password': 'postgres'  # Default password, user might need to change
    }
    
    target_db = 'django_erp'
    target_user = 'ERPUser'
    target_password = 'ERPUser'
    
    try:
        # Connect to PostgreSQL
        print("🔌 Connecting to PostgreSQL...")
        conn = psycopg2.connect(**db_config)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (target_db,))
        db_exists = cursor.fetchone()
        
        if db_exists:
            print(f"✅ Database '{target_db}' already exists")
        else:
            # Create database
            print(f"📊 Creating database '{target_db}'...")
            cursor.execute(f'CREATE DATABASE "{target_db}"')
            print(f"✅ Database '{target_db}' created successfully")
        
        # Check if user exists
        cursor.execute("SELECT 1 FROM pg_user WHERE usename = %s", (target_user,))
        user_exists = cursor.fetchone()
        
        if user_exists:
            print(f"✅ User '{target_user}' already exists")
        else:
            # Create user
            print(f"👤 Creating user '{target_user}'...")
            cursor.execute(f"CREATE USER \"{target_user}\" WITH PASSWORD '{target_password}'")
            print(f"✅ User '{target_user}' created successfully")
        
        # Grant privileges
        print(f"🔐 Granting privileges to '{target_user}'...")
        cursor.execute(f'GRANT ALL PRIVILEGES ON DATABASE "{target_db}" TO "{target_user}"')
        cursor.execute(f'ALTER USER "{target_user}" CREATEDB')
        print(f"✅ Privileges granted to '{target_user}'")
        
        cursor.close()
        conn.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL error: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure PostgreSQL is installed and running")
        print("2. Check if the default postgres user password is correct")
        print("3. You might need to update the password in this script")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def install_psycopg2():
    """Install psycopg2 if not already installed"""
    print("\n📦 Checking psycopg2 installation...")
    
    try:
        import psycopg2
        print("✅ psycopg2 is already installed")
        return True
    except ImportError:
        print("📥 Installing psycopg2...")
        stdout, stderr = run_command('pip install psycopg2-binary')
        if stdout:
            print("✅ psycopg2 installed successfully")
            return True
        else:
            print("❌ Failed to install psycopg2")
            print("Please install manually: pip install psycopg2-binary")
            return False

def run_django_migrations():
    """Run Django migrations"""
    print("\n🔄 Running Django migrations...")
    
    # Change to Django project directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Run migrations
    commands = [
        'python manage.py makemigrations',
        'python manage.py migrate',
    ]
    
    for command in commands:
        print(f"🔧 Running: {command}")
        stdout, stderr = run_command(command)
        if stdout:
            print(f"✅ {command} completed successfully")
            if 'migrations' in command.lower():
                print(stdout)
        else:
            print(f"❌ {command} failed")
            if stderr:
                print(f"Error: {stderr}")
            return False
    
    return True

def create_superuser():
    """Create Django superuser"""
    print("\n👤 Creating Django superuser...")
    
    print("Please enter superuser details:")
    username = input("Username (admin): ") or "admin"
    email = input("Email (<EMAIL>): ") or "<EMAIL>"
    
    # Create superuser command
    command = f'python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username=\'{username}\').exists() or User.objects.create_superuser(\'{username}\', \'{email}\', \'admin123\')"'
    
    stdout, stderr = run_command(command, check=False)
    if 'True' in stdout or not stderr:
        print(f"✅ Superuser '{username}' created successfully")
        print(f"📧 Email: {email}")
        print("🔑 Password: admin123")
        print("⚠️  Please change the password after first login")
        return True
    else:
        print(f"❌ Failed to create superuser")
        if stderr:
            print(f"Error: {stderr}")
        return False

def test_database_connection():
    """Test Django database connection"""
    print("\n🧪 Testing database connection...")
    
    command = 'python manage.py shell -c "from django.db import connection; cursor = connection.cursor(); cursor.execute(\'SELECT 1\'); print(\'Database connection successful\')"'
    stdout, stderr = run_command(command)
    
    if 'successful' in stdout:
        print("✅ Database connection test passed")
        return True
    else:
        print("❌ Database connection test failed")
        if stderr:
            print(f"Error: {stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 Django ERP PostgreSQL Setup")
    print("=" * 50)
    
    # Step 1: Check PostgreSQL service
    if not check_postgresql_service():
        return False
    
    # Step 2: Install psycopg2
    if not install_psycopg2():
        return False
    
    # Step 3: Create database and user
    if not create_database_and_user():
        return False
    
    # Step 4: Run migrations
    if not run_django_migrations():
        return False
    
    # Step 5: Test connection
    if not test_database_connection():
        return False
    
    # Step 6: Create superuser
    create_superuser()
    
    # Final success message
    print("\n" + "=" * 50)
    print("🎉 PostgreSQL Setup Complete!")
    print("=" * 50)
    print("✅ Database: django_erp")
    print("✅ User: ERPUser")
    print("✅ Host: localhost:5432")
    print("✅ All migrations applied")
    print("✅ Database connection tested")
    print("\n🚀 Your Django ERP is ready to use!")
    print("Run: python manage.py runserver")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Setup completed successfully!")
        sys.exit(0)
