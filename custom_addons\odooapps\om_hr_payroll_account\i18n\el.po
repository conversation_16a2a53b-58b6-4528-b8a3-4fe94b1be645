# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: Ko<PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:om_hr_payroll_account.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:om_hr_payroll_account.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr "Λογιστική"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:114
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:129
#, python-format
msgid "Adjustment Entry"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Αναλυτικός Λογαριασμός"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__account_credit
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Πιστωτικός λογαριασμός"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "Ημερομηνία Λογαριασμού"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__account_debit
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Χρεωστικός λογαριασμός"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "Συμβόλαιο Εργαζομένου"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:om_hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip_run
msgid "Payslip Batches"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip_line
msgid "Payslip Line"
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:65
#, python-format
msgid "Payslip of %s"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_contract__journal_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip__journal_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_run__journal_id
msgid "Salary Journal"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__account_tax_id
msgid "Tax"
msgstr "Φόρος"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:112
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:127
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr ""
