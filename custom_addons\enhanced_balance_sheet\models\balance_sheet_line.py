# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class EnhancedBalanceSheetLine(models.Model):
    _name = 'enhanced.balance.sheet.line'
    _description = 'Enhanced Balance Sheet Line'
    _order = 'sequence, id'

    balance_sheet_id = fields.Many2one(
        'enhanced.balance.sheet',
        string='Balance Sheet',
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    name = fields.Char(string='Line Description', required=True)
    
    line_type = fields.Selection([
        ('header', 'Header'),
        ('subtotal', 'Subtotal'),
        ('line', 'Line Item'),
        ('total', 'Total'),
    ], string='Line Type', required=True, default='line')
    
    section = fields.Selection([
        ('assets', 'Assets'),
        ('liabilities', 'Liabilities'),
        ('equity', 'Equity'),
        ('total', 'Total'),
    ], string='Section', required=True)
    
    # Account Types for automatic calculation
    account_types = fields.Text(string='Account Types (JSON)')
    
    # Related Accounts (for drill-down)
    account_ids = fields.Many2many(
        'account.account',
        string='Related Accounts',
        help='Accounts that contribute to this line item'
    )
    
    # Financial Amounts
    current_amount = fields.Monetary(
        string='Current Period',
        currency_field='currency_id',
        default=0.0
    )
    
    comparative_amount = fields.Monetary(
        string='Comparative Period',
        currency_field='currency_id',
        default=0.0
    )
    
    variance_amount = fields.Monetary(
        string='Variance',
        currency_field='currency_id',
        compute='_compute_variance',
        store=True
    )
    
    variance_percentage = fields.Float(
        string='Variance %',
        compute='_compute_variance',
        store=True
    )
    
    currency_id = fields.Many2one(
        related='balance_sheet_id.currency_id',
        string='Currency',
        readonly=True
    )
    
    # Formatting
    bold = fields.Boolean(string='Bold', default=False)
    underline = fields.Boolean(string='Underline', default=False)
    indent_level = fields.Integer(string='Indent Level', default=0)
    
    # Drill-down capability
    is_expandable = fields.Boolean(
        string='Can Drill Down',
        compute='_compute_expandable',
        store=True
    )
    
    @api.depends('comparative_amount', 'current_amount')
    def _compute_variance(self):
        for line in self:
            line.variance_amount = line.current_amount - line.comparative_amount
            if line.comparative_amount != 0:
                line.variance_percentage = (line.variance_amount / abs(line.comparative_amount)) * 100
            else:
                line.variance_percentage = 0.0
    
    @api.depends('line_type', 'account_ids')
    def _compute_expandable(self):
        for line in self:
            line.is_expandable = line.line_type == 'line' and bool(line.account_ids)
    
    def _compute_amounts(self):
        """Compute amounts for line items based on account types"""
        self.ensure_one()

        if not self.account_types:
            return

        # Parse account types from string representation
        import ast
        try:
            if isinstance(self.account_types, str):
                account_types = ast.literal_eval(self.account_types) if self.account_types else []
            else:
                account_types = self.account_types or []
        except:
            account_types = []

        if not account_types:
            return
        
        # Get accounts of specified types
        domain = [
            ('account_type', 'in', account_types),
            ('company_id', '=', self.balance_sheet_id.company_id.id),
        ]
        
        if not self.balance_sheet_id.show_zero_balance:
            domain.append(('balance', '!=', 0))
        
        accounts = self.env['account.account'].search(domain)
        self.account_ids = [(6, 0, accounts.ids)]
        
        # Calculate current period amount
        current_amount = 0.0
        comparative_amount = 0.0
        
        for account in accounts:
            # Current period
            current_balance = self._get_account_balance(
                account,
                self.balance_sheet_id.date_from,
                self.balance_sheet_id.date_to
            )
            current_amount += current_balance
            
            # Comparative period
            if self.balance_sheet_id.comparative_period:
                comparative_balance = self._get_account_balance(
                    account,
                    self.balance_sheet_id.comparative_date_from,
                    self.balance_sheet_id.comparative_date_to
                )
                comparative_amount += comparative_balance
        
        self.current_amount = current_amount
        self.comparative_amount = comparative_amount
    
    def _compute_totals(self):
        """Compute totals for subtotal and total lines"""
        self.ensure_one()
        
        if self.line_type == 'subtotal':
            # Sum all line items in this subtotal section
            child_lines = self.balance_sheet_id.line_ids.filtered(
                lambda l: l.sequence > self.sequence and 
                         l.sequence < self._get_next_subtotal_sequence() and
                         l.line_type == 'line'
            )
            self.current_amount = sum(child_lines.mapped('current_amount'))
            self.comparative_amount = sum(child_lines.mapped('comparative_amount'))
            
        elif self.line_type == 'total':
            if self.section in ['assets', 'liabilities', 'equity']:
                # Sum all subtotals in this section
                subtotal_lines = self.balance_sheet_id.line_ids.filtered(
                    lambda l: l.section == self.section and l.line_type == 'subtotal'
                )
                self.current_amount = sum(subtotal_lines.mapped('current_amount'))
                self.comparative_amount = sum(subtotal_lines.mapped('comparative_amount'))
            elif self.section == 'total':
                # Grand total - sum liabilities and equity
                liab_total = self.balance_sheet_id.line_ids.filtered(
                    lambda l: l.section == 'liabilities' and l.line_type == 'total'
                )
                equity_total = self.balance_sheet_id.line_ids.filtered(
                    lambda l: l.section == 'equity' and l.line_type == 'total'
                )
                self.current_amount = sum(liab_total.mapped('current_amount')) + sum(equity_total.mapped('current_amount'))
                self.comparative_amount = sum(liab_total.mapped('comparative_amount')) + sum(equity_total.mapped('comparative_amount'))
    
    def _get_next_subtotal_sequence(self):
        """Get the sequence of the next subtotal or total line"""
        next_lines = self.balance_sheet_id.line_ids.filtered(
            lambda l: l.sequence > self.sequence and l.line_type in ['subtotal', 'total']
        )
        return min(next_lines.mapped('sequence')) if next_lines else 9999
    
    def _get_account_balance(self, account, date_from, date_to):
        """Get account balance for specified period"""
        domain = [
            ('account_id', '=', account.id),
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('company_id', '=', self.balance_sheet_id.company_id.id),
        ]
        
        if self.balance_sheet_id.target_move == 'posted':
            domain.append(('parent_state', '=', 'posted'))
        
        move_lines = self.env['account.move.line'].search(domain)
        return sum(move_lines.mapped('balance'))
    
    def action_drill_down_accounts(self):
        """Drill down to account level"""
        if not self.is_expandable:
            return
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Accounts - %s') % self.name,
            'res_model': 'account.account',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.account_ids.ids)],
            'context': {
                'search_default_group_by_account_type': 1,
            }
        }
    
    def action_drill_down_ledger(self):
        """Drill down to ledger level (account move lines)"""
        if not self.is_expandable:
            return
        
        domain = [
            ('account_id', 'in', self.account_ids.ids),
            ('date', '>=', self.balance_sheet_id.date_from),
            ('date', '<=', self.balance_sheet_id.date_to),
            ('company_id', '=', self.balance_sheet_id.company_id.id),
        ]
        
        if self.balance_sheet_id.target_move == 'posted':
            domain.append(('parent_state', '=', 'posted'))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Ledger Entries - %s') % self.name,
            'res_model': 'account.move.line',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {
                'search_default_group_by_account': 1,
                'search_default_group_by_date': 1,
            }
        }
    
    def action_drill_down_entries(self):
        """Drill down to journal entry level"""
        if not self.is_expandable:
            return
        
        move_line_domain = [
            ('account_id', 'in', self.account_ids.ids),
            ('date', '>=', self.balance_sheet_id.date_from),
            ('date', '<=', self.balance_sheet_id.date_to),
            ('company_id', '=', self.balance_sheet_id.company_id.id),
        ]
        
        if self.balance_sheet_id.target_move == 'posted':
            move_line_domain.append(('parent_state', '=', 'posted'))
        
        move_lines = self.env['account.move.line'].search(move_line_domain)
        move_ids = move_lines.mapped('move_id').ids
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Journal Entries - %s') % self.name,
            'res_model': 'account.move',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', move_ids)],
            'context': {
                'search_default_group_by_journal': 1,
                'search_default_group_by_date': 1,
            }
        }
