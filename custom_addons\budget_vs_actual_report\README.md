# Budget vs Actual Report Module

## Overview
This module provides comprehensive budget vs actual reporting functionality for Odoo 17.0. It extends the existing budget functionality to provide better insights into budget performance and variance analysis.

## Features
- **Detailed Budget vs Actual Comparison**: Compare planned budget amounts with actual expenses and revenues
- **Variance Analysis**: Calculate and display variance amounts and percentages with color-coded indicators
- **Flexible Filtering**: Filter by date range, specific budgets, accounts, and analytic accounts
- **Multiple Report Types**: Summary, detailed, and variance analysis reports
- **Quick Report Generation**: Pre-configured monthly, quarterly, and annual reports
- **Visual Analytics**: Graphical representation of budget performance
- **Export Capabilities**: PDF reports with professional formatting
- **Account Integration**: Enhanced account model with budget vs actual methods

## Installation

### Prerequisites
- Odoo 17.0
- `base_account_budget` module installed
- `account` module installed
- `analytic` module installed

### Installation Steps
1. Copy the `budget_vs_actual_report` folder to your Odoo custom addons directory
2. Update the addons list in Odoo
3. Install the module from Apps menu

### Manual Installation via Command Line
```bash
# Navigate to your Odoo installation directory
cd /path/to/your/odoo

# Update module list
./odoo-bin -d your_database -u all --stop-after-init

# Install the module
./odoo-bin -d your_database -i budget_vs_actual_report --stop-after-init
```

## Usage

### Accessing the Report
1. Go to **Accounting > Reporting > Budget vs Actual Report**
2. Configure your report parameters:
   - **Report Name**: Give your report a descriptive name
   - **Date Range**: Select start and end dates
   - **Budget**: Choose a specific budget or leave empty for all budgets
   - **Accounts**: Select specific accounts or leave empty for all
   - **Report Type**: Choose between Summary, Detailed, or Variance Analysis

### Report Options
- **Show Zero Budget Accounts**: Include accounts with no budget allocation
- **Show Zero Actual Accounts**: Include accounts with no actual transactions
- **Variance Threshold**: Only show accounts with variance above specified percentage

### Quick Reports
- **Monthly Report**: Generate report for current month
- **Quarterly Report**: Generate report for current quarter
- **Annual Report**: Generate report for current year

### Understanding the Report
- **Green indicators**: Favorable variance (actual less than budget for expenses, more than budget for revenue)
- **Red indicators**: Unfavorable variance (actual more than budget for expenses, less than budget for revenue)
- **Yellow indicators**: Variance within acceptable range (±10%)

## Technical Details

### Models
- `budget.vs.actual.report`: Main report model
- `budget.vs.actual.report.line`: Individual report line items
- `budget.vs.actual.report.wizard`: Report generation wizard

### Enhanced Account Model
The module extends the `account.account` model with:
- `get_budget_vs_actual_data()`: Method to retrieve budget vs actual data for an account

### Security
- Access rights configured for account users and managers
- Report data is company-specific

## Customization
The module can be extended to include:
- Additional filtering options
- Custom variance thresholds
- Integration with other financial modules
- Advanced analytics and forecasting

## Support
For support and customization requests, please contact your system administrator or Odoo partner.

## License
LGPL-3
