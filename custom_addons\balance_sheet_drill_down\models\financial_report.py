# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class FinancialReport(models.TransientModel):
    _inherit = 'financial.report'

    def get_account_lines(self, data):
        """
        Override to add account_id information for drill-down functionality
        """
        lines = super().get_account_lines(data)
        
        # Add account_id to each line for drill-down
        for line in lines:
            if line.get('account_id'):
                # Account information is already present
                continue
            elif line.get('type') == 'account_type':
                # For account type lines, we need to find the accounts
                account_ids = self._get_accounts_for_line(line, data)
                if len(account_ids) == 1:
                    line['account_id'] = account_ids[0]
                    account = self.env['account.account'].browse(account_ids[0])
                    line['account_code'] = account.code
                    line['account_name'] = account.name
            elif line.get('type') == 'accounts':
                # For specific accounts
                if line.get('account_ids'):
                    account_ids = line['account_ids']
                    if len(account_ids) == 1:
                        line['account_id'] = account_ids[0]
                        account = self.env['account.account'].browse(account_ids[0])
                        line['account_code'] = account.code
                        line['account_name'] = account.name
        
        return lines

    def _get_accounts_for_line(self, line, data):
        """
        Get account IDs for a financial report line
        """
        account_ids = []
        
        if line.get('account_ids'):
            account_ids = line['account_ids']
        elif line.get('account_type_ids'):
            # Find accounts by account type
            account_types = line['account_type_ids']
            if isinstance(account_types, str):
                account_types = [account_types]
            
            accounts = self.env['account.account'].search([
                ('account_type', 'in', account_types),
                ('company_id', '=', self.env.company.id)
            ])
            account_ids = accounts.ids
        
        return account_ids

    @api.model
    def drill_down_to_account(self, account_id, date_from, date_to, target_move='posted'):
        """
        Drill down to account move lines
        """
        if not account_id:
            raise UserError(_("No account specified for drill-down"))
        
        account = self.env['account.account'].browse(account_id)
        if not account.exists():
            raise UserError(_("Account not found"))
        
        domain = [
            ('account_id', '=', account_id),
            ('date', '>=', date_from),
            ('date', '<=', date_to),
        ]
        
        if target_move == 'posted':
            domain.append(('move_id.state', '=', 'posted'))
        
        return {
            'name': _('Account Entries: %s') % account.display_name,
            'type': 'ir.actions.act_window',
            'res_model': 'account.move.line',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {
                'search_default_account_id': account_id,
                'default_account_id': account_id,
            },
            'target': 'new',
        }


class AccountFinancialReport(models.Model):
    _inherit = 'account.financial.report'

    def _compute_report_balance(self, reports, account_report, account_balances, params):
        """
        Override to include account information in the result
        """
        result = super()._compute_report_balance(reports, account_report, account_balances, params)
        
        # Add account information for drill-down
        if account_report.type == 'accounts' and account_report.account_ids:
            if len(account_report.account_ids) == 1:
                account = account_report.account_ids[0]
                result.update({
                    'account_id': account.id,
                    'account_code': account.code,
                    'account_name': account.name,
                })
        elif account_report.type == 'account_type' and account_report.account_type_ids:
            # For account type, find accounts
            account_types = account_report.account_type_ids.split(',') if isinstance(account_report.account_type_ids, str) else [account_report.account_type_ids]
            accounts = self.env['account.account'].search([
                ('account_type', 'in', account_types),
                ('company_id', '=', self.env.company.id)
            ])
            if len(accounts) == 1:
                account = accounts[0]
                result.update({
                    'account_id': account.id,
                    'account_code': account.code,
                    'account_name': account.name,
                })
        
        return result
