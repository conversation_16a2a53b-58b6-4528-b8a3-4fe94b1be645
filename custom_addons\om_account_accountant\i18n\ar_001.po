# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_accountant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 18:15+0000\n"
"PO-Revision-Date: 2022-04-15 18:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_group_action
#: model:ir.ui.menu,name:om_account_accountant.menu_account_group
msgid "Account Groups"
msgstr "مجموعات الحساب\n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_account_tag
#: model:ir.ui.menu,name:om_account_accountant.menu_account_tag
msgid "Account Tags"
msgstr "علامات تصنيف الحساب "

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.account_type_menu
msgid "Account Types"
msgstr "أنواع الحسابات\n"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_partner_property_form
msgid "Accounting"
msgstr "محاسبة\n"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Anglo-Saxon Accounting"
msgstr "المحاسبة الأنجلو سكسونية\n"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_bank
msgid "Bank Statements"
msgstr "البيانات المصرفية\n"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_bank_and_cash
msgid "Bank and Cash"
msgstr "البنك والنقد "

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_cash
msgid "Cash Registers"
msgstr "آلات تسجيل النقد "

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_coa_template
msgid "Chart of Accounts Templates"
msgstr "قوالب مخطط الحسابات "

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_fiscal_position_template
#: model:ir.ui.menu,name:om_account_accountant.menu_account_fiscal_position_template
msgid "Fiscal Position Templates"
msgstr "قوالب المركز المالي\n"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Group By"
msgstr "مجموعة من\n"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Method"
msgstr "طرق السداد"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_payment_method
#: model:ir.ui.menu,name:om_account_accountant.menu_account_payment_method
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_tree
msgid "Payment Methods"
msgstr "طرق السداد"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Type"
msgstr "نوع الدفع\n"

#. module: om_account_accountant
#: model:ir.actions.server,name:om_account_accountant.action_account_reconciliation
msgid "Reconcile"
msgstr "التصالح\n"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid ""
"Record the cost of a good as an expense when this good is\n"
"                                invoiced to a final customer (instead of recording the cost as soon\n"
"                                as the product is received in stock)."
msgstr ""
"سجل تكلفة سلعة كمصروف عندما تكون هذه السلعة\n"
"                                إصدار فاتورة للعميل النهائي (بدلاً من تسجيل التكلفة في أقرب وقت\n"
"                                كما يتم استلام المنتج في المخزون)."

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid ""
"Record the cost of a good as an expense when this good is invoiced to a "
"final customer."
msgstr ""
"سجل تكلفة سلعة كمصروف عندما يتم إصدار فاتورة بهذه السلعة للعميل النهائي.\n"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_templates
msgid "Templates"
msgstr "القوالب "

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "استخدام طريقة المحاسبة الأنجلو-ساكسونية"
