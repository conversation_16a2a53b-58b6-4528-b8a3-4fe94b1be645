# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_accountant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 06:40+0000\n"
"PO-Revision-Date: 2022-04-15 06:40+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_group_action
#: model:ir.ui.menu,name:om_account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Hesap Grupları"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_account_tag
#: model:ir.ui.menu,name:om_account_accountant.menu_account_tag
msgid "Account Tags"
msgstr "Hesap Etiketleri"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.account_type_menu
msgid "Account Types"
msgstr "Hesap Tipleri"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_partner_property_form
msgid "Accounting"
msgstr "Muhasebe"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Anglo-Saxon Accounting"
msgstr "Anglo-Sakson Muhasebesi"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_bank
msgid "Bank Statements"
msgstr "Banka Hesap Özetleri"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_bank_and_cash
msgid "Bank and Cash"
msgstr "Banka ve Nakit"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_cash
msgid "Cash Registers"
msgstr "Yazar Kasa"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_coa_template
msgid "Chart of Accounts Templates"
msgstr "Hesap Planı Şablonları"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_fiscal_position_template
#: model:ir.ui.menu,name:om_account_accountant.menu_account_fiscal_position_template
msgid "Fiscal Position Templates"
msgstr "Mali Pozisyon Şablonları"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Group By"
msgstr "Gruplama"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Method"
msgstr "Ödeme Metodu"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_payment_method
#: model:ir.ui.menu,name:om_account_accountant.menu_account_payment_method
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_tree
msgid "Payment Methods"
msgstr "Ödeme Metotları"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Type"
msgstr "Ödeme Tipi"

#. module: om_account_accountant
#: model:ir.actions.server,name:om_account_accountant.action_account_reconciliation
msgid "Reconcile"
msgstr "Mutabakat"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid ""
"Record the cost of a good as an expense when this good is\n"
"                                invoiced to a final customer (instead of recording the cost as soon\n"
"                                as the product is received in stock)."
msgstr ""
"Bir ürünün bedelini, o ürün son tüketiciye faturalandıktan sonra gider "
"olarak kayda gir. (Ürün stoğu geldiği anda gider olarak kaydetmek yerine)"

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid ""
"Record the cost of a good as an expense when this good is invoiced to a "
"final customer."
msgstr ""
"Ürünün bedelini, ürün son tüketiciye faturalandıktan sonra gider olarak "
"kayda gir."

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_templates
msgid "Templates"
msgstr "Şablonlar"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Anglo-Sakson muhasebesi kullan"
