<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_move_line_form" model="ir.ui.view">
        <field name="name">Journal Items (form)</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_form" />
        <field name="arch" type="xml">
            <field name="statement_line_id" position="after">
                <field
                    name="asset_profile_id"
                    domain="[('company_id','=', parent.company_id)]"
                />
                <field name="asset_id" />
            </field>
        </field>
    </record>
    <record id="view_account_move_line_filter" model="ir.ui.view">
        <field name="name">Journal Items (Search)</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_account_move_line_filter" />
        <field name="arch" type="xml">
            <field name="account_id" position="after">
                <field name="asset_id" />
            </field>
        </field>
    </record>
</odoo>
