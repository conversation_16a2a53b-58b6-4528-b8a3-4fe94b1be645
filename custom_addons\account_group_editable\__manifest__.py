{
    'name': 'Account Group Editable',
    'version': '********.0',
    'category': 'Accounting',
    'summary': 'Make Account Group field editable',
    'description': """
        This module makes the Account Group field editable in the Chart of Accounts.
        By default, Odoo automatically computes the group based on account code prefixes,
        but this module allows manual selection of account groups.
    """,
    'author': 'Custom Development',
    'depends': ['account'],
    'data': [],
    'installable': True,
    'auto_install': False,
    'application': False,
}
