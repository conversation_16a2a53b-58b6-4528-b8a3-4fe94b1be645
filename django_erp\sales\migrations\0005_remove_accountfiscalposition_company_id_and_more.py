# Generated by Django 4.2.21 on 2025-07-20 09:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_productpackaging'),
        ('accounting', '0003_accountpaymentterm_accountfiscalposition'),
        ('purchases', '0002_alter_purchaseorder_fiscal_position_id_and_more'),
        ('sales', '0004_saleorderline_product_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='accountfiscalposition',
            name='company_id',
        ),
        migrations.RemoveField(
            model_name='accountfiscalposition',
            name='create_uid',
        ),
        migrations.RemoveField(
            model_name='accountfiscalposition',
            name='write_uid',
        ),
        migrations.RemoveField(
            model_name='accountpaymentterm',
            name='company_id',
        ),
        migrations.RemoveField(
            model_name='accountpaymentterm',
            name='create_uid',
        ),
        migrations.RemoveField(
            model_name='accountpaymentterm',
            name='write_uid',
        ),
        migrations.AddField(
            model_name='saleorder',
            name='journal_id',
            field=models.ForeignKey(blank=True, help_text='Invoicing Journal', null=True, on_delete=django.db.models.deletion.PROTECT, to='accounting.accountjournal'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='prepayment_percent',
            field=models.FloatField(default=0.0, help_text='Prepayment percentage required'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='reference',
            field=models.CharField(blank=True, help_text='Payment Reference', max_length=255),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='require_payment',
            field=models.BooleanField(default=False, help_text='Request online payment from customer'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='require_signature',
            field=models.BooleanField(default=False, help_text='Request online signature from customer'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='signature',
            field=models.ImageField(blank=True, help_text='Customer Signature', null=True, upload_to='signatures/'),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='signed_by',
            field=models.CharField(blank=True, help_text='Signed By', max_length=255),
        ),
        migrations.AddField(
            model_name='saleorder',
            name='signed_on',
            field=models.DateTimeField(blank=True, help_text='Signature Date', null=True),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='is_downpayment',
            field=models.BooleanField(default=False, help_text='Is a down payment line'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='is_expense',
            field=models.BooleanField(default=False, help_text='Is an expense line'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='product_packaging_id',
            field=models.ForeignKey(blank=True, help_text='Product Packaging', null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.productpackaging'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='product_packaging_qty',
            field=models.FloatField(default=0.0, help_text='Packaging Quantity'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='product_template_id',
            field=models.ForeignKey(blank=True, help_text='Product Template', null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.producttemplate'),
        ),
        migrations.AddField(
            model_name='saleorderline',
            name='qty_delivered_method',
            field=models.CharField(choices=[('manual', 'Manual'), ('analytic', 'Analytic From Expenses')], default='manual', help_text='Method to update delivered quantity', max_length=20),
        ),
        migrations.AlterField(
            model_name='saleorder',
            name='fiscal_position_id',
            field=models.ForeignKey(blank=True, help_text='Fiscal Position', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition'),
        ),
        migrations.AlterField(
            model_name='saleorder',
            name='payment_term_id',
            field=models.ForeignKey(blank=True, help_text='Payment Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm'),
        ),
        migrations.AddConstraint(
            model_name='saleorderline',
            constraint=models.CheckConstraint(check=models.Q(('display_type__isnull', False), models.Q(('product_id__isnull', False), ('product_uom__isnull', False)), _connector='OR'), name='accountable_required_fields'),
        ),
        migrations.AddConstraint(
            model_name='saleorderline',
            constraint=models.CheckConstraint(check=models.Q(('display_type__isnull', True), models.Q(('product_id__isnull', True), ('price_unit', 0), ('product_uom_qty', 0), ('product_uom__isnull', True), ('customer_lead', 0)), _connector='OR'), name='non_accountable_null_fields'),
        ),
        migrations.DeleteModel(
            name='AccountFiscalPosition',
        ),
        migrations.DeleteModel(
            name='AccountPaymentTerm',
        ),
    ]
