# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class FinancialRatioAnalysis(models.Model):
    _name = 'financial.ratio.analysis'
    _description = 'Financial Ratio Analysis'
    _order = 'ratio_category, sequence, id'

    statement_id = fields.Many2one(
        'financial.statement.analytics',
        string='Financial Statement',
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    ratio_category = fields.Selection([
        ('liquidity', 'Liquidity Ratios'),
        ('profitability', 'Profitability Ratios'),
        ('leverage', 'Leverage Ratios'),
        ('efficiency', 'Efficiency Ratios'),
        ('market', 'Market Ratios'),
        ('coverage', 'Coverage Ratios'),
    ], string='Ratio Category', required=True)
    
    ratio_name = fields.Char(string='Ratio Name', required=True)
    
    ratio_value = fields.Float(
        string='Ratio Value',
        digits=(12, 4),
        required=True
    )
    
    comparative_ratio_value = fields.Float(
        string='Comparative Ratio Value',
        digits=(12, 4),
        help='Ratio value for comparative period'
    )
    
    benchmark_value = fields.Float(
        string='Benchmark Value',
        digits=(12, 4),
        help='Industry benchmark or target value'
    )
    
    variance_from_benchmark = fields.Float(
        string='Variance from Benchmark',
        compute='_compute_variance_from_benchmark',
        store=True,
        digits=(12, 4)
    )
    
    interpretation = fields.Selection([
        ('excellent', 'Excellent'),
        ('good', 'Good'),
        ('average', 'Average'),
        ('needs_attention', 'Needs Attention'),
        ('poor', 'Poor'),
    ], string='Interpretation', compute='_compute_interpretation', store=True)
    
    interpretation_text = fields.Text(
        string='Interpretation Text',
        compute='_compute_interpretation_text',
        store=True
    )
    
    formula = fields.Char(
        string='Formula',
        help='Mathematical formula for the ratio'
    )
    
    description = fields.Text(
        string='Description',
        help='Description of what this ratio measures'
    )
    
    # Trend Analysis
    trend_direction = fields.Selection([
        ('improving', 'Improving'),
        ('stable', 'Stable'),
        ('declining', 'Declining'),
    ], string='Trend Direction', compute='_compute_trend_direction', store=True)
    
    trend_percentage = fields.Float(
        string='Trend %',
        compute='_compute_trend_direction',
        store=True,
        digits=(12, 2)
    )
    
    # Color coding for dashboard
    color_code = fields.Char(
        string='Color Code',
        compute='_compute_color_code',
        store=True,
        help='Color code for dashboard display'
    )
    
    # Additional Analysis
    industry_average = fields.Float(
        string='Industry Average',
        digits=(12, 4),
        help='Industry average for this ratio'
    )
    
    peer_comparison = fields.Text(
        string='Peer Comparison',
        help='Comparison with peer companies'
    )
    
    recommendations = fields.Text(
        string='Recommendations',
        help='Recommendations for improvement'
    )
    
    @api.depends('ratio_value', 'benchmark_value')
    def _compute_variance_from_benchmark(self):
        """Compute variance from benchmark"""
        for record in self:
            if record.benchmark_value:
                record.variance_from_benchmark = record.ratio_value - record.benchmark_value
            else:
                record.variance_from_benchmark = 0.0
    
    @api.depends('ratio_category', 'ratio_value', 'benchmark_value', 'variance_from_benchmark')
    def _compute_interpretation(self):
        """Compute interpretation based on ratio category and values"""
        for record in self:
            if not record.benchmark_value:
                record.interpretation = 'average'
                continue
            
            variance_pct = abs(record.variance_from_benchmark / record.benchmark_value) * 100
            
            # Liquidity ratios - higher is generally better
            if record.ratio_category == 'liquidity':
                if record.ratio_value >= record.benchmark_value * 1.2:
                    record.interpretation = 'excellent'
                elif record.ratio_value >= record.benchmark_value:
                    record.interpretation = 'good'
                elif record.ratio_value >= record.benchmark_value * 0.8:
                    record.interpretation = 'average'
                elif record.ratio_value >= record.benchmark_value * 0.6:
                    record.interpretation = 'needs_attention'
                else:
                    record.interpretation = 'poor'
            
            # Profitability ratios - higher is generally better
            elif record.ratio_category == 'profitability':
                if record.ratio_value >= record.benchmark_value * 1.3:
                    record.interpretation = 'excellent'
                elif record.ratio_value >= record.benchmark_value * 1.1:
                    record.interpretation = 'good'
                elif record.ratio_value >= record.benchmark_value * 0.9:
                    record.interpretation = 'average'
                elif record.ratio_value >= record.benchmark_value * 0.7:
                    record.interpretation = 'needs_attention'
                else:
                    record.interpretation = 'poor'
            
            # Leverage ratios - lower is generally better
            elif record.ratio_category == 'leverage':
                if record.ratio_value <= record.benchmark_value * 0.7:
                    record.interpretation = 'excellent'
                elif record.ratio_value <= record.benchmark_value * 0.9:
                    record.interpretation = 'good'
                elif record.ratio_value <= record.benchmark_value * 1.1:
                    record.interpretation = 'average'
                elif record.ratio_value <= record.benchmark_value * 1.3:
                    record.interpretation = 'needs_attention'
                else:
                    record.interpretation = 'poor'
            
            # Default interpretation
            else:
                if variance_pct <= 10:
                    record.interpretation = 'good'
                elif variance_pct <= 20:
                    record.interpretation = 'average'
                else:
                    record.interpretation = 'needs_attention'
    
    @api.depends('ratio_category', 'ratio_name', 'ratio_value', 'interpretation')
    def _compute_interpretation_text(self):
        """Generate interpretation text"""
        for record in self:
            interpretations = {
                'excellent': 'This ratio indicates excellent performance and is well above industry standards.',
                'good': 'This ratio shows good performance and meets or exceeds expectations.',
                'average': 'This ratio is within acceptable range but has room for improvement.',
                'needs_attention': 'This ratio requires attention and improvement strategies should be considered.',
                'poor': 'This ratio indicates poor performance and immediate action is required.',
            }
            
            base_text = interpretations.get(record.interpretation, '')
            
            # Add specific guidance based on ratio category
            if record.ratio_category == 'liquidity':
                if record.interpretation in ['needs_attention', 'poor']:
                    base_text += ' Consider improving cash management and reducing short-term liabilities.'
                elif record.interpretation == 'excellent':
                    base_text += ' Excellent liquidity position provides financial flexibility.'
            
            elif record.ratio_category == 'profitability':
                if record.interpretation in ['needs_attention', 'poor']:
                    base_text += ' Focus on cost reduction and revenue enhancement strategies.'
                elif record.interpretation == 'excellent':
                    base_text += ' Strong profitability indicates effective management and market position.'
            
            elif record.ratio_category == 'leverage':
                if record.interpretation in ['needs_attention', 'poor']:
                    base_text += ' Consider debt reduction strategies and improving equity position.'
                elif record.interpretation == 'excellent':
                    base_text += ' Conservative debt levels provide financial stability.'
            
            record.interpretation_text = base_text
    
    @api.depends('ratio_value', 'comparative_ratio_value')
    def _compute_trend_direction(self):
        """Compute trend direction and percentage"""
        for record in self:
            if record.comparative_ratio_value:
                change = record.ratio_value - record.comparative_ratio_value
                record.trend_percentage = (change / abs(record.comparative_ratio_value)) * 100
                
                if abs(record.trend_percentage) < 5:  # Less than 5% change
                    record.trend_direction = 'stable'
                elif record.trend_percentage > 0:
                    # For most ratios, positive change is improving
                    # Exception: leverage ratios where lower is better
                    if record.ratio_category == 'leverage':
                        record.trend_direction = 'declining'
                    else:
                        record.trend_direction = 'improving'
                else:
                    if record.ratio_category == 'leverage':
                        record.trend_direction = 'improving'
                    else:
                        record.trend_direction = 'declining'
            else:
                record.trend_direction = 'stable'
                record.trend_percentage = 0.0
    
    @api.depends('interpretation', 'trend_direction')
    def _compute_color_code(self):
        """Compute color code for dashboard display"""
        for record in self:
            # Base color on interpretation
            color_map = {
                'excellent': '#28a745',  # Green
                'good': '#6f42c1',       # Purple
                'average': '#ffc107',    # Yellow
                'needs_attention': '#fd7e14',  # Orange
                'poor': '#dc3545',       # Red
            }
            
            record.color_code = color_map.get(record.interpretation, '#6c757d')  # Default gray
    
    def get_ratio_definition(self):
        """Get standard ratio definitions"""
        definitions = {
            'Current Ratio': {
                'formula': 'Current Assets / Current Liabilities',
                'description': 'Measures the ability to pay short-term obligations with current assets.',
                'benchmark': 2.0,
            },
            'Quick Ratio': {
                'formula': '(Current Assets - Inventory) / Current Liabilities',
                'description': 'Measures the ability to pay short-term obligations with liquid assets.',
                'benchmark': 1.0,
            },
            'Debt to Equity': {
                'formula': 'Total Debt / Total Equity',
                'description': 'Measures the relative proportion of debt and equity financing.',
                'benchmark': 0.5,
            },
            'Return on Equity': {
                'formula': 'Net Income / Average Shareholders Equity × 100',
                'description': 'Measures the return generated on shareholders equity.',
                'benchmark': 15.0,
            },
            'Return on Assets': {
                'formula': 'Net Income / Average Total Assets × 100',
                'description': 'Measures how efficiently assets are used to generate profit.',
                'benchmark': 10.0,
            },
        }
        
        return definitions.get(self.ratio_name, {})
    
    @api.model
    def create_standard_ratios(self, statement_id):
        """Create standard financial ratios for a statement"""
        statement = self.env['financial.statement.analytics'].browse(statement_id)
        
        standard_ratios = [
            # Liquidity Ratios
            {
                'ratio_category': 'liquidity',
                'ratio_name': 'Current Ratio',
                'formula': 'Current Assets / Current Liabilities',
                'benchmark_value': 2.0,
                'sequence': 10,
            },
            {
                'ratio_category': 'liquidity',
                'ratio_name': 'Quick Ratio',
                'formula': '(Current Assets - Inventory) / Current Liabilities',
                'benchmark_value': 1.0,
                'sequence': 20,
            },
            {
                'ratio_category': 'liquidity',
                'ratio_name': 'Cash Ratio',
                'formula': 'Cash and Cash Equivalents / Current Liabilities',
                'benchmark_value': 0.2,
                'sequence': 30,
            },
            
            # Profitability Ratios
            {
                'ratio_category': 'profitability',
                'ratio_name': 'Gross Profit Margin',
                'formula': 'Gross Profit / Revenue × 100',
                'benchmark_value': 25.0,
                'sequence': 40,
            },
            {
                'ratio_category': 'profitability',
                'ratio_name': 'Net Profit Margin',
                'formula': 'Net Income / Revenue × 100',
                'benchmark_value': 10.0,
                'sequence': 50,
            },
            {
                'ratio_category': 'profitability',
                'ratio_name': 'Return on Assets',
                'formula': 'Net Income / Average Total Assets × 100',
                'benchmark_value': 10.0,
                'sequence': 60,
            },
            {
                'ratio_category': 'profitability',
                'ratio_name': 'Return on Equity',
                'formula': 'Net Income / Average Shareholders Equity × 100',
                'benchmark_value': 15.0,
                'sequence': 70,
            },
            
            # Leverage Ratios
            {
                'ratio_category': 'leverage',
                'ratio_name': 'Debt to Equity',
                'formula': 'Total Debt / Total Equity',
                'benchmark_value': 0.5,
                'sequence': 80,
            },
            {
                'ratio_category': 'leverage',
                'ratio_name': 'Debt to Assets',
                'formula': 'Total Debt / Total Assets',
                'benchmark_value': 0.3,
                'sequence': 90,
            },
            {
                'ratio_category': 'leverage',
                'ratio_name': 'Interest Coverage',
                'formula': 'EBIT / Interest Expense',
                'benchmark_value': 5.0,
                'sequence': 100,
            },
        ]
        
        for ratio_data in standard_ratios:
            ratio_data.update({
                'statement_id': statement_id,
                'ratio_value': self._calculate_ratio_value(statement, ratio_data['ratio_name']),
            })
            
            definition = self.get_ratio_definition().get(ratio_data['ratio_name'], {})
            ratio_data.update({
                'description': definition.get('description', ''),
            })
            
            self.create(ratio_data)
    
    def _calculate_ratio_value(self, statement, ratio_name):
        """Calculate actual ratio value based on statement data"""
        # This would contain the actual calculation logic
        # For now, return sample values
        sample_values = {
            'Current Ratio': 2.27,
            'Quick Ratio': 1.85,
            'Cash Ratio': 0.13,
            'Gross Profit Margin': 35.5,
            'Net Profit Margin': 12.3,
            'Return on Assets': 8.5,
            'Return on Equity': 16.8,
            'Debt to Equity': 0.77,
            'Debt to Assets': 0.43,
            'Interest Coverage': 7.2,
        }
        
        return sample_values.get(ratio_name, 0.0)
