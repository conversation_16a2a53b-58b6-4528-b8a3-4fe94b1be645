from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timed<PERSON>ta
from django.utils import timezone
from core.models import Company, Country, Currency, Partner
from accounting.models import AccountTax
from sales.models import ProductUom, ProductUomCategory, StockWarehouse
from .models import (
    PurchaseOrder, PurchaseOrderLine, PurchaseApprovalSettings,
    StockPickingType
)

class PurchaseModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='purchaseuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Purchase Company',
            code='TPC',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create vendor
        self.vendor = Partner.objects.create(
            name='Test Vendor',
            supplier_rank=1,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create UOM category and UOM
        self.uom_category = ProductUomCategory.objects.create(
            name='Unit',
            create_uid=self.user,
            write_uid=self.user
        )

        self.uom_unit = ProductUom.objects.create(
            name='Unit',
            category_id=self.uom_category,
            uom_type='reference',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create warehouse
        self.warehouse = StockWarehouse.objects.create(
            name='Main Warehouse',
            code='WH',
            company_id=self.company,
            partner_id=self.vendor,  # Using vendor as address for simplicity
            create_uid=self.user,
            write_uid=self.user
        )

        # Create picking type
        self.picking_type = StockPickingType.objects.create(
            name='Receipts',
            code='incoming',
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create a real product using Django ORM
        try:
            from inventory.models import ProductTemplate, Product, ProductCategory

            # Create product category
            self.product_category = ProductCategory.objects.create(
                name='Test Category',
                create_uid=self.user,
                write_uid=self.user
            )

            # Create product template
            self.product_template = ProductTemplate.objects.create(
                name='Test Product',
                categ_id=self.product_category,
                uom_id=self.uom_unit,
                uom_po_id=self.uom_unit,
                create_uid=self.user,
                write_uid=self.user
            )

            # Create product
            self.product = Product.objects.create(
                product_tmpl_id=self.product_template,
                create_uid=self.user,
                write_uid=self.user
            )
        except ImportError:
            # Fallback: create a simple mock if inventory models aren't available
            class MockProduct:
                def __init__(self, id):
                    self.id = id
                    self.pk = id

            self.product = MockProduct(1)

        # Create purchase approval settings
        self.approval_settings = PurchaseApprovalSettings.objects.create(
            company_id=self.company,
            po_double_validation='two_step',
            po_double_validation_amount=Decimal('1000.0'),
            po_lock='edit',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create tax
        self.purchase_tax = AccountTax.objects.create(
            name='Purchase Tax 10%',
            type_tax_use='purchase',
            amount_type='percent',
            amount=Decimal('10.0'),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

    def test_purchase_order_creation(self):
        """Test purchase order creation and basic functionality"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        self.assertEqual(order.name, 'New')
        self.assertEqual(order.state, 'draft')
        self.assertEqual(order.invoice_status, 'no')
        self.assertEqual(order.priority, '0')
        self.assertEqual(str(order), 'New')

    def test_purchase_order_confirmation_one_step(self):
        """Test purchase order confirmation with one-step approval"""
        # Set one-step approval
        self.approval_settings.po_double_validation = 'one_step'
        self.approval_settings.save()

        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line (now using real product)
        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product',
            product_qty=Decimal('2.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )
        line.taxes_id.add(self.purchase_tax)

        # Confirm order (should go directly to purchase state)
        order.button_confirm()

        self.assertEqual(order.state, 'purchase')
        self.assertNotEqual(order.name, 'New')
        self.assertTrue(order.name.startswith('PO/'))
        self.assertIsNotNone(order.date_approve)

    def test_purchase_order_confirmation_two_step_low_amount(self):
        """Test purchase order confirmation with two-step approval for low amounts"""
        # Create purchase order with amount below threshold
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line with low amount
        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Low Cost Product',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('500.0'),  # Below 1000 threshold
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )

        # Confirm order (should go directly to purchase state due to low amount)
        order.button_confirm()

        self.assertEqual(order.state, 'purchase')
        self.assertIsNotNone(order.date_approve)

    def test_purchase_order_confirmation_two_step_high_amount(self):
        """Test purchase order confirmation with two-step approval for high amounts"""
        # Create purchase order with amount above threshold
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line with high amount
        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Expensive Product',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('1500.0'),  # Above 1000 threshold
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )

        # Confirm order (should go to 'to approve' state)
        order.button_confirm()

        self.assertEqual(order.state, 'to approve')
        self.assertIsNone(order.date_approve)

        # Now approve the order
        order.button_approve(force=True)  # Force approval for testing

        self.assertEqual(order.state, 'purchase')
        self.assertIsNotNone(order.date_approve)

    def test_purchase_order_line_amounts(self):
        """Test purchase order line amount calculations"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line with discount and tax
        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product with Discount',
            product_qty=Decimal('2.0'),
            price_unit=Decimal('100.0'),
            discount=Decimal('10.0'),  # 10% discount
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )
        line.taxes_id.add(self.purchase_tax)
        # Recompute amounts after adding tax
        line._compute_amounts()
        line.save()

        # Check computed amounts
        expected_subtotal = Decimal('180.0')  # (100 * 2) - 10% discount
        expected_tax = Decimal('18.0')  # 10% tax on subtotal
        expected_total = Decimal('198.0')  # subtotal + tax

        self.assertEqual(line.price_subtotal, expected_subtotal)
        self.assertEqual(line.price_tax, expected_tax)
        self.assertEqual(line.price_total, expected_total)

    def test_purchase_order_totals(self):
        """Test purchase order total calculations"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add multiple order lines
        line1 = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Product 1',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )
        line1.taxes_id.add(self.purchase_tax)
        line1.save()  # Recalculate amounts after adding tax

        line2 = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Product 2',
            product_qty=Decimal('2.0'),
            price_unit=Decimal('50.0'),
            discount=Decimal('20.0'),  # 20% discount
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )
        line2.taxes_id.add(self.purchase_tax)
        line2.save()  # Recalculate amounts after adding tax

        # Refresh order to get updated totals
        order.refresh_from_db()

        # Check order totals
        expected_untaxed = Decimal('180.0')  # 100 + (50*2 - 20%)
        expected_tax = Decimal('18.0')  # 10% of untaxed
        expected_total = Decimal('198.0')  # untaxed + tax

        self.assertEqual(order.amount_untaxed, expected_untaxed)
        self.assertEqual(order.amount_tax, expected_tax)
        self.assertEqual(order.amount_total, expected_total)

    def test_purchase_order_line_validation(self):
        """Test purchase order line validation rules"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test that accountable line requires UOM
        with self.assertRaises(ValidationError):
            line = PurchaseOrderLine(
                order_id=order,
                name='Invalid Line',
                product_qty=Decimal('1.0'),
                price_unit=Decimal('100.0'),
                # Missing product_uom
                create_uid=self.user,
                write_uid=self.user
            )
            line.full_clean()

        # Test that section line cannot have product values
        with self.assertRaises(ValidationError):
            line = PurchaseOrderLine(
                order_id=order,
                name='Section Line',
                display_type='line_section',
                price_unit=Decimal('100.0'),  # Should be 0 for section lines
                create_uid=self.user,
                write_uid=self.user
            )
            line.full_clean()

    def test_purchase_order_state_transitions(self):
        """Test purchase order state transitions"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test cancel from draft
        order.button_cancel()
        self.assertEqual(order.state, 'cancel')

        # Test reset to draft
        order.button_draft()
        self.assertEqual(order.state, 'draft')

        # Test confirmation and approval
        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )

        # Set one-step approval for this test
        self.approval_settings.po_double_validation = 'one_step'
        self.approval_settings.save()

        order.button_confirm()
        self.assertEqual(order.state, 'purchase')

        # Test lock/unlock
        order.button_done()
        self.assertEqual(order.state, 'done')

        order.button_unlock()
        self.assertEqual(order.state, 'purchase')

    def test_vendor_validation(self):
        """Test vendor validation"""
        # Create partner that is not a vendor
        non_vendor = Partner.objects.create(
            name='Not a Vendor',
            customer_rank=1,  # Customer, not supplier
            supplier_rank=0,
            create_uid=self.user,
            write_uid=self.user
        )

        # Should raise validation error when creating PO with non-vendor
        with self.assertRaises(ValidationError):
            order = PurchaseOrder(
                partner_id=non_vendor,
                company_id=self.company,
                currency_id=self.currency,
                user_id=self.user,
                picking_type_id=self.picking_type,
                create_uid=self.user,
                write_uid=self.user
            )
            order.full_clean()

    def test_section_and_note_lines(self):
        """Test section and note lines functionality"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add section line
        section_line = PurchaseOrderLine.objects.create(
            order_id=order,
            name='=== PRODUCTS ===',
            display_type='line_section',
            sequence=1,
            # For display lines, all product-related fields must be 0/NULL
            product_qty=0,
            price_unit=0,
            product_uom=None,
            date_planned=None,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add note line
        note_line = PurchaseOrderLine.objects.create(
            order_id=order,
            name='Special delivery instructions',
            display_type='line_note',
            sequence=2,
            # For display lines, all product-related fields must be 0/NULL
            product_qty=0,
            price_unit=0,
            product_uom=None,
            date_planned=None,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add product line
        product_line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Actual Product',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            sequence=3,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check that section and note lines have zero amounts
        self.assertEqual(section_line.price_subtotal, Decimal('0.0'))
        self.assertEqual(note_line.price_subtotal, Decimal('0.0'))
        self.assertEqual(product_line.price_subtotal, Decimal('100.0'))

        # Check order total only includes product lines
        order.refresh_from_db()
        self.assertEqual(order.amount_untaxed, Decimal('100.0'))

    def test_approval_settings(self):
        """Test purchase approval settings"""
        # Test approval settings update (use existing one from setUp)
        settings = self.approval_settings
        settings.po_double_validation = 'two_step'
        settings.po_double_validation_amount = Decimal('2000.0')
        settings.po_lock = 'lock'
        settings.po_lead = 5.0
        settings.save()

        self.assertEqual(str(settings), f"Purchase Settings - {self.company.name}")
        self.assertEqual(settings.po_double_validation, 'two_step')
        self.assertEqual(settings.po_double_validation_amount, Decimal('2000.0'))
        self.assertEqual(settings.po_lock, 'lock')
        self.assertEqual(settings.po_lead, 5.0)

    def test_sequence_generation(self):
        """Test sequence number generation"""
        # Set one-step approval
        self.approval_settings.po_double_validation = 'one_step'
        self.approval_settings.save()

        # Create and confirm first order
        order1 = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        line1 = PurchaseOrderLine.objects.create(
            order_id=order1,
            product_id=self.product,  # Required for accountable lines
            name='Product 1',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )

        order1.button_confirm()
        first_name = order1.name

        # Create and confirm second order
        order2 = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        line2 = PurchaseOrderLine.objects.create(
            order_id=order2,
            product_id=self.product,  # Required for accountable lines
            name='Product 2',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )

        order2.button_confirm()
        second_name = order2.name

        # Check that sequence numbers are incremental
        self.assertTrue(first_name.startswith('PO/'))
        self.assertTrue(second_name.startswith('PO/'))
        self.assertNotEqual(first_name, second_name)

        # Extract numbers and verify increment
        first_num = int(first_name.split('/')[-1])
        second_num = int(second_name.split('/')[-1])
        self.assertEqual(second_num, first_num + 1)

    def test_date_planned_computation(self):
        """Test planned date computation"""
        # Create purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line (should get default planned date)
        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            # Don't set date_planned - let it be auto-computed
            create_uid=self.user,
            write_uid=self.user
        )

        # Check that planned date was set
        self.assertIsNotNone(line.date_planned)
        self.assertGreater(line.date_planned, order.date_order)

        # Check that order planned date is computed
        order.refresh_from_db()
        self.assertEqual(order.date_planned, line.date_planned)

    def test_invoice_status_computation(self):
        """Test invoice status computation"""
        # Create and confirm purchase order
        order = PurchaseOrder.objects.create(
            partner_id=self.vendor,
            company_id=self.company,
            currency_id=self.currency,
            user_id=self.user,
            picking_type_id=self.picking_type,
            create_uid=self.user,
            write_uid=self.user
        )

        line = PurchaseOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product',
            product_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            date_planned=timezone.now() + timedelta(days=7),  # Required for accountable lines
            create_uid=self.user,
            write_uid=self.user
        )

        # Draft order should have 'no' invoice status
        self.assertEqual(order.invoice_status, 'no')

        # Set one-step approval and confirm
        self.approval_settings.po_double_validation = 'one_step'
        self.approval_settings.save()

        order.button_confirm()

        # Confirmed order with received quantity should have 'to invoice' status
        line.qty_received = Decimal('1.0')
        line.save()

        order._compute_invoice_status()
        order.save()

        self.assertEqual(order.invoice_status, 'to invoice')
