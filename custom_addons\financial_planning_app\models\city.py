# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date


class City(models.Model):
    _name = 'financial.planning.city'
    _description = 'City for Financial Planning'
    _order = 'population desc, name'

    name = fields.Char(string='City Name', required=True, translate=True)
    country_id = fields.Many2one('financial.planning.country', string='Country', required=True)
    continent_id = fields.Many2one(related='country_id.continent_id', string='Continent', store=True)
    
    # Geographic data
    latitude = fields.Float(string='Latitude', digits=(10, 6))
    longitude = fields.Float(string='Longitude', digits=(10, 6))
    elevation = fields.Integer(string='Elevation (m)', help='Elevation above sea level in meters')
    area_km2 = fields.Float(string='Area (km²)')
    
    # Population data
    population = fields.Float(string='Population', help='Population in millions', required=True)
    population_density = fields.Float(string='Population Density', help='People per km²', compute='_compute_population_density', store=True)
    urban_population_percentage = fields.Float(string='Urban Population %', digits=(5, 2), default=100.0)
    
    # Economic indicators
    gdp_contribution = fields.Monetary(string='GDP Contribution', currency_field='currency_id', help='City contribution to national GDP')
    average_income = fields.Monetary(string='Average Income', currency_field='currency_id')
    cost_of_living_index = fields.Float(string='Cost of Living Index', digits=(5, 2), help='Relative to national average (100 = national average)')
    
    # Growth rates
    annual_population_growth_rate = fields.Float(string='Population Growth Rate (%)', digits=(5, 2), default=2.0)
    annual_economic_growth_rate = fields.Float(string='Economic Growth Rate (%)', digits=(5, 2), default=3.5)
    
    # City classification
    city_type = fields.Selection([
        ('megacity', 'Megacity (>10M)'),
        ('metropolis', 'Metropolis (1-10M)'),
        ('large_city', 'Large City (300K-1M)'),
        ('medium_city', 'Medium City (100K-300K)'),
        ('small_city', 'Small City (<100K)')
    ], string='City Type', compute='_compute_city_type', store=True)
    
    is_capital = fields.Boolean(string='Is Capital City', default=False)
    is_financial_center = fields.Boolean(string='Is Financial Center', default=False)
    is_tech_hub = fields.Boolean(string='Is Technology Hub', default=False)
    is_industrial_center = fields.Boolean(string='Is Industrial Center', default=False)
    
    # Business and market data
    market_potential = fields.Selection([
        ('very_low', 'Very Low'),
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('very_high', 'Very High')
    ], string='Market Potential', default='medium')
    
    business_environment_score = fields.Float(string='Business Environment Score', digits=(3, 1), help='Score out of 10')
    infrastructure_quality = fields.Selection([
        ('poor', 'Poor'),
        ('fair', 'Fair'),
        ('good', 'Good'),
        ('excellent', 'Excellent')
    ], string='Infrastructure Quality', default='good')
    
    # Currency
    currency_id = fields.Many2one(related='country_id.currency_id', string='Currency', store=True)
    
    # Additional information
    time_zone = fields.Char(string='Time Zone')
    major_industries = fields.Text(string='Major Industries')
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)

    @api.depends('population', 'area_km2')
    def _compute_population_density(self):
        for city in self:
            if city.population and city.area_km2:
                # Convert population from millions to actual number
                city.population_density = (city.population * 1000000) / city.area_km2
            else:
                city.population_density = 0.0

    @api.depends('population')
    def _compute_city_type(self):
        for city in self:
            population_actual = city.population * 1000000  # Convert from millions
            if population_actual >= 10000000:
                city.city_type = 'megacity'
            elif population_actual >= 1000000:
                city.city_type = 'metropolis'
            elif population_actual >= 300000:
                city.city_type = 'large_city'
            elif population_actual >= 100000:
                city.city_type = 'medium_city'
            else:
                city.city_type = 'small_city'

    def name_get(self):
        result = []
        for city in self:
            name = f"{city.name}, {city.country_id.name}"
            result.append((city.id, name))
        return result

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if name:
            # Search by city name or country name
            cities = self.search([
                '|',
                ('name', operator, name),
                ('country_id.name', operator, name)
            ] + args, limit=limit)
            return cities.name_get()
        return super()._name_search(name, args, operator, limit, name_get_uid)

    def calculate_projected_population(self, target_date):
        """Calculate projected population for a target date"""
        self.ensure_one()
        if not self.population or not self.annual_population_growth_rate:
            return self.population
        
        current_year = date.today().year
        target_year = target_date.year if isinstance(target_date, date) else target_date
        years_diff = target_year - current_year
        
        # Compound growth formula: P(t) = P(0) * (1 + r)^t
        growth_factor = (1 + self.annual_population_growth_rate / 100) ** years_diff
        projected_population = self.population * growth_factor
        
        return projected_population

    def calculate_projected_gdp_contribution(self, target_date):
        """Calculate projected GDP contribution for a target date"""
        self.ensure_one()
        if not self.gdp_contribution or not self.annual_economic_growth_rate:
            return self.gdp_contribution
        
        current_year = date.today().year
        target_year = target_date.year if isinstance(target_date, date) else target_date
        years_diff = target_year - current_year
        
        # Compound growth formula
        growth_factor = (1 + self.annual_economic_growth_rate / 100) ** years_diff
        projected_gdp = self.gdp_contribution * growth_factor
        
        return projected_gdp

    def get_city_summary(self):
        """Get comprehensive city summary"""
        self.ensure_one()
        return {
            'city_name': self.name,
            'country': self.country_id.name,
            'continent': self.continent_id.name,
            'population': self.population,
            'population_density': self.population_density,
            'city_type': self.city_type,
            'gdp_contribution': self.gdp_contribution,
            'average_income': self.average_income,
            'population_growth_rate': self.annual_population_growth_rate,
            'economic_growth_rate': self.annual_economic_growth_rate,
            'market_potential': self.market_potential,
            'is_capital': self.is_capital,
            'is_financial_center': self.is_financial_center,
            'is_tech_hub': self.is_tech_hub,
        }

    @api.model
    def get_megacities(self):
        """Get all megacities (population > 10M)"""
        return self.search([('city_type', '=', 'megacity')])

    @api.model
    def get_financial_centers(self):
        """Get all financial centers"""
        return self.search([('is_financial_center', '=', True)])

    @api.model
    def get_tech_hubs(self):
        """Get all technology hubs"""
        return self.search([('is_tech_hub', '=', True)])

    @api.model
    def get_fastest_growing_cities(self, limit=20):
        """Get cities with highest population growth rates"""
        return self.search([('annual_population_growth_rate', '>', 0)], 
                          order='annual_population_growth_rate desc', limit=limit)
