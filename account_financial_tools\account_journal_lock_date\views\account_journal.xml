<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2017 ACSONE SA/NV
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <record model="ir.ui.view" id="view_account_journal_form">
        <field name="name">account.journal.form (in account_journal_lock_date)
        </field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.view_account_journal_form" />
        <field name="arch" type="xml">
            <field name="account_control_ids" position="after">
                <field name="fiscalyear_lock_date" />
                <field name="period_lock_date" />
            </field>
        </field>
    </record>
    <record model="ir.ui.view" id="view_account_journal_tree">
        <field name="name">account.journal.tree (in account_journal_lock_date)
        </field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.view_account_journal_tree" />
        <field name="arch" type="xml">
            <field name="type" position="after">
                <field name="fiscalyear_lock_date" />
                <field name="period_lock_date" />
            </field>
        </field>
    </record>
</odoo>
