===========
XML Reports
===========

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:f5a881c0eade552010cfe051f392fb1c7a5d474081f114d52a1f2a21d851096f
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Production%2FStable-green.png
    :target: https://odoo-community.org/page/development-status
    :alt: Production/Stable
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Freporting--engine-lightgray.png?logo=github
    :target: https://github.com/OCA/reporting-engine/tree/17.0/report_xml
    :alt: OCA/reporting-engine
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/reporting-engine-17-0/reporting-engine-17-0-report_xml
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/reporting-engine&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module was written to extend the functionality of the reporting
engine to support XML reports and allow modules to generate them by code
or by QWeb templates.

**Table of contents**

.. contents::
   :local:

Installation
============

To install this module, you need to:

-  Install `lxml <http://lxml.de/>`__ in Odoo's ``$PYTHONPATH``.
-  Install the repository
   `reporting-engine <https://github.com/OCA/reporting-engine>`__.

But this module does nothing for the end user by itself, so if you have
it installed it's probably because there is another module that depends
on it.

Usage
=====

This module is intended as a base engine for other modules to use it, so
no direct result if you are a user.

If you are a developer
----------------------

To learn from an example, just check the `demo
report <https://github.com/OCA/reporting-engine/blob/13.0/report_xml/demo/demo_report.xml>`__
on GitHub for the model ``res.company`` or check it in interface from
companies views.

To develop with this module, you need to:

-  Create a module.
-  Make it depend on this one.
-  Follow `instructions to create
   reports <https://www.odoo.com/documentation/13.0/reference/reports.html>`__
   having in mind that the ``report_type`` field in your
   ``ir.actions.report`` record must be ``qweb-xml``.

In case you want to create a `custom
report <https://www.odoo.com/documentation/13.0/reference/reports.html#custom-reports>`__,
the instructions remain the same as for HTML reports, and the method
that you must override is also called ``_get_report_values``, even when
this time you are creating a XML report.

You can make your custom report inherit ``report.report_xml.abstract``,
name it in such way ``report.<module.report_name>``. Also you can add a
XSD file for report validation into ``xsd_schema`` field of your report
(check `report
definition <https://github.com/OCA/reporting-engine/blob/13.0/report_xml/demo/report.xml>`__)
and have XSD automatic checking for free.

You can customize rendering process and validation way via changing
logic of ``generate_report`` and ``validate_report`` methods in your
report class.

You can visit
``http://<server-address>/report/xml/<module.report_name>/<ids>`` to see
your XML report online as a web page.

For further information, please visit:

-  https://www.odoo.com/forum/help-1
-  https://github.com/OCA/reporting-engine

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/reporting-engine/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/reporting-engine/issues/new?body=module:%20report_xml%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Tecnativa
* Avoin.Systems

Contributors
------------

-  Enric Tobella <<EMAIL>>

-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Jairo Llopis

-  `Avoin.Systems <https://avoin.systems/>`__:

   -  Tatiana Deribina

-  Iván Antón <<EMAIL>>

Other credits
-------------

-  Icon taken from http://commons.wikimedia.org/wiki/File:Text-xml.svg

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/reporting-engine <https://github.com/OCA/reporting-engine/tree/17.0/report_xml>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
