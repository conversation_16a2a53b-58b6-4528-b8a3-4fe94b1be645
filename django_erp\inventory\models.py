from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
from core.models import BaseModel, Company, Partner, Currency
from accounting.models import AccountAccount
from sales.models import ProductUom, ProductUomCategory, StockWarehouse
from purchases.models import StockPickingType

# Product Category and Core Product Models

class ProductCategory(BaseModel):
    """Product Category model - equivalent to product.category in Odoo"""

    VALUATION_CHOICES = [
        ('manual_periodic', 'Manual'),
        ('real_time', 'Automated'),
    ]

    COST_METHOD_CHOICES = [
        ('standard', 'Standard Price'),
        ('fifo', 'First In First Out (FIFO)'),
        ('average', 'Average Cost (AVCO)'),
    ]

    name = models.CharField(max_length=255, db_index=True)
    complete_name = models.CharField(max_length=500, blank=True)
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    parent_path = models.CharField(max_length=500, blank=True, db_index=True)

    # Accounting properties (company-dependent in Odoo, simplified here)
    property_valuation = models.CharField(max_length=20, choices=VALUATION_CHOICES,
                                        default='manual_periodic')
    property_cost_method = models.CharField(max_length=20, choices=COST_METHOD_CHOICES,
                                          default='standard')

    # Account configuration
    property_stock_account_input_categ_id = models.ForeignKey(
        AccountAccount, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='category_stock_input', help_text="Stock Input Account")
    property_stock_account_output_categ_id = models.ForeignKey(
        AccountAccount, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='category_stock_output', help_text="Stock Output Account")
    property_stock_valuation_account_id = models.ForeignKey(
        AccountAccount, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='category_stock_valuation', help_text="Stock Valuation Account")

    class Meta:
        verbose_name_plural = "Product Categories"

    def __str__(self):
        return self.complete_name or self.name

    def save(self, *args, **kwargs):
        # Compute complete name
        if self.parent_id:
            self.complete_name = f"{self.parent_id.complete_name} / {self.name}"
            self.parent_path = f"{self.parent_id.parent_path}/{self.parent_id.id}"
        else:
            self.complete_name = self.name
            self.parent_path = ""

        super().save(*args, **kwargs)

class ProductTemplate(BaseModel):
    """Product Template model - equivalent to product.template in Odoo"""

    TYPE_CHOICES = [
        ('consu', 'Consumable'),
        ('service', 'Service'),
        ('product', 'Storable Product'),
    ]

    TRACKING_CHOICES = [
        ('serial', 'By Unique Serial Number'),
        ('lot', 'By Lots'),
        ('none', 'No Tracking'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, db_index=True)
    default_code = models.CharField(max_length=100, blank=True, help_text="Internal Reference")
    barcode = models.CharField(max_length=100, blank=True, null=True, unique=True)

    # Product Type and Category
    detailed_type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='consu')
    categ_id = models.ForeignKey(ProductCategory, on_delete=models.PROTECT,
                                help_text="Product Category")

    # Pricing
    list_price = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                   help_text="Sales Price")
    standard_price = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                       help_text="Cost Price")

    # Units of Measure
    uom_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                              help_text="Unit of Measure")
    uom_po_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                                 related_name='product_template_po',
                                 help_text="Purchase Unit of Measure")

    # Inventory and Tracking
    tracking = models.CharField(max_length=20, choices=TRACKING_CHOICES, default='none')

    # Descriptions
    description = models.TextField(blank=True, help_text="Internal Notes")
    description_sale = models.TextField(blank=True, help_text="Sales Description")
    description_purchase = models.TextField(blank=True, help_text="Purchase Description")
    description_picking = models.TextField(blank=True, help_text="Description on Picking")
    description_pickingout = models.TextField(blank=True, help_text="Description on Delivery Orders")
    description_pickingin = models.TextField(blank=True, help_text="Description on Receptions")

    # Inventory Management
    responsible_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                                     help_text="Responsible Person")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True)

    # Computed quantities (will be computed from stock moves)
    qty_available = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                      help_text="Quantity On Hand")
    virtual_available = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                          help_text="Forecasted Quantity")
    incoming_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Incoming Quantity")
    outgoing_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Outgoing Quantity")

    # Active flag
    active = models.BooleanField(default=True)

    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['default_code']),
            models.Index(fields=['detailed_type']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()

        # Validate tracking for storable products
        if self.detailed_type == 'product' and self.tracking not in ['serial', 'lot', 'none']:
            raise ValidationError("Invalid tracking method for storable product.")

        # Validate UOM consistency
        if self.uom_id and self.uom_po_id:
            if self.uom_id.category_id != self.uom_po_id.category_id:
                raise ValidationError("Sales and Purchase units of measure must be in the same category.")

class Product(BaseModel):
    """Product Variant model - equivalent to product.product in Odoo"""

    # Link to template
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE,
                                       related_name='product_variant_ids')

    # Variant-specific fields
    default_code = models.CharField(max_length=100, blank=True, help_text="Internal Reference")
    barcode = models.CharField(max_length=100, blank=True, null=True, unique=True)

    # Computed quantities (will be computed from stock moves)
    qty_available = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                      help_text="Quantity On Hand")
    virtual_available = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                          help_text="Forecasted Quantity")
    incoming_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Incoming Quantity")
    outgoing_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Outgoing Quantity")

    # Active flag
    active = models.BooleanField(default=True)

    class Meta:
        indexes = [
            models.Index(fields=['product_tmpl_id']),
            models.Index(fields=['default_code']),
        ]

    def __str__(self):
        return self.display_name

    @property
    def display_name(self):
        """Get display name for the product"""
        if self.default_code:
            return f"[{self.default_code}] {self.product_tmpl_id.name}"
        return self.product_tmpl_id.name

    @property
    def name(self):
        """Get product name from template"""
        return self.product_tmpl_id.name

    @property
    def detailed_type(self):
        """Get product type from template"""
        return self.product_tmpl_id.detailed_type

    @property
    def tracking(self):
        """Get tracking method from template"""
        return self.product_tmpl_id.tracking

    @property
    def uom_id(self):
        """Get UOM from template"""
        return self.product_tmpl_id.uom_id

    @property
    def categ_id(self):
        """Get category from template"""
        return self.product_tmpl_id.categ_id


class ProductPackaging(BaseModel):
    """Product Packaging model - equivalent to product.packaging in Odoo"""

    name = models.CharField(max_length=255, help_text="Packaging Name")
    product_id = models.ForeignKey(Product, on_delete=models.CASCADE, null=True, blank=True)
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE)

    # Packaging details
    qty = models.FloatField(default=1.0, help_text="Quantity per Package")
    barcode = models.CharField(max_length=255, blank=True, help_text="Barcode")

    # Sales and Purchase
    sales = models.BooleanField(default=False, help_text="Available in Sales")
    purchase = models.BooleanField(default=False, help_text="Available in Purchase")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT, null=True, blank=True)

    class Meta:
        constraints = [
            # Equivalent to Odoo's positive_qty constraint
            models.CheckConstraint(
                check=models.Q(qty__gt=0),
                name='packaging_positive_qty'
            ),
        ]
        indexes = [
            models.Index(fields=['product_id']),
            models.Index(fields=['product_tmpl_id']),
        ]

    def __str__(self):
        return f"{self.product_tmpl_id.name} - {self.name}"


# Stock Location and Warehouse Models

class StockLocation(BaseModel):
    """Stock Location model - equivalent to stock.location in Odoo"""

    USAGE_CHOICES = [
        ('supplier', 'Vendor Location'),
        ('view', 'View'),
        ('internal', 'Internal Location'),
        ('customer', 'Customer Location'),
        ('inventory', 'Inventory Loss'),
        ('production', 'Production'),
        ('transit', 'Transit Location'),
    ]

    name = models.CharField(max_length=255)
    complete_name = models.CharField(max_length=500, blank=True)
    active = models.BooleanField(default=True)
    usage = models.CharField(max_length=20, choices=USAGE_CHOICES, default='internal', db_index=True)

    # Hierarchy
    location_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                                   related_name='child_locations', help_text="Parent Location")
    parent_path = models.CharField(max_length=500, blank=True, db_index=True)

    # Physical coordinates
    posx = models.IntegerField(default=0, help_text="Corridor (X)")
    posy = models.IntegerField(default=0, help_text="Shelves (Y)")
    posz = models.IntegerField(default=0, help_text="Height (Z)")

    # Barcode and identification
    barcode = models.CharField(max_length=100, blank=True)

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True)

    # Additional information
    comment = models.TextField(blank=True, help_text="Additional Information")

    # Removal strategy
    removal_strategy_id = models.ForeignKey('StockRemovalStrategy', on_delete=models.SET_NULL,
                                          null=True, blank=True)

    # Accounting (for valuation)
    valuation_in_account_id = models.ForeignKey(
        AccountAccount, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='location_valuation_in',
        help_text="Used for real-time inventory valuation")
    valuation_out_account_id = models.ForeignKey(
        AccountAccount, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='location_valuation_out',
        help_text="Used for real-time inventory valuation")

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['barcode', 'company_id'],
                name='barcode_company_uniq',
                condition=models.Q(barcode__isnull=False) & ~models.Q(barcode='')
            )
        ]
        indexes = [
            models.Index(fields=['usage']),
            models.Index(fields=['parent_path']),
        ]

    def __str__(self):
        return self.complete_name or self.name

    def save(self, *args, **kwargs):
        # Compute complete name and parent path
        if self.location_id:
            self.complete_name = f"{self.location_id.complete_name}/{self.name}"
            self.parent_path = f"{self.location_id.parent_path}/{self.location_id.id}"
        else:
            self.complete_name = self.name
            self.parent_path = ""

        super().save(*args, **kwargs)

    def _should_be_valued(self):
        """Check if this location should be valued for accounting"""
        return self.usage == 'internal'

# StockWarehouse is imported from sales.models

class StockRemovalStrategy(BaseModel):
    """Removal Strategy model - equivalent to product.removal in Odoo"""

    name = models.CharField(max_length=255)
    method = models.CharField(max_length=20, choices=[
        ('fifo', 'First In First Out (FIFO)'),
        ('lifo', 'Last In First Out (LIFO)'),
        ('closest', 'Closest Location'),
        ('fefo', 'First Expiry First Out (FEFO)'),
    ], default='fifo')

    def __str__(self):
        return self.name

# StockPickingType is imported from purchases.models

class IrSequence(BaseModel):
    """Sequence model - simplified equivalent to ir.sequence in Odoo"""

    name = models.CharField(max_length=255)
    code = models.CharField(max_length=100)
    prefix = models.CharField(max_length=50, blank=True)
    suffix = models.CharField(max_length=50, blank=True)
    number_next = models.IntegerField(default=1)
    number_increment = models.IntegerField(default=1)
    padding = models.IntegerField(default=4)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

    def next_by_code(self, code):
        """Get next sequence number by code"""
        sequence = IrSequence.objects.filter(code=code, company_id=self.company_id).first()
        if sequence:
            number = sequence.number_next
            sequence.number_next += sequence.number_increment
            sequence.save()
            return f"{sequence.prefix}{str(number).zfill(sequence.padding)}{sequence.suffix}"
        return "NEW"

# Stock Movement Models

class StockPicking(BaseModel):
    """Stock Picking model - equivalent to stock.picking in Odoo"""

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('waiting', 'Waiting Another Operation'),
        ('confirmed', 'Waiting'),
        ('assigned', 'Ready'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ]

    MOVE_TYPE_CHOICES = [
        ('direct', 'As soon as possible'),
        ('one', 'When all products are ready'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, default='New')
    origin = models.CharField(max_length=255, blank=True, help_text="Source Document")

    # Partner and locations
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                   help_text="Source Location")
    location_dest_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                       related_name='picking_dest',
                                       help_text="Destination Location")

    # Type and state
    picking_type_id = models.ForeignKey(StockPickingType, on_delete=models.PROTECT)
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft', db_index=True)
    move_type = models.CharField(max_length=20, choices=MOVE_TYPE_CHOICES, default='direct')

    # Dates
    scheduled_date = models.DateTimeField(default=timezone.now, help_text="Scheduled Date")
    date_done = models.DateTimeField(null=True, blank=True, help_text="Date Done")

    # Company and user
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Responsible")

    # Priority
    priority = models.CharField(max_length=1, choices=[
        ('0', 'Normal'),
        ('1', 'Urgent'),
    ], default='0')

    # Notes
    note = models.TextField(blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['state']),
            models.Index(fields=['scheduled_date']),
            models.Index(fields=['picking_type_id']),
        ]

    def __str__(self):
        return self.name

    def action_confirm(self):
        """Confirm the picking"""
        if self.state != 'draft':
            raise ValidationError("Only draft pickings can be confirmed.")

        self.state = 'confirmed'

        # Generate sequence number if needed
        if self.name == 'New':
            self.name = self._get_sequence_number()

        self.save()

        # Confirm all moves
        for move in self.move_ids.all():
            move._action_confirm()

    def action_assign(self):
        """Check availability and assign picking"""
        if self.state not in ['confirmed', 'waiting']:
            raise ValidationError("Only confirmed pickings can be assigned.")

        # Check if all moves are available
        all_available = True
        for move in self.move_ids.all():
            move._action_assign()
            if move.state != 'assigned':
                all_available = False

        if all_available:
            self.state = 'assigned'
        else:
            self.state = 'confirmed'

        self.save()

    def button_validate(self):
        """Validate the picking"""
        if self.state not in ['assigned', 'confirmed']:
            raise ValidationError("Only assigned pickings can be validated.")

        # Validate all moves
        for move in self.move_ids.all():
            move._action_done()

        self.state = 'done'
        self.date_done = timezone.now()
        self.save()

    def action_cancel(self):
        """Cancel the picking"""
        if self.state == 'done':
            raise ValidationError("Cannot cancel a done picking.")

        # Cancel all moves
        for move in self.move_ids.all():
            move._action_cancel()

        self.state = 'cancel'
        self.save()

    def _get_sequence_number(self):
        """Generate sequence number for the picking"""
        # Fallback sequence generation
        prefix = {
            'incoming': 'IN',
            'outgoing': 'OUT',
            'internal': 'INT',
        }.get(self.picking_type_id.code, 'PICK')

        last_picking = StockPicking.objects.filter(
            picking_type_id=self.picking_type_id,
            scheduled_date__year=self.scheduled_date.year
        ).exclude(name='New').order_by('-name').first()

        if last_picking and last_picking.name:
            try:
                last_num = int(last_picking.name.split('/')[-1])
                return f"{prefix}/{self.scheduled_date.year}/{last_num + 1:05d}"
            except (ValueError, IndexError):
                pass

        return f"{prefix}/{self.scheduled_date.year}/00001"

class StockMove(BaseModel):
    """Stock Move model - equivalent to stock.move in Odoo"""

    STATE_CHOICES = [
        ('draft', 'New'),
        ('waiting', 'Waiting Another Move'),
        ('confirmed', 'Waiting Availability'),
        ('partially_available', 'Partially Available'),
        ('assigned', 'Available'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, help_text="Description")
    sequence = models.IntegerField(default=10)

    # Product and quantities
    product_id = models.ForeignKey(Product, on_delete=models.PROTECT)
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1.0,
                                        help_text="Demand")
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1.0,
                                    help_text="Real Quantity")
    quantity_done = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                      help_text="Quantity Done")
    product_uom = models.ForeignKey(ProductUom, on_delete=models.PROTECT)

    # Locations
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                   help_text="Source Location")
    location_dest_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                       related_name='move_dest',
                                       help_text="Destination Location")

    # Picking and state
    picking_id = models.ForeignKey(StockPicking, on_delete=models.CASCADE,
                                 related_name='move_ids', null=True, blank=True)
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft', db_index=True)

    # Dates
    date = models.DateTimeField(default=timezone.now, help_text="Date")
    date_deadline = models.DateTimeField(null=True, blank=True, help_text="Deadline")

    # Company and partner
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    # Origin and reference
    origin = models.CharField(max_length=255, blank=True)
    reference = models.CharField(max_length=255, blank=True)

    # Move chaining
    move_orig_ids = models.ManyToManyField('self', blank=True, symmetrical=False,
                                         related_name='move_dest_ids',
                                         help_text="Original Moves")

    # Procurement and rules
    group_id = models.ForeignKey('sales.ProcurementGroup', on_delete=models.SET_NULL,
                                null=True, blank=True)
    rule_id = models.ForeignKey('StockRule', on_delete=models.SET_NULL, null=True, blank=True)

    # Additional fields
    description_picking = models.TextField(blank=True)
    priority = models.CharField(max_length=1, choices=[
        ('0', 'Normal'),
        ('1', 'Urgent'),
    ], default='0')

    class Meta:
        indexes = [
            models.Index(fields=['state']),
            models.Index(fields=['date']),
            models.Index(fields=['product_id']),
            models.Index(fields=['picking_id']),
        ]

    def __str__(self):
        return f"{self.product_id.display_name} - {self.product_uom_qty} {self.product_uom.name}"

    def clean(self):
        super().clean()

        # Equivalent to Odoo's _check_uom constraint
        if self.product_id and self.product_uom:
            if self.product_id.uom_id.category_id != self.product_uom.category_id:
                raise ValidationError(
                    f"You cannot perform the move because the unit of measure has a different category "
                    f"as the product unit of measure.\n"
                    f"{self.product_id.display_name} --> Product UoM is {self.product_id.uom_id.name} "
                    f"({self.product_id.uom_id.category_id.name}) - Move UoM is {self.product_uom.name} "
                    f"({self.product_uom.category_id.name})"
                )

    def _action_confirm(self):
        """Confirm the stock move"""
        if self.state != 'draft':
            return

        self.state = 'confirmed'
        self.save()

        # Update product quantities
        self._update_product_quantities()

    def _action_assign(self):
        """Assign/reserve the stock move"""
        if self.state not in ['confirmed', 'waiting']:
            return

        # Check availability
        available_qty = self._get_available_quantity()

        if available_qty >= self.product_uom_qty:
            self.state = 'assigned'
        elif available_qty > 0:
            self.state = 'partially_available'
        else:
            self.state = 'confirmed'

        self.save()

    def _action_done(self):
        """Mark the move as done"""
        if self.state not in ['assigned', 'confirmed', 'partially_available']:
            return

        # Set quantity done if not set
        if self.quantity_done == 0:
            self.quantity_done = self.product_uom_qty

        self.state = 'done'
        self.save()

        # Update stock quantities
        self._update_stock_quantities()

        # Update product quantities
        self._update_product_quantities()

    def _action_cancel(self):
        """Cancel the stock move"""
        if self.state == 'done':
            raise ValidationError("Cannot cancel a done move.")

        self.state = 'cancel'
        self.save()

        # Update product quantities
        self._update_product_quantities()

    def _get_available_quantity(self):
        """Get available quantity for this move"""
        # Simplified - in real implementation would check stock quants
        if self.location_id.usage == 'supplier':
            return self.product_uom_qty  # Supplier locations have unlimited stock
        elif self.location_id.usage == 'internal':
            # Check internal stock - simplified
            return self.product_id.qty_available
        else:
            return Decimal('0.0')

    def _update_stock_quantities(self):
        """Update stock quantities after move"""
        # This would update stock.quant records
        # For now, we'll update product quantities directly
        pass

    def _update_product_quantities(self):
        """Update product computed quantities"""
        # This would recompute product quantities from all moves
        # Simplified implementation
        pass

    class Meta:
        constraints = [
            # Equivalent to Odoo's UOM category constraint
            # Note: This would need to be implemented in clean() method since it involves foreign key relationships
        ]
        indexes = [
            models.Index(fields=['product_id']),
            models.Index(fields=['location_id']),
            models.Index(fields=['location_dest_id']),
            models.Index(fields=['state']),
            models.Index(fields=['date']),
        ]

class StockRule(BaseModel):
    """Stock Rule model - simplified equivalent to stock.rule in Odoo"""

    ACTION_CHOICES = [
        ('pull', 'Pull From'),
        ('push', 'Push To'),
        ('pull_push', 'Pull & Push'),
        ('buy', 'Buy'),
        ('manufacture', 'Manufacture'),
    ]

    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT)
    location_src_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                      related_name='rule_src', null=True, blank=True)
    picking_type_id = models.ForeignKey(StockPickingType, on_delete=models.PROTECT)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

# Stock Quantity and Lot Tracking Models

class StockQuant(BaseModel):
    """Stock Quant model - equivalent to stock.quant in Odoo"""

    # Product and location
    product_id = models.ForeignKey(Product, on_delete=models.PROTECT)
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT)

    # Quantities
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                 help_text="Quantity")
    reserved_quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                          help_text="Reserved Quantity")
    available_quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                           help_text="Available Quantity")

    # Lot and package tracking
    lot_id = models.ForeignKey('StockLot', on_delete=models.PROTECT, null=True, blank=True)
    package_id = models.ForeignKey('StockQuantPackage', on_delete=models.PROTECT,
                                 null=True, blank=True)
    owner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True,
                               help_text="Owner")

    # Dates
    in_date = models.DateTimeField(default=timezone.now, help_text="Incoming Date")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Valuation (for accounting integration)
    value = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                              help_text="Inventory Value")
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['product_id', 'location_id', 'lot_id', 'package_id', 'owner_id'],
                name='stock_quant_unique'
            )
        ]
        indexes = [
            models.Index(fields=['product_id', 'location_id']),
            models.Index(fields=['location_id']),
        ]

    def __str__(self):
        return f"{self.product_id.display_name} @ {self.location_id.name}: {self.quantity}"

    def clean(self):
        super().clean()

        # Equivalent to Odoo's check_quantity for serial numbers
        if (self.product_id and self.product_id.tracking == 'serial' and
            self.lot_id and self.location_id.usage != 'inventory'):
            if abs(self.quantity) > 1:
                raise ValidationError(
                    f"The serial number has already been assigned: \n"
                    f"Product: {self.product_id.display_name}, Serial Number: {self.lot_id.name}"
                )

        # Equivalent to Odoo's check_location_id
        if self.location_id and self.location_id.usage == 'view':
            raise ValidationError(
                f'You cannot take products from or deliver products to a location of type "view" '
                f'({self.location_id.name}).'
            )

        # Equivalent to Odoo's check_lot_id
        if self.lot_id and self.lot_id.product_id and self.lot_id.product_id != self.product_id:
            raise ValidationError(
                f'The Lot/Serial number ({self.lot_id.name}) is linked to another product.'
            )

    def save(self, *args, **kwargs):
        # Compute available quantity
        self.available_quantity = self.quantity - self.reserved_quantity
        super().save(*args, **kwargs)

class StockLot(BaseModel):
    """Stock Lot/Serial Number model - equivalent to stock.lot in Odoo"""

    name = models.CharField(max_length=255, help_text="Lot/Serial Number")
    ref = models.CharField(max_length=255, blank=True, help_text="Internal Reference")

    # Product
    product_id = models.ForeignKey(Product, on_delete=models.PROTECT)

    # Dates
    create_date = models.DateTimeField(auto_now_add=True)
    use_date = models.DateField(null=True, blank=True, help_text="Best before Date")
    removal_date = models.DateField(null=True, blank=True, help_text="Removal Date")
    alert_date = models.DateField(null=True, blank=True, help_text="Alert Date")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Additional information
    note = models.TextField(blank=True)

    class Meta:
        unique_together = [['name', 'product_id', 'company_id']]
        indexes = [
            models.Index(fields=['product_id']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        return f"{self.name} ({self.product_id.display_name})"

    def clean(self):
        super().clean()

        # Equivalent to Odoo's _check_unique_lot constraint
        # This is already handled by unique_together, but we can add additional validation
        if self.name and self.product_id:
            existing = StockLot.objects.filter(
                name=self.name,
                product_id=self.product_id,
                company_id=self.company_id
            ).exclude(pk=self.pk)

            if existing.exists():
                raise ValidationError(
                    f'The combination of serial number and product must be unique across a company.\n'
                    f'Following combination contains duplicates:\n'
                    f' - Product: {self.product_id.display_name}, Serial Number: {self.name}'
                )

class StockQuantPackage(BaseModel):
    """Stock Package model - equivalent to stock.quant.package in Odoo"""

    name = models.CharField(max_length=255, help_text="Package Reference")

    # Package hierarchy
    package_type_id = models.ForeignKey('StockPackageType', on_delete=models.SET_NULL,
                                      null=True, blank=True)
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                                 related_name='child_packages')

    # Location
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT)

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

class StockPackageType(BaseModel):
    """Package Type model - equivalent to stock.package.type in Odoo"""

    name = models.CharField(max_length=255)
    sequence = models.IntegerField(default=1)

    # Dimensions
    height = models.IntegerField(default=0, help_text="Height in mm")
    width = models.IntegerField(default=0, help_text="Width in mm")
    packaging_length = models.IntegerField(default=0, help_text="Length in mm")
    max_weight = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                   help_text="Max Weight in kg")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

# Inventory Adjustment Models

class StockInventory(BaseModel):
    """Stock Inventory model - equivalent to stock.inventory in Odoo"""

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('cancel', 'Cancelled'),
        ('confirm', 'In Progress'),
        ('done', 'Validated'),
    ]

    name = models.CharField(max_length=255)
    date = models.DateTimeField(default=timezone.now)
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')

    # Filters
    location_ids = models.ManyToManyField(StockLocation, blank=True)
    product_ids = models.ManyToManyField(Product, blank=True)
    category_ids = models.ManyToManyField(ProductCategory, blank=True)

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

    def action_start(self):
        """Start the inventory"""
        if self.state != 'draft':
            raise ValidationError("Only draft inventories can be started.")

        self.state = 'confirm'
        self.save()

        # Create inventory lines
        self._create_inventory_lines()

    def action_validate(self):
        """Validate the inventory"""
        if self.state != 'confirm':
            raise ValidationError("Only confirmed inventories can be validated.")

        # Create stock moves for adjustments
        for line in self.line_ids.all():
            if line.difference_qty != 0:
                line._create_stock_move()

        self.state = 'done'
        self.save()

    def _create_inventory_lines(self):
        """Create inventory lines based on current stock"""
        # This would create lines for all products in scope
        # Simplified implementation
        pass

class StockInventoryLine(BaseModel):
    """Stock Inventory Line model - equivalent to stock.inventory.line in Odoo"""

    inventory_id = models.ForeignKey(StockInventory, on_delete=models.CASCADE,
                                   related_name='line_ids')

    # Product and location
    product_id = models.ForeignKey(Product, on_delete=models.PROTECT)
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT)

    # Quantities
    theoretical_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                        help_text="Theoretical Quantity")
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                    help_text="Real Quantity")
    difference_qty = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                       help_text="Difference")

    # Lot tracking
    lot_id = models.ForeignKey(StockLot, on_delete=models.PROTECT, null=True, blank=True)
    package_id = models.ForeignKey(StockQuantPackage, on_delete=models.PROTECT,
                                 null=True, blank=True)

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def save(self, *args, **kwargs):
        # Compute difference
        self.difference_qty = self.product_qty - self.theoretical_qty
        super().save(*args, **kwargs)

    def _create_stock_move(self):
        """Create stock move for inventory adjustment"""
        if self.difference_qty == 0:
            return

        # Determine source and destination locations
        if self.difference_qty > 0:
            # Positive adjustment - from inventory location to stock
            location_id = self._get_inventory_loss_location()
            location_dest_id = self.location_id
        else:
            # Negative adjustment - from stock to inventory location
            location_id = self.location_id
            location_dest_id = self._get_inventory_loss_location()

        # Create stock move
        move = StockMove.objects.create(
            name=f"Inventory Adjustment: {self.product_id.display_name}",
            product_id=self.product_id,
            product_uom_qty=abs(self.difference_qty),
            product_qty=abs(self.difference_qty),
            quantity_done=abs(self.difference_qty),
            product_uom=self.product_id.uom_id,
            location_id=location_id,
            location_dest_id=location_dest_id,
            state='done',
            company_id=self.company_id,
            origin=f"Inventory: {self.inventory_id.name}",
            create_uid=self.create_uid,
            write_uid=self.write_uid
        )

        return move

    def _get_inventory_loss_location(self):
        """Get inventory adjustment location"""
        # Find or create inventory loss location
        location = StockLocation.objects.filter(
            usage='inventory',
            company_id=self.company_id
        ).first()

        if not location:
            location = StockLocation.objects.create(
                name='Inventory adjustment',
                usage='inventory',
                company_id=self.company_id,
                create_uid=self.create_uid,
                write_uid=self.write_uid
            )

        return location

    def __str__(self):
        return f"{self.product_id.display_name} @ {self.location_id.name}"
