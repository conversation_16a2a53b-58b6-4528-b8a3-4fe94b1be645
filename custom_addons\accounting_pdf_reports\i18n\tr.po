# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* accounting_pdf_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 06:34+0000\n"
"PO-Revision-Date: 2022-04-15 06:34+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid ": General ledger"
msgstr ": B<PERSON><PERSON>ük <PERSON>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid ": Trial Balance"
msgstr ": <PERSON><PERSON><PERSON><PERSON> mizan\n"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Analytic Accounts:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Company:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Display Account</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Journal:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Journals:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Purchase</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Sorted By:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Total</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Account"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "Hesap Eski Deneme bakiyesi Raporu\n"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_account_report
msgid "Account Common Account Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_print_journal
msgid "Account Print Journal"
msgstr "Yevmiye Hesap Yazdırma"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_financial_report
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__account_report_id
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_reports
msgid "Account Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Account Total"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_type
msgid "Account Type"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_accounting_report
msgid "Accounting Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__account_ids
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__accounts
msgid "Accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_balance_view
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_aged_partner_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_trial_balance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Eski İş Ortağı Bakiyesi\n"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr "Yaşlı İş Ortağı Bakiyesi Raporu\n"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_payable
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_payable
msgid "Aged Payable"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_receivable
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_receivable
msgid "Aged Receivable"
msgstr "Yaşlı Alacak\n"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__all
msgid "All"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Entries"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Posted Entries"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "All accounts'"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Analytic Account"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__analytic_account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__analytic_account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__analytic_account_ids
msgid "Analytic Accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_assets0
msgid "Assets"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_audit_reports
msgid "Audit Reports"
msgstr "Denetim Raporları\n"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__0
msgid "Automatic formatting"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Balance"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_bs
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_bs
msgid "Balance Sheet"
msgstr "Bilanço"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Base Amount"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_tax_report_view
msgid "Cancel"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__children_ids
msgid "Children"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
msgid "Childrens"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Code"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__label_filter
msgid "Column Label"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__company_id
msgid "Company"
msgstr "Şirket"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Comparison"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Credit"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Currency"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_date
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Date"
msgstr "Tarih"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from_cmp
msgid "Date From"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to_cmp
msgid "Date To"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Date:"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Dates"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Debit"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_account
msgid "Display Accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__debit_credit
msgid "Display Debit/Credit Columns"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_flat
msgid "Display children flat"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_with_hierarchy
msgid "Display children with hierarchy"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_detail
msgid "Display details"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Drill Down Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Dynamic Accounting Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__enable_filter
msgid "Enable Comparison"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Enhanced Financial Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Kayıtları Sıralama"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Entry Label"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Excel Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_expense0
msgid "Expense"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__filter_cmp
msgid "Filter by"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_financial
msgid "Financial Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid "Financial Report Style"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_financial_report_tree
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_financial
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_legal_statement
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_reports_settings
msgid "Financial Reports"
msgstr "Finansal raporlar\n"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Financial Reports in Excel"
msgstr "Finansal raporlar Excel"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#: code:addons/accounting_pdf_reports/report/report_financial.py:0
#: code:addons/accounting_pdf_reports/report/report_general_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_journal.py:0
#: code:addons/accounting_pdf_reports/report/report_partner_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_tax.py:0
#: code:addons/accounting_pdf_reports/report/report_trial_balance.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_general_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_general_ledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_ledger
msgid "General Ledger"
msgstr "Büyük Defter"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_general_ledger
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_general_ledger
msgid "General Ledger Report"
msgstr "Büyük Defter Raporu"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Group By"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__id
msgid "ID"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid "Include Initial Balances"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_income0
msgid "Income"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__5
msgid "Italic Text (smaller)"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "JRNL"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_journal
msgid "Journal Audit Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal Entry Number"
msgstr "Yevmiye Kayıt Numarası"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Journal and Partner"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Journal:"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__journal_ids
msgid "Journals"
msgstr "Yevmiyeler"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_print_journal_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_print_journal
msgid "Journals Audit"
msgstr "Yevmiye Denetimi"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal_entries
msgid "Journals Entries"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Label"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__level
msgid "Level"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liability0
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liabilitysum0
msgid "Liability"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__1
msgid "Main Title 1 (bold, underlined)"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Move"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Name"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Net"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_no
msgid "No Filters"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__no_detail
msgid "No detail"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__4
msgid "Normal Text"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__parent_id
msgid "Parent"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Parent Report"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Partner"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_partner_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_partnerledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_partner_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Partner Ledger"
msgstr "İş Ortağı Defteri"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_partnerledger
msgid "Partner Ledger Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_partner_reports
msgid "Partner Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__result_selection
msgid "Partner's"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Partner:"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__partner_ids
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Partners"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Payable Accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__period_length
msgid "Period Length (days)"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__1
msgid "Preserve balance sign"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Preview financial reports without downloading"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_tax_report_view
msgid "Print"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Eğer para birimi, şirket para biriminden farklıysa Raporu para birimi "
"sütununu içerecek şekilde yazdırın."

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_pl
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_pl
msgid "Profit and Loss"
msgstr "kâr ve zarar"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable Accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer_supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__reconciled
msgid "Reconciled Entries"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Ref"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Reference:"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
msgid "Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__name
msgid "Report Name"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_tax_report_view
msgid "Report Options"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Report Type"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_report_id
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_report
msgid "Report Value"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__-1
msgid "Reverse balance sign"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Sale"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sequence
msgid "Sequence"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sign
msgid "Sign on Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__6
msgid "Smallest Text"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__sortby
msgid "Sort by"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__target_move
msgid "Target Moves"
msgstr "Hedef Hareketler"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Amount"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Declaration"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_account_tax
#: model:ir.model,name:accounting_pdf_reports.model_account_tax_report_wizard
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_tax
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax Report"
msgstr "Vergi Rapor"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_tax_report
msgid "Tax Reports"
msgstr "Vergi Raporları"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__2
msgid "Title 2 (bold)"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__3
msgid "Title 3 (bold, smaller)"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Total"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_balance_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_trial_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_balance_report
msgid "Trial Balance"
msgstr "Geçici mizan\n"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_balance_report
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_trialbalance
msgid "Trial Balance Report"
msgstr "Geçici mizan Rapor\n"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__type
msgid "Type"
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#, python-format
msgid "Unknown Partner"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__sum
msgid "View"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__amount_currency
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid "With Currency"
msgstr "Para Birimi ile"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With balance not equal to zero"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__movement
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With movements"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/account_general_ledger.py:0
#, python-format
msgid "You must define a Start Date"
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#, python-format
msgid "You must set a period length greater than 0."
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#, python-format
msgid "You must set a start date."
msgstr ""
