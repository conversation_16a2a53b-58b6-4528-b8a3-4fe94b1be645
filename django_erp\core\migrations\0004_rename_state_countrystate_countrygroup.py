# Generated by Django 4.2.21 on 2025-07-20 09:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0003_accountpaymentterm_accountfiscalposition'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0003_country_phone_code'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='State',
            new_name='CountryState',
        ),
        migrations.CreateModel(
            name='CountryGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('country_ids', models.ManyToManyField(blank=True, to='core.country')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
