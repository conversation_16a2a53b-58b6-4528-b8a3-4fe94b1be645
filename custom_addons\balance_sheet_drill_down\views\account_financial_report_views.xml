<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit the existing financial report template to add drill-down -->
        <template id="report_financial_drill_down" inherit_id="base_accounting_kit.report_financial">

            <!-- Add drill-down to Balance column in debit/credit table -->
            <xpath expr="//table[@t-if='data[&quot;form&quot;][&quot;debit_credit&quot;] == 1']//td[@class='text-end'][3]//span" position="attributes">
                <attribute name="class">drill-down-amount</attribute>
                <attribute name="data-account-id" t-att-data-account-id="a.get('account_id', '')"/>
                <attribute name="data-account-code" t-att-data-account-code="a.get('account_code', '')"/>
                <attribute name="data-account-name" t-att-data-account-name="a.get('name', '')"/>
                <attribute name="data-amount" t-att-data-amount="a.get('balance', 0)"/>
                <attribute name="data-date-from" t-att-data-date-from="data['form']['date_from']"/>
                <attribute name="data-date-to" t-att-data-date-to="data['form']['date_to']"/>
                <attribute name="style">cursor: pointer; color: #007cba;</attribute>
                <attribute name="title">Click to drill down to account details</attribute>
            </xpath>

            <!-- Add drill-down to Balance column in simple balance table -->
            <xpath expr="//table[@t-if='not data[&quot;form&quot;][&quot;enable_filter&quot;] and not data[&quot;form&quot;][&quot;debit_credit&quot;]']//td[@class='text-end']//span" position="attributes">
                <attribute name="class">drill-down-amount</attribute>
                <attribute name="data-account-id" t-att-data-account-id="a.get('account_id', '')"/>
                <attribute name="data-account-code" t-att-data-account-code="a.get('account_code', '')"/>
                <attribute name="data-account-name" t-att-data-account-name="a.get('name', '')"/>
                <attribute name="data-amount" t-att-data-amount="a.get('balance', 0)"/>
                <attribute name="data-date-from" t-att-data-date-from="data['form']['date_from']"/>
                <attribute name="data-date-to" t-att-data-date-to="data['form']['date_to']"/>
                <attribute name="style">cursor: pointer; color: #007cba;</attribute>
                <attribute name="title">Click to drill down to account details</attribute>
            </xpath>

            <!-- Add drill-down to Balance column in comparative table -->
            <xpath expr="//table[@t-if='data[&quot;form&quot;][&quot;enable_filter&quot;] == 1 and not data[&quot;form&quot;][&quot;debit_credit&quot;]']//td[@class='text-end'][1]//span" position="attributes">
                <attribute name="class">drill-down-amount</attribute>
                <attribute name="data-account-id" t-att-data-account-id="a.get('account_id', '')"/>
                <attribute name="data-account-code" t-att-data-account-code="a.get('account_code', '')"/>
                <attribute name="data-account-name" t-att-data-account-name="a.get('name', '')"/>
                <attribute name="data-amount" t-att-data-amount="a.get('balance', 0)"/>
                <attribute name="data-date-from" t-att-data-date-from="data['form']['date_from']"/>
                <attribute name="data-date-to" t-att-data-date-to="data['form']['date_to']"/>
                <attribute name="style">cursor: pointer; color: #007cba;</attribute>
                <attribute name="title">Click to drill down to account details</attribute>
            </xpath>

        </template>

        <!-- Enhanced Balance Sheet Report Action -->
        <record id="action_balance_sheet_drill_down" model="ir.actions.act_window">
            <field name="name">Balance Sheet (with Drill-Down)</field>
            <field name="res_model">financial.report</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="base_accounting_kit.financial_report_wiz_modified"/>
            <field name="target">new</field>
            <field name="context" eval="{'default_account_report_id':ref('base_accounting_kit.account_financial_report_balancesheet0')}"/>
        </record>

        <!-- Menu Item -->
        <menuitem 
            id="menu_balance_sheet_drill_down" 
            sequence="3"
            name="Balance Sheet (Drill-Down)"
            parent="base_accounting_kit.account_reports_generic_statements"
            action="action_balance_sheet_drill_down"/>

        <!-- Account Move Line Tree View for Drill-Down -->
        <record id="view_move_line_drill_down_tree" model="ir.ui.view">
            <field name="name">account.move.line.drill.down.tree</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <tree string="Journal Entries" create="false" edit="false">
                    <field name="date"/>
                    <field name="move_id"/>
                    <field name="journal_id"/>
                    <field name="account_id"/>
                    <field name="partner_id"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="debit" sum="Total Debit"/>
                    <field name="credit" sum="Total Credit"/>
                    <field name="balance" sum="Total Balance"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="amount_currency" groups="base.group_multi_currency"/>
                </tree>
            </field>
        </record>

        <!-- Account Move Line Form View for Drill-Down -->
        <record id="view_move_line_drill_down_form" model="ir.ui.view">
            <field name="name">account.move.line.drill.down.form</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <form string="Journal Entry" create="false" edit="false">
                    <sheet>
                        <group>
                            <group>
                                <field name="date"/>
                                <field name="move_id"/>
                                <field name="journal_id"/>
                                <field name="account_id"/>
                                <field name="partner_id"/>
                            </group>
                            <group>
                                <field name="name"/>
                                <field name="ref"/>
                                <field name="debit"/>
                                <field name="credit"/>
                                <field name="balance"/>
                                <field name="currency_id" groups="base.group_multi_currency"/>
                                <field name="amount_currency" groups="base.group_multi_currency"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Action for Account Move Lines Drill-Down -->
        <record id="action_account_move_line_drill_down" model="ir.actions.act_window">
            <field name="name">Account Entries</field>
            <field name="res_model">account.move.line</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_move_line_drill_down_tree"/>
            <field name="context">{
                'search_default_account_id': active_id,
                'default_account_id': active_id,
            }</field>
            <field name="domain">[]</field>
        </record>

    </data>
</odoo>
