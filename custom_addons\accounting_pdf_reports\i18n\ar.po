# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* accounting_pdf_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-12-11 14:51+0000\n"
"PO-Revision-Date: 2022-05-21 13:26+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"X-Generator: Poedit 3.0.1\n"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid ": General ledger"
msgstr ": دفتر الأستاذ العام"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid ": Trial Balance"
msgstr ": ميزان المراجعة"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>غير مستحق</span>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Company:</strong>"
msgstr "<strong>شركة:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>التاريخ من :</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>تاريخ ل :</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>عرض الحساب:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>عرض الحساب</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>إدخالات مرتبة حسب:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>مجلة:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>المجلات:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>شريك:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>طول الفترة (أيام)</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>شراء</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>مرتبة حسب:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>تاريخ البدء:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>تحركات الهدف:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>مجموع</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Account"
msgstr "حساب"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "تقرير ميزان المراجعة المسن في الحساب"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "تقرير الحساب المشترك"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "تقرير الشريك المشترك للحساب"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "حساب شريك الأستاذ"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_print_journal
msgid "Account Print Journal"
msgstr "مجلة طباعة الحساب"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_financial_report
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__children_ids
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr "تقرير الحساب"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__account_report_id
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_reports
msgid "Account Reports"
msgstr "تقارير الحساب"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Account Total"
msgstr "إجمالي الحساب"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_type
msgid "Account Type"
msgstr "نوع الحساب"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_accounting_report
msgid "Accounting Report"
msgstr "تقرير محاسبي"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_ids
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__accounts
msgid "Accounts"
msgstr "حسابات"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_balance_view
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_aged_partner_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_trial_balance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "رصيد الشريك المسن"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__all
msgid "All"
msgstr "الجميع"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Entries"
msgstr "كل المقالات"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Posted Entries"
msgstr "جميع المشاركات المنشورة"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All accounts"
msgstr "جميع الحسابات"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "All accounts'"
msgstr "جميع الحسابات'"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_assets0
msgid "Assets"
msgstr "أصول"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__0
msgid "Automatic formatting"
msgstr "تنسيق تلقائي"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Balance"
msgstr "توازن"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_bs
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_bs
msgid "Balance Sheet"
msgstr "ورقة التوازن"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Base Amount"
msgstr "كمية أساسية"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
msgid "Cancel"
msgstr "يلغي"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Code"
msgstr "رمز"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__label_filter
msgid "Column Label"
msgstr "تسمية العمود"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__company_id
msgid "Company"
msgstr "شركة"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Comparison"
msgstr "مقارنة"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_uid
msgid "Created by"
msgstr "انشأ من قبل"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_date
msgid "Created on"
msgstr "تم إنشاؤها على"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Credit"
msgstr "تنسب إليه"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Currency"
msgstr "عملة"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_date
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Date"
msgstr "تاريخ"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Dates"
msgstr "تواريخ"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Debit"
msgstr "مدين"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_account
msgid "Display Accounts"
msgstr "عرض الحسابات"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__debit_credit
msgid "Display Debit/Credit Columns"
msgstr "عرض أعمدة الخصم / الائتمان"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_financial__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_general_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_journal__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_partnerledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_tax__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_trialbalance__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_flat
msgid "Display children flat"
msgstr "عرض الأطفال مسطح"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_with_hierarchy
msgid "Display children with hierarchy"
msgstr "عرض الأطفال مع التسلسل الهرمي"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_detail
msgid "Display details"
msgstr "عرض التفاصيل"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__enable_filter
msgid "Enable Comparison"
msgstr "تمكين المقارنة"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to_cmp
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "إدخالات مرتبة حسب"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Entry Label"
msgstr "بطاقة الدخول"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_expense0
msgid "Expense"
msgstr "مصروف"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__filter_cmp
msgid "Filter by"
msgstr "مصنف بواسطة"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid "Financial Report Style"
msgstr "نمط التقرير المالي"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_reports_settings
msgid "Financial Reports"
msgstr "تقارير مالية"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_financial
msgid "Financial report"
msgstr "تقرير مالي"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"للحسابات التي يتم خصمها عادةً أكثر من الدائن وأنك ترغب في طباعتها كمبالغ "
"سالبة في تقاريرك ، يجب عليك عكسها علامة التوازن على سبيل المثال: حساب "
"المصاريف. الأمر نفسه ينطبق على الحسابات التي يتم إيداعها عادةً أكثر من الخصم "
"والتي تريدها ترغب في طباعة مبالغ موجبة في تقاريرك ؛ على سبيل المثال: حساب "
"الدخل."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#: code:addons/accounting_pdf_reports/report/report_financial.py:0
#: code:addons/accounting_pdf_reports/report/report_general_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_journal.py:0
#: code:addons/accounting_pdf_reports/report/report_partner_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_tax.py:0
#: code:addons/accounting_pdf_reports/report/report_trial_balance.py:0
#: code:addons/report/report_aged_partner.py:0
#: code:addons/report/report_financial.py:0
#: code:addons/report/report_general_ledger.py:0
#: code:addons/report/report_journal.py:0
#: code:addons/report/report_partner_ledger.py:0
#: code:addons/report/report_tax.py:0
#: code:addons/report/report_trial_balance.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "محتوى النموذج مفقود ، لا يمكن طباعة هذا التقرير."

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_general_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_general_ledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_ledger
msgid "General Ledger"
msgstr "دفتر الأستاذ العام"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_general_ledger
msgid "General Ledger Report"
msgstr "تقرير دفتر الأستاذ العام"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Group By"
msgstr "مجموعة من"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_financial__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_general_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_journal__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_partnerledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_tax__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_trialbalance__id
msgid "ID"
msgstr "بطاقة تعريف"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"إذا قمت بتحديد التاريخ ، فإن هذا الحقل يسمح لك بإضافة صف لعرض ملف مبلغ "
"الخصم / الائتمان / الرصيد الذي يسبق الفلتر الذي قمت بتعيينه."

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid "Include Initial Balances"
msgstr "قم بتضمين الأرصدة الأولية"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_income0
msgid "Income"
msgstr "دخل"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""
"يقوم بإضافة عمود العملة في التقرير إذا كانت العملة تختلف عن عملة الشركة."

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__5
msgid "Italic Text (smaller)"
msgstr "نص مائل (أصغر)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "JRNL"
msgstr "JRNL"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal"
msgstr "مجلة"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr "مجلة وشريك"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal Entry Number"
msgstr "رقم إدخال دفتر اليومية"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Journal and Partner"
msgstr "مجلة وشريك"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__journal_ids
msgid "Journals"
msgstr "المجلات"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_print_journal_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_print_journal
msgid "Journals Audit"
msgstr "Auditoría de Libros"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Label"
msgstr "ملصق"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_financial____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_general_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_journal____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_partnerledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_tax____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_trialbalance____last_update
msgid "Last Modified on"
msgstr "تاريخ آخر تعديل"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__level
msgid "Level"
msgstr "مستوى"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liability0
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "مسؤلية"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__1
msgid "Main Title 1 (bold, underlined)"
msgstr "العنوان الرئيسي 1 (غامق ، مسطر)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Move"
msgstr "يتحرك"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Name"
msgstr "اسم"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Net"
msgstr "شبكة"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_no
msgid "No Filters"
msgstr "لا المرشحات"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__no_detail
msgid "No detail"
msgstr "بدون تفاصيل"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__4
msgid "Normal Text"
msgstr "Texto Normal"

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "تقارير PDF"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__parent_id
msgid "Parent"
msgstr "الأبوين"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Parent Report"
msgstr "Reporte Padre"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Partner"
msgstr "شريك"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_partner_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_partnerledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_partner_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Partner Ledger"
msgstr "شريك ليدجر"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__result_selection
msgid "Partner's"
msgstr "شريك"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Partners"
msgstr "شركاء"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Payable Accounts"
msgstr "حسابات الدفع"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__period_length
msgid "Period Length (days)"
msgstr "طول الفترة (أيام)"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__1
msgid "Preserve balance sign"
msgstr "حفظ علامة التوازن"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
msgid "Print"
msgstr "Imprimir"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr "اطبع التقرير بعمود العملة إذا كانت العملة تختلف عن عملة الشركة."

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "الربح (الخسارة) للتقرير"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_pl
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_pl
msgid "Profit and Loss"
msgstr "الربح والخسارة"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable Accounts"
msgstr "حسابات القبض"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer_supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "حسابات القبض والذمم الدائنة"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__reconciled
msgid "Reconciled Entries"
msgstr "إدخالات تمت تسويتها"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Ref"
msgstr "المرجع"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
msgid "Report"
msgstr "نقل"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__name
msgid "Report Name"
msgstr "تقرير اسم"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
msgid "Report Options"
msgstr "خيارات التقرير"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Report Type"
msgstr "نوع التقرير"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_report_id
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_report
msgid "Report Value"
msgstr "قيمة التقرير"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__-1
msgid "Reverse balance sign"
msgstr "علامة التوازن العكسي"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Sale"
msgstr "تخفيض السعر"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sequence
msgid "Sequence"
msgstr "تسلسل"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sign
msgid "Sign on Reports"
msgstr "تسجيل الدخول التقارير"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__6
msgid "Smallest Text"
msgstr "أصغر نص"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__sortby
msgid "Sort by"
msgstr "ترتيب حسب"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from_cmp
msgid "Start Date"
msgstr "تاريخ البدء"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__target_move
msgid "Target Moves"
msgstr "تحركات الهدف"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax"
msgstr "ضريبة"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Amount"
msgstr "قيمة الضريبة"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Declaration"
msgstr "الإقرار الضريبي"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_account_tax
#: model:ir.model,name:accounting_pdf_reports.model_account_tax_report
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax Report"
msgstr "تقرير الضرائب"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_tax_report
msgid "Tax Reports"
msgstr "تقارير الضرائب"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""
"سيتم عرض هذه التسمية في التقرير لإظهار الرصيد المحسوب لـ مرشح مقارنة معين."

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"يتيح لك هذا الخيار الحصول على مزيد من التفاصيل حول طريقة أرصدةك محسوب. نظرًا "
"لأنها تستهلك مساحة ، فنحن لا نسمح باستخدامها أثناء إجراء مقارنة."

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__2
msgid "Title 2 (bold)"
msgstr "العنوان 2 (غامق)"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__3
msgid "Title 3 (bold, smaller)"
msgstr "العنوان 3 (غامق ، أصغر)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Total"
msgstr "مجموع"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_balance_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_trial_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_Balance_report
msgid "Trial Balance"
msgstr "ميزان المراجعة"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_balance_report
msgid "Trial Balance Report"
msgstr "تقرير ميزان المراجعة"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__type
msgid "Type"
msgstr "اكتب"

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#: code:addons/report/report_aged_partner.py:0
#, python-format
msgid "Unknown Partner"
msgstr "شريك غير معروف"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__sum
msgid "View"
msgstr "رأي"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__amount_currency
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid "With Currency"
msgstr "بالعملة"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "مع رصيد لا يساوي 0"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With balance not equal to zero"
msgstr "مع رصيد لا يساوي الصفر"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__movement
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With movements"
msgstr "مع الحركات"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"يمكنك هنا إعداد التنسيق الذي تريده لعرض هذا السجل. اذا أنت اترك التنسيق "
"التلقائي ، فسيتم حسابه بناءً على البيانات المالية التسلسل الهرمي للتقارير "
"(\"مستوى\" الحقل المحسوب تلقائيًا)."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/account_general_ledger.py:0
#: code:addons/wizard/account_general_ledger.py:0
#, python-format
msgid "You must define a Start Date"
msgstr "يجب عليك تحديد تاريخ البدء"

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#: code:addons/wizard/aged_partner.py:0
#, python-format
msgid "You must set a period length greater than 0."
msgstr "يجب عليك تعيين طول فترة أكبر من 0."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#: code:addons/wizard/aged_partner.py:0
#, python-format
msgid "You must set a start date."
msgstr "يجب عليك تحديد تاريخ البدء."

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_agedpartnerbalance
msgid "report.accounting_pdf_reports.report_agedpartnerbalance"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_financial
msgid "report.accounting_pdf_reports.report_financial"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_general_ledger
msgid "report.accounting_pdf_reports.report_general_ledger"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_journal
msgid "report.accounting_pdf_reports.report_journal"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_partnerledger
msgid "report.accounting_pdf_reports.report_partnerledger"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_tax
msgid "report.accounting_pdf_reports.report_tax"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_trialbalance
msgid "report.accounting_pdf_reports.report_trialbalance"
msgstr ""
