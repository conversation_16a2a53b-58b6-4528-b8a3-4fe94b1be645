# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_accountant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-07 07:33+0000\n"
"PO-Revision-Date: 2022-07-07 07:33+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_group_action
#: model:ir.ui.menu,name:om_account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Групи рахунків"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_account_tag
#: model:ir.ui.menu,name:om_account_accountant.menu_account_tag
msgid "Account Tags"
msgstr "Теги рахунків"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.account_type_menu
msgid "Account Types"
msgstr "Типи рахунків"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_partner_property_form
msgid "Accounting"
msgstr ""

#. module: om_account_accountant
#: model:res.groups,name:om_account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Anglo-Saxon Accounting"
msgstr ""

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_bank
msgid "Bank Statements"
msgstr "Банківські виписки"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_bank_and_cash
msgid "Bank and Cash"
msgstr "Банк та каса"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_change_lock_date
msgid "Cancel"
msgstr "Скасувати"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_cash
msgid "Cash Registers"
msgstr "Касові апарати"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_change_lock_date
msgid "Change Lock Date"
msgstr ""

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_coa_template
msgid "Chart of Accounts Templates"
msgstr "Шаблони планів рахунків"

#. module: om_account_accountant
#: model_terms:ir.actions.act_window,help:om_account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__company_id
msgid "Company"
msgstr ""

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__create_uid
msgid "Created by"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__create_date
msgid "Created on"
msgstr ""

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:om_account_accountant.field_account_move__display_name
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__display_name
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__display_name
msgid "Display Name"
msgstr "Відобразити назву"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "Дата закінчення"

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Кінцева дата, яка входиь до фіскального року."

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Fiscal Period Closing"
msgstr ""

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_fiscal_position_template
#: model:ir.ui.menu,name:om_account_accountant.menu_account_fiscal_position_template
msgid "Fiscal Position Templates"
msgstr "Шаблони схем оподаткування"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_account_fiscal_year
#: model:ir.ui.menu,name:om_account_accountant.menu_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr ""

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__group_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr ""

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Group By"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:om_account_accountant.field_account_move__id
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__id
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__id
msgid "ID"
msgstr ""

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:om_account_accountant.field_account_move____last_update
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date____last_update
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings____last_update
msgid "Last Modified on"
msgstr "Останні зміни"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__write_uid
msgid "Last Updated by"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__write_date
msgid "Last Updated on"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date"
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Дата закриття для всіх користувачів"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__period_lock_date
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Дата закриття для користувачів без ролі Консультант"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Дати закриття періодів"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_view_change_lock_date
msgid "Lock your Fiscal Period"
msgstr "Закрийте свій податковий період"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Lock your fiscal period"
msgstr "Закрийте свій податковий період"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__name
msgid "Name"
msgstr "Назва"

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_change_lock_date__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_change_lock_date__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_change_lock_date__period_lock_date
msgid ""
"Only users with the Adviser role can edit accounts prior to and inclusive of"
" this date. Use it for period locking inside an open fiscal year."
msgstr ""

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Method"
msgstr "Спосіб оплати"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_payment_method
#: model:ir.ui.menu,name:om_account_accountant.menu_account_payment_method
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_tree
msgid "Payment Methods"
msgstr "Спосіб оплати"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Type"
msgstr "Тип оплати"

#. module: om_account_accountant
#: model:ir.actions.server,name:om_account_accountant.action_account_reconciliation
msgid "Reconcile"
msgstr ""

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid ""
"Record the cost of a good as an expense when this good is\n"
"                                    invoiced to a final customer (instead of recording the cost as soon\n"
"                                    as the product is received in stock)."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid ""
"Record the cost of a good as an expense when this good is invoiced to a "
"final customer."
msgstr ""

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_change_lock_date
msgid "Save"
msgstr "Зберегти"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "Дата початку"

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_change_lock_date__tax_lock_date
msgid "Tax Lock Date"
msgstr ""

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_templates
msgid "Templates"
msgstr "Шаблони"

#. module: om_account_accountant
#: code:addons/om_account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr ""

#. module: om_account_accountant
#: code:addons/om_account_accountant/wizard/change_lock_date.py:0
#, python-format
msgid "You Are Not Allowed To Perform This Operation"
msgstr ""

#. module: om_account_accountant
#: code:addons/om_account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
