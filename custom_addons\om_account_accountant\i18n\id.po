# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_accountant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-09 12:27+0000\n"
"PO-Revision-Date: 2024-11-09 12:27+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_group_action
#: model:ir.ui.menu,name:om_account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Grup Akun"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_account_tag
#: model:ir.ui.menu,name:om_account_accountant.menu_account_tag
msgid "Account Tags"
msgstr "Tag Akun"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_partner_property_form
msgid "Accounting"
msgstr "Akuntansi"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Anglo-Saxon Accounting"
msgstr "Akuntansi Anglo-Saxon"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_bank
msgid "Bank Statements"
msgstr "Laporan Bank"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_bank_and_cash
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_bank_cash
msgid "Bank and Cash"
msgstr "Bank dan Uang Tunai"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_cash
msgid "Cash Registers"
msgstr "Mesin Kasir"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Group By"
msgstr "Kelompokkan Berdasarkan"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_finance_entries_accounting_journals
msgid "Journals"
msgstr "Jurnal"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_misc
msgid "Miscellaneous"
msgstr "Lain-Lain"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Method"
msgstr "Metode Pembayaran"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_payment_method
#: model:ir.ui.menu,name:om_account_accountant.menu_account_payment_method
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_tree
msgid "Payment Methods"
msgstr "Metode Pembayaran"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Type"
msgstr "Tipe Pembayaran"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_purchase
msgid "Purchases"
msgstr "Pembelian"

#. module: om_account_accountant
#: model:ir.actions.server,name:om_account_accountant.action_account_reconciliation
msgid "Reconcile"
msgstr "Penyesuaian"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid ""
"Record the cost of a good as an expense when this good is\n"
"                                invoiced to a final customer (instead of recording the cost as soon\n"
"                                as the product is received in stock)."
msgstr ""
"Catat harga pokok suatu barang sebagai pengeluaran ketika barang tersebut\n"
"                                ditagihkan ke pelanggan akhir (alih-alih mencatat biayanya\n"
"                                segera setelah produk diterima dalam stok)."

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid ""
"Record the cost of a good as an expense when this good is invoiced to a "
"final customer."
msgstr ""
"Catat harga suatu barang sebagai beban ketika barang tersebut ditagihkan "
"ke pelanggan akhir."

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_action_account_moves_journal_sales
msgid "Sales"
msgstr "Penjualan"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_templates
msgid "Templates"
msgstr "Templat"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Gunakan akuntansi Anglo-Saxon"
