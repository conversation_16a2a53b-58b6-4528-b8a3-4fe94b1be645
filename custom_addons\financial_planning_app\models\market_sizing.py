# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date


class MarketSizing(models.Model):
    _name = 'financial.planning.market.sizing'
    _description = 'Market Sizing Analysis'
    _order = 'name, date_analysis desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Market Analysis Name', required=True)
    description = fields.Text(string='Description')
    date_analysis = fields.Date(string='Analysis Date', required=True, default=fields.Date.today)
    
    # Geographic scope
    continent_ids = fields.Many2many('financial.planning.continent', 'market_sizing_continent_rel', 'market_id', 'continent_id', string='Target Continents')
    country_ids = fields.Many2many('financial.planning.country', 'market_sizing_country_rel', 'market_id', 'country_id', string='Target Countries')
    city_ids = fields.Many2many('financial.planning.city', 'market_sizing_city_rel', 'market_id', 'city_id', string='Target Cities')
    
    # Market definition
    industry_sector = fields.Selection([
        ('fintech', 'Financial Technology'),
        ('ecommerce', 'E-commerce'),
        ('healthcare', 'Healthcare'),
        ('education', 'Education Technology'),
        ('entertainment', 'Entertainment & Media'),
        ('food_delivery', 'Food Delivery'),
        ('transportation', 'Transportation & Mobility'),
        ('real_estate', 'Real Estate Technology'),
        ('retail', 'Retail & Consumer Goods'),
        ('b2b_services', 'B2B Services'),
        ('other', 'Other')
    ], string='Industry Sector', required=True)
    
    product_category = fields.Char(string='Product/Service Category', required=True)
    target_customer_segment = fields.Selection([
        ('b2c_mass', 'B2C Mass Market'),
        ('b2c_premium', 'B2C Premium'),
        ('b2c_budget', 'B2C Budget'),
        ('b2b_sme', 'B2B Small & Medium Enterprises'),
        ('b2b_enterprise', 'B2B Enterprise'),
        ('b2b_government', 'B2B Government'),
        ('mixed', 'Mixed B2B/B2C')
    ], string='Target Customer Segment', required=True)
    
    # Total Available Market (TAM)
    total_available_market = fields.Monetary(string='Total Available Market (TAM)', currency_field='currency_id',
                                           help='Total global market demand for this product/service')
    tam_calculation_method = fields.Selection([
        ('population_based', 'Population-Based Calculation'),
        ('top_down', 'Top-Down Market Research'),
        ('bottom_up', 'Bottom-Up Analysis'),
        ('competitor_analysis', 'Competitor Analysis'),
        ('manual', 'Manual Entry')
    ], string='TAM Calculation Method', default='population_based', required=True)
    
    # Population-based TAM calculation
    target_population = fields.Float(string='Target Population (M)', compute='_compute_target_population', store=True,
                                   help='Total population in target markets (millions)')
    addressable_population_percentage = fields.Float(string='Addressable Population (%)', digits=(5, 2), default=100.0,
                                                    help='Percentage of population that could use this product/service')
    addressable_population = fields.Float(string='Addressable Population (M)', compute='_compute_addressable_population', store=True)
    average_spending_per_person = fields.Monetary(string='Average Annual Spending per Person', currency_field='currency_id',
                                                 help='Average amount each person spends on this category annually')
    
    # Serviceable Available Market (SAM)
    serviceable_available_market = fields.Monetary(string='Serviceable Available Market (SAM)', currency_field='currency_id',
                                                  help='Portion of TAM that can be served by your business model')
    sam_percentage_of_tam = fields.Float(string='SAM as % of TAM', digits=(5, 2), compute='_compute_market_percentages', store=True)
    geographic_reach_percentage = fields.Float(string='Geographic Reach (%)', digits=(5, 2), default=80.0,
                                             help='Percentage of target geography you can realistically serve')
    business_model_fit_percentage = fields.Float(string='Business Model Fit (%)', digits=(5, 2), default=70.0,
                                                help='Percentage of market that fits your business model')
    
    # Serviceable Obtainable Market (SOM) - Addressable Market
    addressable_market = fields.Monetary(string='Addressable Market (SOM)', currency_field='currency_id',
                                       help='Realistic market share you can capture')
    som_percentage_of_sam = fields.Float(string='SOM as % of SAM', digits=(5, 2), compute='_compute_market_percentages', store=True)
    realistic_market_share = fields.Float(string='Realistic Market Share (%)', digits=(5, 2), default=5.0,
                                        help='Realistic market share you can achieve in 5 years')
    competitive_intensity = fields.Selection([
        ('low', 'Low Competition'),
        ('medium', 'Medium Competition'),
        ('high', 'High Competition'),
        ('very_high', 'Very High Competition')
    ], string='Competitive Intensity', default='medium')
    
    # Market dynamics
    market_growth_rate = fields.Float(string='Annual Market Growth Rate (%)', digits=(5, 2), default=10.0)
    market_maturity = fields.Selection([
        ('emerging', 'Emerging Market'),
        ('growth', 'Growth Stage'),
        ('mature', 'Mature Market'),
        ('declining', 'Declining Market')
    ], string='Market Maturity', default='growth')
    
    # Computed fields
    tam_per_capita = fields.Monetary(string='TAM per Capita', compute='_compute_per_capita_metrics', currency_field='currency_id')
    sam_per_capita = fields.Monetary(string='SAM per Capita', compute='_compute_per_capita_metrics', currency_field='currency_id')
    som_per_capita = fields.Monetary(string='SOM per Capita', compute='_compute_per_capita_metrics', currency_field='currency_id')
    
    # Currency and company
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    
    # Additional analysis
    key_assumptions = fields.Text(string='Key Assumptions')
    market_risks = fields.Text(string='Market Risks')
    opportunities = fields.Text(string='Market Opportunities')
    data_sources = fields.Text(string='Data Sources')
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('review', 'Under Review'),
        ('approved', 'Approved'),
        ('outdated', 'Outdated')
    ], string='Status', default='draft')
    
    active = fields.Boolean(string='Active', default=True)

    @api.depends('country_ids', 'city_ids')
    def _compute_target_population(self):
        for sizing in self:
            total_population = 0.0
            
            # Add population from selected countries
            if sizing.country_ids:
                total_population += sum(sizing.country_ids.mapped('population'))
            
            # Add population from selected cities (if not already counted via countries)
            if sizing.city_ids:
                city_countries = sizing.city_ids.mapped('country_id')
                cities_not_in_countries = sizing.city_ids.filtered(lambda c: c.country_id not in sizing.country_ids)
                total_population += sum(cities_not_in_countries.mapped('population'))
            
            sizing.target_population = total_population

    @api.depends('target_population', 'addressable_population_percentage')
    def _compute_addressable_population(self):
        for sizing in self:
            if sizing.target_population and sizing.addressable_population_percentage:
                sizing.addressable_population = sizing.target_population * (sizing.addressable_population_percentage / 100)
            else:
                sizing.addressable_population = 0.0

    @api.depends('total_available_market', 'serviceable_available_market', 'addressable_market')
    def _compute_market_percentages(self):
        for sizing in self:
            # SAM as percentage of TAM
            if sizing.total_available_market > 0:
                sizing.sam_percentage_of_tam = (sizing.serviceable_available_market / sizing.total_available_market) * 100
            else:
                sizing.sam_percentage_of_tam = 0.0
            
            # SOM as percentage of SAM
            if sizing.serviceable_available_market > 0:
                sizing.som_percentage_of_sam = (sizing.addressable_market / sizing.serviceable_available_market) * 100
            else:
                sizing.som_percentage_of_sam = 0.0

    @api.depends('total_available_market', 'serviceable_available_market', 'addressable_market', 'addressable_population')
    def _compute_per_capita_metrics(self):
        for sizing in self:
            if sizing.addressable_population > 0:
                # Convert population from millions to actual number for calculation
                actual_population = sizing.addressable_population * 1000000
                sizing.tam_per_capita = sizing.total_available_market / actual_population
                sizing.sam_per_capita = sizing.serviceable_available_market / actual_population
                sizing.som_per_capita = sizing.addressable_market / actual_population
            else:
                sizing.tam_per_capita = 0.0
                sizing.sam_per_capita = 0.0
                sizing.som_per_capita = 0.0

    @api.onchange('addressable_population', 'average_spending_per_person', 'tam_calculation_method')
    def _onchange_calculate_tam(self):
        """Auto-calculate TAM when using population-based method"""
        if self.tam_calculation_method == 'population_based' and self.addressable_population and self.average_spending_per_person:
            # Convert population from millions to actual number
            actual_population = self.addressable_population * 1000000
            self.total_available_market = actual_population * self.average_spending_per_person

    @api.onchange('total_available_market', 'geographic_reach_percentage', 'business_model_fit_percentage')
    def _onchange_calculate_sam(self):
        """Auto-calculate SAM based on TAM and reach factors"""
        if self.total_available_market and self.geographic_reach_percentage and self.business_model_fit_percentage:
            reach_factor = self.geographic_reach_percentage / 100
            fit_factor = self.business_model_fit_percentage / 100
            self.serviceable_available_market = self.total_available_market * reach_factor * fit_factor

    @api.onchange('serviceable_available_market', 'realistic_market_share')
    def _onchange_calculate_som(self):
        """Auto-calculate SOM based on SAM and market share"""
        if self.serviceable_available_market and self.realistic_market_share:
            market_share_factor = self.realistic_market_share / 100
            self.addressable_market = self.serviceable_available_market * market_share_factor

    def action_approve(self):
        """Approve the market sizing analysis"""
        self.ensure_one()
        self.state = 'approved'
        return True

    def action_mark_outdated(self):
        """Mark analysis as outdated"""
        self.ensure_one()
        self.state = 'outdated'
        return True

    def get_market_sizing_summary(self):
        """Get comprehensive market sizing summary"""
        self.ensure_one()
        return {
            'analysis_name': self.name,
            'industry_sector': self.industry_sector,
            'target_customer_segment': self.target_customer_segment,
            'target_population': self.target_population,
            'addressable_population': self.addressable_population,
            'total_available_market': self.total_available_market,
            'serviceable_available_market': self.serviceable_available_market,
            'addressable_market': self.addressable_market,
            'sam_percentage_of_tam': self.sam_percentage_of_tam,
            'som_percentage_of_sam': self.som_percentage_of_sam,
            'tam_per_capita': self.tam_per_capita,
            'sam_per_capita': self.sam_per_capita,
            'som_per_capita': self.som_per_capita,
            'market_growth_rate': self.market_growth_rate,
            'realistic_market_share': self.realistic_market_share,
            'competitive_intensity': self.competitive_intensity,
            'state': self.state,
        }

    def calculate_projected_market_size(self, years_ahead=5):
        """Calculate projected market sizes for future years"""
        self.ensure_one()
        
        projections = []
        current_tam = self.total_available_market
        current_sam = self.serviceable_available_market
        current_som = self.addressable_market
        
        for year in range(1, years_ahead + 1):
            # Apply market growth rate
            growth_factor = (1 + self.market_growth_rate / 100) ** year
            
            projected_tam = current_tam * growth_factor
            projected_sam = current_sam * growth_factor
            projected_som = current_som * growth_factor
            
            projections.append({
                'year': year,
                'projected_tam': projected_tam,
                'projected_sam': projected_sam,
                'projected_som': projected_som,
            })
        
        return projections
