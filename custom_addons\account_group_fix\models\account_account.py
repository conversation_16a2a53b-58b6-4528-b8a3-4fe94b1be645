from odoo import models, fields, api


class AccountAccount(models.Model):
    _inherit = 'account.account'

    # Override the group_id field to make it editable
    group_id = fields.Many2one(
        'account.group',
        string='Account Group',
        readonly=False,
        store=True,
        help="Select the account group manually for analytics purposes."
    )

    # Disable the automatic computation
    @api.depends()
    def _compute_account_group(self):
        # Do nothing - disable automatic computation
        pass

    # Override methods that might interfere
    def _adapt_accounts_for_account_groups(self, account_ids=None, company=None):
        # Do nothing - disable automatic adaptation
        pass

    @api.model
    def _get_account_group(self, account_code, company):
        # Return None to prevent automatic group assignment
        return None
