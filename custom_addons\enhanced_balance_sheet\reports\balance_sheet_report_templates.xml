<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Enhanced Balance Sheet Report Template -->
        <template id="report_enhanced_balance_sheet_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page enhanced_balance_sheet_report">
                            
                            <!-- Report Header -->
                            <div class="report-header">
                                <div class="company-name">
                                    <strong t-field="doc.company_id.name"/>
                                </div>
                                <div class="report-title">
                                    STATEMENT OF FINANCIAL POSITION (BALANCE SHEET)
                                </div>
                                <div class="report-period">
                                    As at <span t-field="doc.date_to"/>
                                    <span t-if="doc.comparative_period">
                                        (Comparative: <span t-field="doc.comparative_date_to"/>)
                                    </span>
                                </div>
                            </div>

                            <!-- Balance Sheet Table -->
                            <table class="balance_sheet_table">
                                <thead>
                                    <tr>
                                        <th style="width: 60%;">Description</th>
                                        <th class="amount-column" style="width: 20%;">
                                            <span t-field="doc.date_to" t-options="{'format': 'yyyy'}"/>
                                        </th>
                                        <th class="amount-column" style="width: 20%;" t-if="doc.comparative_period">
                                            <span t-field="doc.comparative_date_to" t-options="{'format': 'yyyy'}"/>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="doc.line_ids" t-as="line">
                                        <tr t-att-class="'line-' + line.line_type + (' section-' + line.section if line.section else '') + (' underline' if line.underline else '')">
                                            <td>
                                                <span t-att-style="'padding-left: %spx; font-weight: %s;' % (line.indent_level * 20, 'bold' if line.bold else 'normal')">
                                                    <span t-field="line.name"/>
                                                </span>
                                            </td>
                                            <td class="amount-column">
                                                <span t-if="line.current_amount != 0 or line.line_type in ['subtotal', 'total', 'header']"
                                                      t-field="line.current_amount" 
                                                      t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                            <td class="amount-column" t-if="doc.comparative_period">
                                                <span t-if="line.comparative_amount != 0 or line.line_type in ['subtotal', 'total', 'header']"
                                                      t-field="line.comparative_amount" 
                                                      t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>

                            <!-- Balance Verification -->
                            <div class="balance_verification">
                                <div class="verification-title">Balance Verification</div>
                                <div class="verification-grid">
                                    <div class="verification-item">
                                        <div class="verification-label">Total Assets</div>
                                        <div class="verification-amount">
                                            <span t-field="doc.total_assets" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                        </div>
                                    </div>
                                    <div class="verification-item">
                                        <div class="verification-label">Total Liabilities</div>
                                        <div class="verification-amount">
                                            <span t-field="doc.total_liabilities" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                        </div>
                                    </div>
                                    <div class="verification-item">
                                        <div class="verification-label">Total Equity</div>
                                        <div class="verification-amount">
                                            <span t-field="doc.total_equity" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                        </div>
                                    </div>
                                    <div class="verification-item">
                                        <div class="verification-label">Balance Check</div>
                                        <div t-att-class="'verification-amount ' + ('balanced' if doc.balance_check == 0 else 'unbalanced')">
                                            <span t-field="doc.balance_check" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                            <div style="font-size: 0.8rem; margin-top: 5px;">
                                                <span t-if="doc.balance_check == 0" class="text-success">✓ Balanced</span>
                                                <span t-else="" class="text-danger">⚠ Out of Balance</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Report Footer -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <p class="text-muted text-center">
                                        <small>
                                            Report generated on <span t-esc="datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"/>
                                            | Target Moves: <span t-field="doc.target_move"/>
                                            <span t-if="not doc.show_zero_balance"> | Zero balance accounts excluded</span>
                                        </small>
                                    </p>
                                </div>
                            </div>

                        </div>
                    </t>
                </t>
            </t>
        </template>

        <!-- Report Action -->
        <record id="action_report_balance_sheet" model="ir.actions.report">
            <field name="name">Enhanced Balance Sheet</field>
            <field name="model">enhanced.balance.sheet</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">enhanced_balance_sheet.report_enhanced_balance_sheet_template</field>
            <field name="report_file">enhanced_balance_sheet.report_enhanced_balance_sheet_template</field>
            <field name="print_report_name">'Balance Sheet - %s' % (object.name)</field>
            <field name="binding_model_id" ref="model_enhanced_balance_sheet"/>
            <field name="binding_type">report</field>
        </record>

    </data>
</odoo>
