# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date


class MarketSizingWizard(models.TransientModel):
    _name = 'financial.planning.market.sizing.wizard'
    _description = 'Market Sizing Analysis Wizard'

    name = fields.Char(string='Analysis Name', required=True, default='New Market Analysis')
    description = fields.Text(string='Description')
    
    # Market definition
    industry_sector = fields.Selection([
        ('fintech', 'Financial Technology'),
        ('ecommerce', 'E-commerce'),
        ('healthcare', 'Healthcare'),
        ('education', 'Education Technology'),
        ('entertainment', 'Entertainment & Media'),
        ('food_delivery', 'Food Delivery'),
        ('transportation', 'Transportation & Mobility'),
        ('real_estate', 'Real Estate Technology'),
        ('retail', 'Retail & Consumer Goods'),
        ('b2b_services', 'B2B Services'),
        ('other', 'Other')
    ], string='Industry Sector', required=True)
    
    product_category = fields.Char(string='Product/Service Category', required=True)
    target_customer_segment = fields.Selection([
        ('b2c_mass', 'B2C Mass Market'),
        ('b2c_premium', 'B2C Premium'),
        ('b2c_budget', 'B2C Budget'),
        ('b2b_sme', 'B2B Small & Medium Enterprises'),
        ('b2b_enterprise', 'B2B Enterprise'),
        ('b2b_government', 'B2B Government'),
        ('mixed', 'Mixed B2B/B2C')
    ], string='Target Customer Segment', required=True)
    
    # Geographic scope
    country_ids = fields.Many2many('financial.planning.country', string='Target Countries')
    city_ids = fields.Many2many('financial.planning.city', string='Target Cities')
    
    # TAM calculation method
    tam_calculation_method = fields.Selection([
        ('population_based', 'Population-Based Calculation'),
        ('top_down', 'Top-Down Market Research'),
        ('bottom_up', 'Bottom-Up Analysis'),
        ('manual', 'Manual Entry')
    ], string='TAM Calculation Method', default='population_based', required=True)
    
    # Population-based parameters
    addressable_population_percentage = fields.Float(string='Addressable Population (%)', digits=(5, 2), default=80.0)
    average_spending_per_person = fields.Monetary(string='Average Annual Spending per Person', currency_field='currency_id')
    
    # Manual TAM entry
    manual_tam = fields.Monetary(string='Total Available Market', currency_field='currency_id')
    
    # SAM parameters
    geographic_reach_percentage = fields.Float(string='Geographic Reach (%)', digits=(5, 2), default=70.0)
    business_model_fit_percentage = fields.Float(string='Business Model Fit (%)', digits=(5, 2), default=60.0)
    
    # SOM parameters
    realistic_market_share = fields.Float(string='Realistic Market Share (%)', digits=(5, 2), default=3.0)
    competitive_intensity = fields.Selection([
        ('low', 'Low Competition'),
        ('medium', 'Medium Competition'),
        ('high', 'High Competition'),
        ('very_high', 'Very High Competition')
    ], string='Competitive Intensity', default='medium')
    
    # Market dynamics
    market_growth_rate = fields.Float(string='Annual Market Growth Rate (%)', digits=(5, 2), default=8.0)
    market_maturity = fields.Selection([
        ('emerging', 'Emerging Market'),
        ('growth', 'Growth Stage'),
        ('mature', 'Mature Market'),
        ('declining', 'Declining Market')
    ], string='Market Maturity', default='growth')
    
    # Computed preview fields
    target_population = fields.Float(string='Target Population (M)', compute='_compute_preview_metrics')
    addressable_population = fields.Float(string='Addressable Population (M)', compute='_compute_preview_metrics')
    calculated_tam = fields.Monetary(string='Calculated TAM', compute='_compute_preview_metrics', currency_field='currency_id')
    calculated_sam = fields.Monetary(string='Calculated SAM', compute='_compute_preview_metrics', currency_field='currency_id')
    calculated_som = fields.Monetary(string='Calculated SOM', compute='_compute_preview_metrics', currency_field='currency_id')
    
    # Currency
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)
    
    # Wizard steps
    step = fields.Selection([
        ('market_definition', 'Market Definition'),
        ('geographic_scope', 'Geographic Scope'),
        ('tam_calculation', 'TAM Calculation'),
        ('sam_som_calculation', 'SAM & SOM Calculation'),
        ('review', 'Review & Create')
    ], string='Step', default='market_definition')

    @api.depends('country_ids', 'city_ids', 'addressable_population_percentage', 'average_spending_per_person', 
                 'tam_calculation_method', 'manual_tam', 'geographic_reach_percentage', 'business_model_fit_percentage', 
                 'realistic_market_share')
    def _compute_preview_metrics(self):
        for wizard in self:
            # Calculate target population
            total_population = 0.0
            if wizard.country_ids:
                total_population += sum(wizard.country_ids.mapped('population'))
            if wizard.city_ids:
                city_countries = wizard.city_ids.mapped('country_id')
                cities_not_in_countries = wizard.city_ids.filtered(lambda c: c.country_id not in wizard.country_ids)
                total_population += sum(cities_not_in_countries.mapped('population'))
            
            wizard.target_population = total_population
            
            # Calculate addressable population
            if wizard.addressable_population_percentage:
                wizard.addressable_population = total_population * (wizard.addressable_population_percentage / 100)
            else:
                wizard.addressable_population = 0.0
            
            # Calculate TAM
            if wizard.tam_calculation_method == 'population_based' and wizard.addressable_population and wizard.average_spending_per_person:
                actual_population = wizard.addressable_population * 1000000  # Convert to actual number
                wizard.calculated_tam = actual_population * wizard.average_spending_per_person
            elif wizard.tam_calculation_method == 'manual':
                wizard.calculated_tam = wizard.manual_tam
            else:
                wizard.calculated_tam = 0.0
            
            # Calculate SAM
            if wizard.calculated_tam and wizard.geographic_reach_percentage and wizard.business_model_fit_percentage:
                reach_factor = wizard.geographic_reach_percentage / 100
                fit_factor = wizard.business_model_fit_percentage / 100
                wizard.calculated_sam = wizard.calculated_tam * reach_factor * fit_factor
            else:
                wizard.calculated_sam = 0.0
            
            # Calculate SOM
            if wizard.calculated_sam and wizard.realistic_market_share:
                market_share_factor = wizard.realistic_market_share / 100
                wizard.calculated_som = wizard.calculated_sam * market_share_factor
            else:
                wizard.calculated_som = 0.0

    def action_next_step(self):
        """Move to next step in wizard"""
        self.ensure_one()
        
        if self.step == 'market_definition':
            self.step = 'geographic_scope'
        elif self.step == 'geographic_scope':
            self.step = 'tam_calculation'
        elif self.step == 'tam_calculation':
            self.step = 'sam_som_calculation'
        elif self.step == 'sam_som_calculation':
            self.step = 'review'
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.market.sizing.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_previous_step(self):
        """Move to previous step in wizard"""
        self.ensure_one()
        
        if self.step == 'review':
            self.step = 'sam_som_calculation'
        elif self.step == 'sam_som_calculation':
            self.step = 'tam_calculation'
        elif self.step == 'tam_calculation':
            self.step = 'geographic_scope'
        elif self.step == 'geographic_scope':
            self.step = 'market_definition'
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.market.sizing.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_create_analysis(self):
        """Create the market sizing analysis"""
        self.ensure_one()
        
        # Determine continents from selected countries
        continent_ids = []
        if self.country_ids:
            continent_ids = self.country_ids.mapped('continent_id').ids
        
        # Create the market sizing analysis
        analysis_vals = {
            'name': self.name,
            'description': self.description,
            'industry_sector': self.industry_sector,
            'product_category': self.product_category,
            'target_customer_segment': self.target_customer_segment,
            'continent_ids': [(6, 0, continent_ids)],
            'country_ids': [(6, 0, self.country_ids.ids)],
            'city_ids': [(6, 0, self.city_ids.ids)],
            'tam_calculation_method': self.tam_calculation_method,
            'addressable_population_percentage': self.addressable_population_percentage,
            'average_spending_per_person': self.average_spending_per_person,
            'total_available_market': self.calculated_tam if self.tam_calculation_method == 'population_based' else self.manual_tam,
            'geographic_reach_percentage': self.geographic_reach_percentage,
            'business_model_fit_percentage': self.business_model_fit_percentage,
            'serviceable_available_market': self.calculated_sam,
            'realistic_market_share': self.realistic_market_share,
            'addressable_market': self.calculated_som,
            'competitive_intensity': self.competitive_intensity,
            'market_growth_rate': self.market_growth_rate,
            'market_maturity': self.market_maturity,
            'currency_id': self.currency_id.id,
            'state': 'draft',
        }
        
        analysis = self.env['financial.planning.market.sizing'].create(analysis_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.market.sizing',
            'res_id': analysis.id,
            'view_mode': 'form',
            'target': 'current',
        }

    @api.onchange('country_ids')
    def _onchange_country_ids(self):
        """Update suggested parameters based on selected countries"""
        if self.country_ids:
            # Calculate average growth rate from selected countries
            avg_growth = sum(self.country_ids.mapped('annual_gdp_growth_rate')) / len(self.country_ids)
            if avg_growth > 0:
                self.market_growth_rate = avg_growth
            
            # Suggest market maturity based on countries
            developed_countries = self.country_ids.filtered(lambda c: c.human_development_index > 0.8)
            if len(developed_countries) == len(self.country_ids):
                self.market_maturity = 'mature'
            elif len(developed_countries) > len(self.country_ids) / 2:
                self.market_maturity = 'growth'
            else:
                self.market_maturity = 'emerging'

    def get_analysis_summary(self):
        """Get analysis summary for review step"""
        self.ensure_one()
        return {
            'analysis_name': self.name,
            'industry_sector': self.industry_sector,
            'target_customer_segment': self.target_customer_segment,
            'target_countries': len(self.country_ids),
            'target_cities': len(self.city_ids),
            'target_population': self.target_population,
            'addressable_population': self.addressable_population,
            'calculated_tam': self.calculated_tam,
            'calculated_sam': self.calculated_sam,
            'calculated_som': self.calculated_som,
            'market_growth_rate': self.market_growth_rate,
            'realistic_market_share': self.realistic_market_share,
        }
