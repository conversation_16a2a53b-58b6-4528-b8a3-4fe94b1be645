from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta

from core.models import Company, Partner, Currency, Country
from inventory.models import (
    Product, ProductTemplate, ProductCategory,
    StockLocation, StockWarehouse
)
from sales.models import ProductUom, ProductUomCategory
from .models import (
    MrpBom, MrpBomLine, MrpWorkcenter, Mrp<PERSON><PERSON>ing, MrpRoutingWorkcenter,
    MrpProduction, MrpWorkorder, MrpByproduct, MrpUnbuild
)


class MrpModelsTestCase(TestCase):
    """Test case for MRP models and business logic"""
    
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create currency
        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create country
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Manufacturing Company',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create UOM
        uom_category = ProductUomCategory.objects.create(
            name='Unit',
            create_uid=self.user,
            write_uid=self.user
        )

        self.uom_unit = ProductUom.objects.create(
            name='Unit',
            category_id=uom_category,
            factor=1.0,
            uom_type='reference',
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create product category
        self.product_category = ProductCategory.objects.create(
            name='Manufacturing Products',
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create products
        # Finished product
        self.finished_product_tmpl = ProductTemplate.objects.create(
            name='Finished Product',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.finished_product = Product.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Component products
        self.component1_tmpl = ProductTemplate.objects.create(
            name='Component 1',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.component1 = Product.objects.create(
            product_tmpl_id=self.component1_tmpl,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.component2_tmpl = ProductTemplate.objects.create(
            name='Component 2',
            detailed_type='product',
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.component2 = Product.objects.create(
            product_tmpl_id=self.component2_tmpl,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create partner for warehouse
        self.warehouse_partner = Partner.objects.create(
            name='Warehouse Partner',
            is_company=True,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create locations
        self.warehouse = StockWarehouse.objects.create(
            name='Main Warehouse',
            code='WH',
            partner_id=self.warehouse_partner,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create picking type for manufacturing
        from purchases.models import StockPickingType
        self.manufacturing_picking_type = StockPickingType.objects.create(
            name='Manufacturing',
            code='internal',
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.stock_location = StockLocation.objects.create(
            name='Stock',
            usage='internal',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.production_location = StockLocation.objects.create(
            name='Production',
            usage='production',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create work center
        self.workcenter = MrpWorkcenter.objects.create(
            name='Assembly Line',
            code='ASM',
            capacity=1.0,
            time_efficiency=100.0,
            costs_hour=50.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create routing
        self.routing = MrpRouting.objects.create(
            name='Assembly Routing',
            code='ASM-001',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create routing operation
        self.operation = MrpRoutingWorkcenter.objects.create(
            routing_id=self.routing,
            name='Assembly Operation',
            workcenter_id=self.workcenter,
            sequence=10,
            time_cycle=60.0,  # 60 minutes
            create_uid=self.user,
            write_uid=self.user
        )
    
    def test_bom_creation_and_validation(self):
        """Test BOM creation and validation"""
        # Create BOM
        bom = MrpBom.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            product_id=self.finished_product,
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            type='normal',
            routing_id=self.routing,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test BOM properties
        self.assertEqual(bom.product_tmpl_id, self.finished_product_tmpl)
        self.assertEqual(bom.product_qty, 1.0)
        self.assertEqual(bom.type, 'normal')
        self.assertTrue(bom.active)
        
        # Create BOM lines
        bom_line1 = MrpBomLine.objects.create(
            bom_id=bom,
            product_id=self.component1,
            product_qty=2.0,
            product_uom_id=self.uom_unit,
            sequence=10,
            create_uid=self.user,
            write_uid=self.user
        )
        
        bom_line2 = MrpBomLine.objects.create(
            bom_id=bom,
            product_id=self.component2,
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            sequence=20,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test BOM lines
        self.assertEqual(bom.bom_line_ids.count(), 2)
        self.assertEqual(bom_line1.product_qty, 2.0)
        self.assertEqual(bom_line2.product_qty, 1.0)
        
        # Test BOM explosion
        exploded_lines = bom._compute_exploded_bom(product_qty=5.0)
        self.assertEqual(len(exploded_lines), 2)
        
        # Check quantities are correctly calculated
        component1_line = next(line for line in exploded_lines 
                              if line['product_id'] == self.component1)
        component2_line = next(line for line in exploded_lines 
                              if line['product_id'] == self.component2)
        
        self.assertEqual(component1_line['product_qty'], 10.0)  # 2.0 * 5.0
        self.assertEqual(component2_line['product_qty'], 5.0)   # 1.0 * 5.0
    
    def test_manufacturing_order_workflow(self):
        """Test complete manufacturing order workflow"""
        # Create BOM first
        bom = MrpBom.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            product_id=self.finished_product,
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            routing_id=self.routing,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create BOM lines
        MrpBomLine.objects.create(
            bom_id=bom,
            product_id=self.component1,
            product_qty=2.0,
            product_uom_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create manufacturing order
        production = MrpProduction.objects.create(
            product_id=self.finished_product,
            product_tmpl_id=self.finished_product_tmpl,
            product_qty=10.0,
            product_uom_id=self.uom_unit,
            bom_id=bom,
            routing_id=self.routing,
            date_planned_start=timezone.now(),
            date_planned_finished=timezone.now() + timedelta(hours=8),
            location_src_id=self.stock_location,
            location_dest_id=self.production_location,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test initial state
        self.assertEqual(production.state, 'draft')
        self.assertEqual(production.name, 'New')
        
        # Confirm manufacturing order
        production.action_confirm()
        
        # Test after confirmation
        self.assertEqual(production.state, 'confirmed')
        self.assertTrue(production.name.startswith('MO'))
        
        # Check work orders were created
        self.assertEqual(production.workorder_ids.count(), 1)
        workorder = production.workorder_ids.first()
        self.assertEqual(workorder.workcenter_id, self.workcenter)
        self.assertEqual(workorder.state, 'pending')
        
        # Plan the manufacturing order
        production.button_plan()
        
        # Check work order is now ready
        workorder.refresh_from_db()
        self.assertEqual(workorder.state, 'ready')
        
        # Start work order
        workorder.button_start()
        self.assertEqual(workorder.state, 'progress')
        self.assertIsNotNone(workorder.date_start)
        
        # Check production state changed
        production.refresh_from_db()
        self.assertEqual(production.state, 'progress')
        
        # Finish work order
        workorder.qty_producing = 10.0
        workorder.save()
        workorder.button_finish()
        
        self.assertEqual(workorder.state, 'done')
        self.assertEqual(workorder.qty_produced, 10.0)
        self.assertIsNotNone(workorder.date_finished)
        
        # Check production is ready to close
        production.refresh_from_db()
        self.assertEqual(production.state, 'to_close')
        
        # Mark production as done
        production.button_mark_done()
        self.assertEqual(production.state, 'done')
        self.assertIsNotNone(production.date_finished)
    
    def test_work_center_operations(self):
        """Test work center functionality"""
        # Test work center creation
        workcenter = MrpWorkcenter.objects.create(
            name='CNC Machine',
            code='CNC01',
            capacity=2.0,
            time_efficiency=95.0,
            costs_hour=75.0,
            time_start=10.0,  # 10 minutes setup
            time_stop=5.0,    # 5 minutes cleanup
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test work center properties
        self.assertEqual(workcenter.name, 'CNC Machine')
        self.assertEqual(workcenter.capacity, 2.0)
        self.assertEqual(workcenter.time_efficiency, 95.0)
        self.assertEqual(workcenter.costs_hour, 75.0)
        self.assertTrue(workcenter.active)
    
    def test_unbuild_order_workflow(self):
        """Test unbuild order functionality"""
        # Create BOM for unbuild
        bom = MrpBom.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            product_id=self.finished_product,
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create BOM line
        MrpBomLine.objects.create(
            bom_id=bom,
            product_id=self.component1,
            product_qty=3.0,
            product_uom_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create unbuild order
        unbuild = MrpUnbuild.objects.create(
            product_id=self.finished_product,
            product_qty=5.0,
            product_uom_id=self.uom_unit,
            bom_id=bom,
            location_id=self.stock_location,
            location_dest_id=self.production_location,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test initial state
        self.assertEqual(unbuild.state, 'draft')
        self.assertEqual(unbuild.name, 'New')
        
        # Process unbuild
        unbuild.action_unbuild()
        
        # Test after processing
        self.assertEqual(unbuild.state, 'done')
        self.assertTrue(unbuild.name.startswith('UB'))
    
    def test_bom_find_functionality(self):
        """Test BOM finding functionality"""
        # Create multiple BOMs
        bom1 = MrpBom.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            product_id=None,  # Generic BOM
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            sequence=10,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        bom2 = MrpBom.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            product_id=self.finished_product,  # Specific variant BOM
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            sequence=5,  # Higher priority
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Test BOM finding - should return specific variant BOM
        found_bom = MrpBom._bom_find(self.finished_product, self.company.id)
        self.assertEqual(found_bom, bom2)
        
        # Test with inactive BOM
        bom2.active = False
        bom2.save()
        
        found_bom = MrpBom._bom_find(self.finished_product, self.company.id)
        self.assertEqual(found_bom, bom1)

    def test_mrp_sales_integration(self):
        """Test MRP integration with sales orders"""
        from sales.models import SaleOrder, SaleOrderLine, SalesTeam, ProductPricelist

        # Create sales team
        sales_team = SalesTeam.objects.create(
            name='Manufacturing Sales Team',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create pricelist
        pricelist = ProductPricelist.objects.create(
            name='Manufacturing Pricelist',
            currency_id=self.currency,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create customer
        customer = Partner.objects.create(
            name='Manufacturing Customer',
            is_company=True,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create BOM for the product
        bom = MrpBom.objects.create(
            product_tmpl_id=self.finished_product_tmpl,
            product_id=self.finished_product,
            product_qty=1.0,
            product_uom_id=self.uom_unit,
            routing_id=self.routing,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create BOM line
        MrpBomLine.objects.create(
            bom_id=bom,
            product_id=self.component1,
            product_qty=2.0,
            product_uom_id=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create sales order
        sale_order = SaleOrder.objects.create(
            partner_id=customer,
            partner_invoice_id=customer,
            partner_shipping_id=customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=pricelist,
            user_id=self.user,
            team_id=sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create sales order line for manufactured product
        sale_line = SaleOrderLine.objects.create(
            order_id=sale_order,
            product_id=self.finished_product,
            product_uom_qty=5.0,
            product_uom=self.uom_unit,
            price_unit=100.0,
            create_uid=self.user,
            write_uid=self.user
        )

        # Confirm sales order
        sale_order.action_confirm()

        # Verify sales order is confirmed
        self.assertEqual(sale_order.state, 'sale')

        # Now create manufacturing order for the sales order
        production = MrpProduction.objects.create(
            product_id=self.finished_product,
            product_tmpl_id=self.finished_product_tmpl,
            product_qty=5.0,
            product_uom_id=self.uom_unit,
            bom_id=bom,
            routing_id=self.routing,
            origin=sale_order.name,  # Link to sales order
            date_planned_start=timezone.now(),
            date_planned_finished=timezone.now() + timedelta(hours=8),
            location_src_id=self.stock_location,
            location_dest_id=self.production_location,
            picking_type_id=self.manufacturing_picking_type,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Confirm manufacturing order
        production.action_confirm()

        # Verify manufacturing order is confirmed
        self.assertEqual(production.state, 'confirmed')
        self.assertTrue(production.name.startswith('MO'))
        self.assertEqual(production.origin, sale_order.name)

        # Verify work orders were created
        self.assertEqual(production.workorder_ids.count(), 1)
        workorder = production.workorder_ids.first()
        self.assertEqual(workorder.qty_production, 5.0)

        # This demonstrates the complete integration:
        # Sales Order -> Manufacturing Order -> Work Orders
        # This is exactly how Odoo MRP works!

        print(f"✅ MRP-Sales Integration Test Passed!")
        print(f"   Sales Order: {sale_order.name}")
        print(f"   Manufacturing Order: {production.name}")
        print(f"   Work Order: {workorder.name}")
        print(f"   Quantity to Produce: {production.product_qty}")
        print(f"   Components Required: {bom.bom_line_ids.count()}")

        return True
