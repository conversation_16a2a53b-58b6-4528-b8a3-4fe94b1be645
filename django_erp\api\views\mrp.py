from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

# Placeholder for MRP API views
# TODO: Implement full MRP API

class ProductionViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class BomViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class WorkcenterViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class WorkorderViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None
