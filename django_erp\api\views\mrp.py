from rest_framework import viewsets, serializers
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

# Placeholder serializer for API documentation
class PlaceholderSerializer(serializers.Serializer):
    message = serializers.CharField(default="This endpoint is not yet implemented")

# Placeholder for MRP API views
# TODO: Implement full MRP API

class ProductionViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Manufacturing Order API endpoints will be implemented soon"})

class BomViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Bill of Materials API endpoints will be implemented soon"})

class WorkcenterViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Work Center API endpoints will be implemented soon"})

class WorkorderViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Work Order API endpoints will be implemented soon"})
