import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import getpass

def create_odoo_user():
    try:
        print("Starting database setup...")
        # Try with empty password first
        postgres_password = ""
        
        # Connect to PostgreSQL as postgres user
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            user="postgres",
            password=postgres_password,
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        cursor = conn.cursor()
        
        # Check if odoo user already exists
        cursor.execute("SELECT 1 FROM pg_roles WHERE rolname='odoo'")
        if cursor.fetchone():
            print("User 'odoo' already exists. Dropping and recreating...")
            cursor.execute("DROP USER odoo")
        
        # Create odoo user with createdb privilege
        cursor.execute("CREATE USER odoo WITH CREATEDB PASSWORD 'odoo'")
        print("User 'odoo' created successfully with CREATEDB privilege")
        
        # Create the database
        cursor.execute("SELECT 1 FROM pg_database WHERE datname='my_odoo_erp'")
        if cursor.fetchone():
            print("Database 'my_odoo_erp' already exists. Dropping and recreating...")
            cursor.execute("DROP DATABASE my_odoo_erp")
        
        cursor.execute("CREATE DATABASE my_odoo_erp OWNER odoo")
        print("Database 'my_odoo_erp' created successfully")
        
        cursor.close()
        conn.close()
        
        print("Setup completed successfully!")
        
    except psycopg2.Error as e:
        print(f"Database error: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    create_odoo_user()
