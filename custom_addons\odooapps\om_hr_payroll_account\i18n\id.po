# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <wahyuse<PERSON><EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: Ryanto The <<EMAIL>>, 2019\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:om_hr_payroll_account.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:om_hr_payroll_account.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr "Akuntansi"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "Catatan akuntansi"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:114
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:129
#, python-format
msgid "Adjustment Entry"
msgstr "Penyesuaian entri"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Akun Analitik"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__account_credit
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Rekening kredit"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__account_debit
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Debet rekening"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "Kontrak Karyawan"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Buat Slip Gaji untuk semuar karyawan yang dipilih"

#. module: hr_payroll_account
#: model:ir.model.fields,help:om_hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "Biarkan tetap kosong untuk menggunakan periode tanggal  validasi"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "Slip Gaji"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Slip Gaji Massal"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_payslip_line
msgid "Payslip Line"
msgstr "Baris Slip Gaji"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:65
#, python-format
msgid "Payslip of %s"
msgstr "Payslip dari %s"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_contract__journal_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip__journal_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_run__journal_id
msgid "Salary Journal"
msgstr "Jurnal gaji"

#. module: hr_payroll_account
#: model:ir.model,name:om_hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,field_description:om_hr_payroll_account.field_hr_salary_rule__account_tax_id
msgid "Tax"
msgstr "Pajak"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:112
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr ""
"Biaya jurnal \"%s\" telah tidak dikonfigurasi dengan benar rekening kredit!"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/om_hr_payroll_account.py:127
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr ""
"Biaya jurnal \"%s\" telah tidak dikonfigurasi dengan benar Debit rekening!"
