# Generated by Django 4.2.21 on 2025-07-20 09:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_rename_state_countrystate_countrygroup'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0002_remove_stocklocation_barcode_company_uniq_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductPackaging',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Packaging Name', max_length=255)),
                ('qty', models.FloatField(default=1.0, help_text='Quantity per Package')),
                ('barcode', models.CharField(blank=True, help_text='Barcode', max_length=255)),
                ('sales', models.BooleanField(default=False, help_text='Available in Sales')),
                ('purchase', models.BooleanField(default=False, help_text='Available in Purchase')),
                ('company_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('product_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.product')),
                ('product_tmpl_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.producttemplate')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['product_id'], name='inventory_p_product_be25a5_idx'), models.Index(fields=['product_tmpl_id'], name='inventory_p_product_8beae6_idx')],
            },
        ),
    ]
