<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Financial Analytics Categories -->
        <record id="module_category_financial_analytics" model="ir.module.category">
            <field name="name">Financial Analytics</field>
            <field name="description">Financial Statements with Analytics</field>
            <field name="sequence">25</field>
        </record>

        <!-- Financial Analytics User Group -->
        <record id="group_financial_analytics_user" model="res.groups">
            <field name="name">Financial Analytics User</field>
            <field name="category_id" ref="module_category_financial_analytics"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">User can create and view financial statements with analytics</field>
        </record>

        <!-- Financial Analytics Manager Group -->
        <record id="group_financial_analytics_manager" model="res.groups">
            <field name="name">Financial Analytics Manager</field>
            <field name="category_id" ref="module_category_financial_analytics"/>
            <field name="implied_ids" eval="[(4, ref('group_financial_analytics_user'))]"/>
            <field name="comment">Manager can approve and publish financial statements</field>
        </record>

        <!-- Financial Analytics Analyst Group -->
        <record id="group_financial_analytics_analyst" model="res.groups">
            <field name="name">Financial Analytics Analyst</field>
            <field name="category_id" ref="module_category_financial_analytics"/>
            <field name="implied_ids" eval="[(4, ref('group_financial_analytics_user'))]"/>
            <field name="comment">Analyst can perform advanced analytics and ratio analysis</field>
        </record>

        <!-- Record Rules -->
        
        <!-- Financial Statement Analytics - Multi-company Rule -->
        <record id="financial_statement_analytics_company_rule" model="ir.rule">
            <field name="name">Financial Statement Analytics: Multi-company</field>
            <field name="model_id" ref="model_financial_statement_analytics"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
            <field name="global" eval="True"/>
        </record>

        <!-- Financial Statement Analytics - User Access Rule -->
        <record id="financial_statement_analytics_user_rule" model="ir.rule">
            <field name="name">Financial Statement Analytics: User Access</field>
            <field name="model_id" ref="model_financial_statement_analytics"/>
            <field name="domain_force">[('company_id', 'in', user.company_ids.ids)]</field>
            <field name="groups" eval="[(4, ref('group_financial_analytics_user'))]"/>
        </record>

        <!-- Financial Statement Line Analytics - User Access Rule -->
        <record id="financial_statement_line_analytics_user_rule" model="ir.rule">
            <field name="name">Financial Statement Line Analytics: User Access</field>
            <field name="model_id" ref="model_financial_statement_line_analytics"/>
            <field name="domain_force">[('statement_id.company_id', 'in', user.company_ids.ids)]</field>
            <field name="groups" eval="[(4, ref('group_financial_analytics_user'))]"/>
        </record>

        <!-- Financial Ratio Analysis - User Access Rule -->
        <record id="financial_ratio_analysis_user_rule" model="ir.rule">
            <field name="name">Financial Ratio Analysis: User Access</field>
            <field name="model_id" ref="model_financial_ratio_analysis"/>
            <field name="domain_force">[('statement_id.company_id', 'in', user.company_ids.ids)]</field>
            <field name="groups" eval="[(4, ref('group_financial_analytics_user'))]"/>
        </record>

    </data>
</odoo>
