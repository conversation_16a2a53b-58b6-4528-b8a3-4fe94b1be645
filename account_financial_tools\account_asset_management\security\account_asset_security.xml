<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">
    <record id="account_asset_profile_multi_company_rule" model="ir.rule">
        <field name="name">Account Asset Profile multi-company</field>
        <field ref="model_account_asset_profile" name="model_id" />
        <field eval="True" name="global" />
        <field
            name="domain_force"
        >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>
    <record id="account_asset_multi_company_rule" model="ir.rule">
        <field name="name">Account Asset multi-company</field>
        <field ref="model_account_asset" name="model_id" />
        <field eval="True" name="global" />
        <field
            name="domain_force"
        >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>
    <record id="account_asset_group_multi_company_rule" model="ir.rule">
        <field name="name">Account Asset Group multi-company</field>
        <field ref="model_account_asset_group" name="model_id" />
        <field eval="True" name="global" />
        <field
            name="domain_force"
        >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>
</odoo>
