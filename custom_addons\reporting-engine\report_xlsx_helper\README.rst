===================
Report xlsx helpers
===================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:df481003a65f02d5bf2edcc90f4238033b81c3fa5120209c2e36e25b1cdabb77
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Freporting--engine-lightgray.png?logo=github
    :target: https://github.com/OCA/reporting-engine/tree/17.0/report_xlsx_helper
    :alt: OCA/reporting-engine
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/reporting-engine-17-0/reporting-engine-17-0-report_xlsx_helper
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/reporting-engine&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module provides a set of tools to facilitate the creation of excel
reports with format xlsx.

**Table of contents**

.. contents::
   :local:

Installation
============

This module requires report_xlsx version 13.0.1.0.0 or higher.

Usage
=====

In order to create an Excel report you can define a report of type
'xlsx' in a static or dynamic way:

-  Static syntax: cf. ``account_move_line_report_xls`` for an example.
-  Dynamic syntax: cf. ``report_xlsx_helper_demo`` for an example

The ``AbstractReportXlsx`` class contains a number of attributes and
methods to facilitate the creation excel reports in Odoo.

-  Cell types

   string, number, boolean, datetime.

-  Cell formats

   The predefined cell formats result in a consistent look and feel of
   the Odoo Excel reports.

-  Cell formulas

   Cell formulas can be easily added with the help of the
   ``_rowcol_to_cell()`` method.

-  Excel templates

   It is possible to define Excel templates which can be adapted by
   'inherited' modules. Download the ``account_move_line_report_xls``
   module from http://apps.odoo.com as example.

-  Excel with multiple sheets

   Download the ``account_asset_management_xls`` module from
   http://apps.odoo.com as example.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/reporting-engine/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/reporting-engine/issues/new?body=module:%20report_xlsx_helper%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Noviat

Contributors
------------

-  Luc De Meyer <<EMAIL>>

-  Rattapong Chokmasermkul <<EMAIL>>

-  Saran Lim. <<EMAIL>>

-  `Sinerkia Innovación y Desarrollo S.L. <https://www.sinerkia.com>`__:

   -  Luis Pomar

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/reporting-engine <https://github.com/OCA/reporting-engine/tree/17.0/report_xlsx_helper>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
