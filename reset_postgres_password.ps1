# PowerShell script to reset PostgreSQL password
Write-Host "Resetting PostgreSQL password..." -ForegroundColor Green

# Stop PostgreSQL service
Write-Host "Stopping PostgreSQL service..." -ForegroundColor Yellow
Stop-Service -Name "postgresql-x64-17" -Force

# Backup original pg_hba.conf
Write-Host "Backing up pg_hba.conf..." -ForegroundColor Yellow
$pgHbaPath = "D:\PostgreSQL\17\data\pg_hba.conf"
$backupPath = "D:\PostgreSQL\17\data\pg_hba.conf.backup"
Copy-Item $pgHbaPath $backupPath -Force

# Modify pg_hba.conf to use trust authentication
Write-Host "Modifying authentication to trust..." -ForegroundColor Yellow
$content = Get-Content $pgHbaPath
$content = $content -replace "scram-sha-256", "trust"
$content | Set-Content $pgHbaPath

# Start PostgreSQL service
Write-Host "Starting PostgreSQL service..." -ForegroundColor Yellow
Start-Service -Name "postgresql-x64-17"

# Wait for service to start
Start-Sleep -Seconds 5

# Reset password
Write-Host "Resetting postgres password to 'postgres'..." -ForegroundColor Yellow
& "D:\PostgreSQL\17\bin\psql.exe" -U postgres -c "ALTER USER postgres PASSWORD 'postgres';"

# Restore original pg_hba.conf
Write-Host "Restoring original authentication settings..." -ForegroundColor Yellow
Copy-Item $backupPath $pgHbaPath -Force

# Restart PostgreSQL service
Write-Host "Restarting PostgreSQL service..." -ForegroundColor Yellow
Restart-Service -Name "postgresql-x64-17"

Write-Host "Password reset complete! The postgres password is now 'postgres'" -ForegroundColor Green
Write-Host "Press any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
