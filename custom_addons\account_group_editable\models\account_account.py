from odoo import fields, models


class AccountAccount(models.Model):
    _inherit = 'account.account'

    # Override the group_id field to make it editable
    group_id = fields.Many2one(
        'account.group', 
        string='Account Group',
        compute='_compute_account_group', 
        store=True, 
        readonly=False,  # Make it editable
        help="Account prefixes can determine account groups, but you can also set it manually."
    )
