from odoo import fields, models, api


class AccountAccount(models.Model):
    _inherit = 'account.account'

    # Completely redefine the group_id field without compute dependency
    group_id = fields.Many2one(
        'account.group',
        string='Account Group',
        store=True,
        readonly=False,
        help="Select the account group manually. This field is no longer automatically computed.",
        # Remove all compute-related attributes
        compute=False,
        inverse=False,
        search=False,
    )

    # Override the compute method to do nothing (disable automatic computation)
    def _compute_account_group(self):
        # Do nothing - disable automatic group computation
        return

    # Override the method that automatically adapts accounts for account groups
    def _adapt_accounts_for_account_groups(self, account_ids=None, company=None):
        # Do nothing - disable automatic group adaptation
        return

    # Override the write method to ensure group_id changes are saved
    def write(self, vals):
        return super(AccountAccount, self).write(vals)

    # Override create method to ensure group_id is properly set
    @api.model_create_multi
    def create(self, vals_list):
        return super(AccountAccount, self).create(vals_list)
