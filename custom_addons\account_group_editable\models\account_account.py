from odoo import fields, models, api, _
from odoo.exceptions import UserError


class AccountAccount(models.Model):
    _inherit = 'account.account'

    def _setup_fields(self):
        super()._setup_fields()
        # Force the group_id field to be non-readonly and non-computed
        if 'group_id' in self._fields:
            field = self._fields['group_id']
            field.readonly = False
            field.compute = None
            field.inverse = None
            field.depends = ()
            field.store = True

    # Override the compute method to do nothing (disable automatic computation)
    def _compute_account_group(self):
        # Do nothing - disable automatic group computation
        pass

    # Override the method that automatically adapts accounts for account groups
    def _adapt_accounts_for_account_groups(self, account_ids=None, company=None):
        # Do nothing - disable automatic group adaptation
        pass

    # Override any method that might trigger automatic group assignment
    @api.model
    def _get_account_group(self, account_code, company):
        # Return None to prevent automatic group assignment
        return None

    # Ensure write operations work properly
    def write(self, vals):
        # Allow group_id to be written without restrictions
        return super(AccountAccount, self).write(vals)
