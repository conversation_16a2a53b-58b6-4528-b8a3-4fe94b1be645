from odoo import fields, models, api


class AccountAccount(models.Model):
    _inherit = 'account.account'

    # Completely override the group_id field to remove compute dependency
    group_id = fields.Many2one(
        'account.group',
        string='Account Group',
        store=True,
        readonly=False,  # Make it editable
        help="Select the account group manually. This field is no longer automatically computed."
    )

    # Override the compute method to do nothing (disable automatic computation)
    @api.depends('code')
    def _compute_account_group(self):
        # Do nothing - disable automatic group computation
        pass

    # Override the method that automatically adapts accounts for account groups
    def _adapt_accounts_for_account_groups(self, account_ids=None, company=None):
        # Do nothing - disable automatic group adaptation
        pass
