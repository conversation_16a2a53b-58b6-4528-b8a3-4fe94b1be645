This Module manages the assets owned by a company. It will keep track of
depreciation's occurred on those assets. And it allows to create
accounting entries from the depreciation lines.

The full asset life-cycle is managed (from asset creation to asset
removal).

Assets can be created manually as well as automatically (via the
creation of an accounting entry on the asset account).

Depreciation Journal Entries can be created manually in the "Deprecation
Board" tab, or automatically by two ways:

- Using the "Invoicing/Assets/Compute Assets" wizard.
- Activating the "Asset Management: Generate assets" cron.

These options are compatibles each other.

The module contains a large number of functional enhancements compared
to the standard account_asset module from Odoo.
