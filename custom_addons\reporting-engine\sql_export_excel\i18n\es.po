# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sql_export_excel
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-10-15 19:36+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: none\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__col_position
msgid "Column Position"
msgstr "Posición de la columna"

#. module: sql_export_excel
#: model:ir.model.fields.selection,name:sql_export_excel.selection__sql_export__file_format__excel
msgid "Excel"
msgstr "Excel"

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__attachment_id
msgid "Excel Template"
msgstr "Plantilla Excel"

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__file_format
msgid "File Format"
msgstr "Formato del archivo"

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__header
msgid "Header"
msgstr "Cabecera"

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__attachment_id
msgid ""
"If you configure an excel file (in xlsx format) here, the result of the query will be injected in it.\n"
"It is usefull to feed data in a excel file pre-configured with calculation"
msgstr ""
"Si configura aquí un fichero excel (en formato xlsx), el resultado de la "
"consulta se inyectará en él.\n"
"Es útil para alimentar los datos en un archivo excel preconfigurado con "
"cálculo"

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__col_position
msgid "Indicate from which column the result of the query should be injected."
msgstr ""
"Indique a partir de qué columna debe inyectarse el resultado de la consulta."

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__row_position
msgid "Indicate from which row the result of the query should be injected."
msgstr ""
"Indica a partir de qué fila debe inyectarse el resultado de la consulta."

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__header
msgid "Indicate if the header should be exported to the file."
msgstr "Indique si la cabecera debe exportarse al fichero."

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__sheet_position
msgid ""
"Indicate the sheet's position of the excel template where the result of the "
"sql query should be injected."
msgstr ""
"Indique la posición de la hoja de la plantilla excel donde debe inyectarse "
"el resultado de la consulta sql."

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__row_position
msgid "Row Position"
msgstr "Posición de la fila"

#. module: sql_export_excel
#: model:ir.model,name:sql_export_excel.model_sql_export
msgid "SQL export"
msgstr "Exportar SQL"

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__sheet_position
msgid "Sheet Position"
msgstr "Posición de la hoja"

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid ""
"The Excel Template file contains less than %s sheets Please, adjust the "
"Sheet Position parameter."
msgstr ""
"El archivo Plantilla Excel contiene menos de %s hojas Por favor, ajuste el "
"parámetro Posición de la Hoja."

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid "The column position can't be less than 1."
msgstr "La posición de la columna no puede ser inferior a 1."

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid "The row position can't be less than 1."
msgstr "La posición de la fila no puede ser inferior a 1."

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid "The sheet position can't be less than 1."
msgstr "La posición de la hoja no puede ser inferior a 1."
