# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date
from dateutil.relativedelta import relativedelta


class FinancialPlanningWizard(models.TransientModel):
    _name = 'financial.planning.wizard'
    _description = 'Financial Planning Wizard'

    name = fields.Char(string='Plan Name', required=True, default='New Financial Plan')
    description = fields.Text(string='Description')
    
    # Launch and planning
    launch_date = fields.Date(string='Launch Date', required=True, default=fields.Date.today)
    base_currency_id = fields.Many2one('res.currency', string='Base Currency', required=True, 
                                      default=lambda self: self.env.company.currency_id)
    
    # Target markets
    continent_ids = fields.Many2many('financial.planning.continent', string='Target Continents')
    country_ids = fields.Many2many('financial.planning.country', string='Target Countries')
    city_ids = fields.Many2many('financial.planning.city', string='Target Cities')
    
    # Financial parameters
    initial_investment = fields.Monetary(string='Initial Investment', currency_field='base_currency_id')
    target_revenue_year_1 = fields.Monetary(string='Target Revenue Year 1', currency_field='base_currency_id')
    target_revenue_year_5 = fields.Monetary(string='Target Revenue Year 5', currency_field='base_currency_id')
    revenue_per_capita = fields.Monetary(string='Revenue per Capita', currency_field='base_currency_id')
    
    # Growth assumptions
    annual_revenue_growth_rate = fields.Float(string='Annual Revenue Growth Rate (%)', digits=(5, 2), default=15.0)
    market_penetration_rate = fields.Float(string='Market Penetration Rate (%)', digits=(5, 2), default=5.0)
    target_population_coverage = fields.Float(string='Target Population Coverage (%)', digits=(5, 2), default=10.0)
    customer_acquisition_cost = fields.Monetary(string='Customer Acquisition Cost', currency_field='base_currency_id')
    
    # Wizard steps
    step = fields.Selection([
        ('basic', 'Basic Information'),
        ('markets', 'Target Markets'),
        ('financial', 'Financial Parameters'),
        ('review', 'Review & Create')
    ], string='Step', default='basic')
    
    # Computed fields
    total_target_population = fields.Float(string='Total Target Population (M)', compute='_compute_target_population')
    potential_market_size = fields.Monetary(string='Potential Market Size', compute='_compute_market_size', 
                                          currency_field='base_currency_id')

    @api.depends('country_ids', 'city_ids')
    def _compute_target_population(self):
        for wizard in self:
            total_population = 0.0
            
            # Add population from selected countries
            if wizard.country_ids:
                total_population += sum(wizard.country_ids.mapped('population'))
            
            # Add population from selected cities (if not already counted via countries)
            if wizard.city_ids:
                city_countries = wizard.city_ids.mapped('country_id')
                cities_not_in_countries = wizard.city_ids.filtered(lambda c: c.country_id not in wizard.country_ids)
                total_population += sum(cities_not_in_countries.mapped('population'))
            
            wizard.total_target_population = total_population

    @api.depends('total_target_population', 'revenue_per_capita', 'target_population_coverage')
    def _compute_market_size(self):
        for wizard in self:
            if wizard.total_target_population and wizard.revenue_per_capita and wizard.target_population_coverage:
                covered_population = wizard.total_target_population * (wizard.target_population_coverage / 100)
                wizard.potential_market_size = covered_population * 1000000 * wizard.revenue_per_capita
            else:
                wizard.potential_market_size = 0.0

    def action_next_step(self):
        """Move to next step in wizard"""
        self.ensure_one()
        
        if self.step == 'basic':
            self.step = 'markets'
        elif self.step == 'markets':
            self.step = 'financial'
        elif self.step == 'financial':
            self.step = 'review'
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_previous_step(self):
        """Move to previous step in wizard"""
        self.ensure_one()
        
        if self.step == 'review':
            self.step = 'financial'
        elif self.step == 'financial':
            self.step = 'markets'
        elif self.step == 'markets':
            self.step = 'basic'
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_create_plan(self):
        """Create the financial plan"""
        self.ensure_one()
        
        # Create the financial plan
        plan_vals = {
            'name': self.name,
            'description': self.description,
            'launch_date': self.launch_date,
            'base_currency_id': self.base_currency_id.id,
            'continent_ids': [(6, 0, self.continent_ids.ids)],
            'country_ids': [(6, 0, self.country_ids.ids)],
            'city_ids': [(6, 0, self.city_ids.ids)],
            'initial_investment': self.initial_investment,
            'target_revenue_year_1': self.target_revenue_year_1,
            'target_revenue_year_5': self.target_revenue_year_5,
            'revenue_per_capita': self.revenue_per_capita,
            'annual_revenue_growth_rate': self.annual_revenue_growth_rate,
            'market_penetration_rate': self.market_penetration_rate,
            'target_population_coverage': self.target_population_coverage,
            'customer_acquisition_cost': self.customer_acquisition_cost,
            'state': 'draft',
        }
        
        plan = self.env['financial.planning.plan'].create(plan_vals)
        
        # Generate forecasts automatically
        plan.action_generate_forecasts()
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.plan',
            'res_id': plan.id,
            'view_mode': 'form',
            'target': 'current',
        }

    @api.onchange('country_ids')
    def _onchange_country_ids(self):
        """Update continent selection based on countries"""
        if self.country_ids:
            continents = self.country_ids.mapped('continent_id')
            self.continent_ids = [(6, 0, continents.ids)]

    def get_plan_summary(self):
        """Get plan summary for review step"""
        self.ensure_one()
        return {
            'plan_name': self.name,
            'launch_date': self.launch_date,
            'target_countries': len(self.country_ids),
            'target_cities': len(self.city_ids),
            'total_target_population': self.total_target_population,
            'potential_market_size': self.potential_market_size,
            'initial_investment': self.initial_investment,
            'target_revenue_year_1': self.target_revenue_year_1,
            'annual_growth_rate': self.annual_revenue_growth_rate,
            'market_penetration_rate': self.market_penetration_rate,
        }
