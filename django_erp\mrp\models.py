from django.db import models
from django.db.models import Q, Sum, F
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta, datetime
from core.models import BaseModel, Company, Partner, Currency
from inventory.models import Product, ProductTemplate, StockLocation, StockMove, StockPicking
from sales.models import ProductUom, ProcurementGroup
from purchases.models import StockPickingType
# from accounting.models import AccountAnalyticAccount  # Will be added later


# ===== BILL OF MATERIALS (BOM) MODELS =====

class MrpBom(BaseModel):
    """Bill of Materials - equivalent to mrp.bom in Odoo"""
    
    BOM_TYPE_CHOICES = [
        ('normal', 'Manufacture this product'),
        ('phantom', 'Kit'),
        ('subcontract', 'Subcontracting'),
    ]
    
    # Basic Information
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE, 
                                       help_text="Product Template")
    product_id = models.ForeignKey(Product, on_delete=models.CASCADE, null=True, blank=True,
                                  help_text="Product Variant")
    product_qty = models.DecimalField(max_digits=20, decimal_places=4, default=1.0,
                                     help_text="Product Quantity")
    product_uom_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                                      help_text="Product Unit of Measure")
    
    # BOM Configuration
    type = models.CharField(max_length=20, choices=BOM_TYPE_CHOICES, default='normal',
                           help_text="BOM Type")
    code = models.CharField(max_length=255, blank=True, help_text="Reference")
    version = models.CharField(max_length=255, blank=True, help_text="Version")
    
    # Manufacturing Details
    routing_id = models.ForeignKey('MrpRouting', on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Manufacturing Routing")
    ready_to_produce = models.CharField(max_length=20, choices=[
        ('all_available', 'When all components are available'),
        ('asap', 'When components for 1st operation are available'),
    ], default='all_available', help_text="Manufacturing Readiness")
    
    # Dates and Validity
    active = models.BooleanField(default=True, help_text="Active")
    sequence = models.IntegerField(default=1, help_text="Sequence")
    
    # Subcontracting
    subcontractor_ids = models.ManyToManyField(Partner, blank=True,
                                             help_text="Subcontractors")
    
    # Consumption and Production
    consumption = models.CharField(max_length=20, choices=[
        ('flexible', 'Allowed'),
        ('warning', 'Allowed with warning'),
        ('strict', 'Blocked'),
    ], default='flexible', help_text="Flexible Consumption")
    
    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    
    class Meta:
        indexes = [
            models.Index(fields=['product_tmpl_id', 'active']),
            models.Index(fields=['product_id', 'active']),
            models.Index(fields=['type']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(product_qty__gt=0),
                name='mrp_bom_qty_positive'
            ),
        ]
    
    def __str__(self):
        return f"BOM: {self.product_tmpl_id.name}"
    
    def _compute_exploded_bom(self, product_qty=1.0, picking_type=None):
        """Compute exploded BOM structure"""
        exploded_boms = []
        exploded_lines = []
        
        def _explode_bom(bom, product_qty, level=0):
            for line in bom.bom_line_ids.all():
                line_qty = line.product_qty * Decimal(str(product_qty)) / Decimal(str(bom.product_qty))
                
                # Check if component has its own BOM
                component_bom = self._bom_find(line.product_id)
                if component_bom and component_bom.type == 'phantom':
                    # Explode phantom BOM
                    _explode_bom(component_bom, line_qty, level + 1)
                else:
                    exploded_lines.append({
                        'product_id': line.product_id,
                        'product_qty': line_qty,
                        'product_uom_id': line.product_uom_id,
                        'level': level,
                        'bom_line': line,
                    })
        
        _explode_bom(self, product_qty)
        return exploded_lines
    
    @classmethod
    def _bom_find(cls, product, company_id=None, bom_type='normal'):
        """Find appropriate BOM for product"""
        from django.db.models import Q

        # Build base query
        query = Q(product_tmpl_id=product.product_tmpl_id.id) & Q(active=True) & Q(type=bom_type)

        # Add product variant filter
        if product.id:
            query = query & (Q(product_id=product.id) | Q(product_id__isnull=True))

        # Add company filter
        if company_id:
            query = query & Q(company_id=company_id)

        return cls.objects.filter(query).order_by('sequence', 'product_id').first()


class MrpBomLine(BaseModel):
    """BOM Line - equivalent to mrp.bom.line in Odoo"""
    
    # BOM Reference
    bom_id = models.ForeignKey(MrpBom, on_delete=models.CASCADE, related_name='bom_line_ids')
    
    # Product Information
    product_id = models.ForeignKey(Product, on_delete=models.CASCADE, help_text="Component")
    product_qty = models.DecimalField(max_digits=20, decimal_places=4, default=1.0,
                                     help_text="Quantity")
    product_uom_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                                      help_text="Unit of Measure")
    
    # Operation and Routing
    operation_id = models.ForeignKey('MrpRoutingWorkcenter', on_delete=models.SET_NULL, 
                                    null=True, blank=True, help_text="Consumed in Operation")
    
    # Configuration
    sequence = models.IntegerField(default=1, help_text="Sequence")
    manual_consumption = models.BooleanField(default=False, help_text="Manual Consumption")
    
    # Attributes and Variants
    attribute_value_ids = models.ManyToManyField('ProductAttributeValue', blank=True,
                                               help_text="Apply on Variants")
    
    class Meta:
        indexes = [
            models.Index(fields=['bom_id', 'sequence']),
            models.Index(fields=['product_id']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(product_qty__gt=0),
                name='mrp_bom_line_qty_positive'
            ),
        ]
    
    def __str__(self):
        return f"{self.product_id.name} ({self.product_qty})"


# ===== ROUTING AND WORK CENTER MODELS =====

class MrpWorkcenter(BaseModel):
    """Work Center - equivalent to mrp.workcenter in Odoo"""
    
    # Basic Information
    name = models.CharField(max_length=255, help_text="Work Center Name")
    code = models.CharField(max_length=50, blank=True, help_text="Code")
    active = models.BooleanField(default=True, help_text="Active")
    sequence = models.IntegerField(default=1, help_text="Sequence")
    color = models.IntegerField(default=0, help_text="Color Index")
    
    # Capacity and Time
    time_efficiency = models.DecimalField(max_digits=5, decimal_places=2, default=100.0,
                                        help_text="Time Efficiency (%)")
    capacity = models.DecimalField(max_digits=10, decimal_places=2, default=1.0,
                                  help_text="Working Capacity")
    
    # Costs
    costs_hour = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                   help_text="Cost per Hour")
    time_start = models.DecimalField(max_digits=10, decimal_places=2, default=0.0,
                                   help_text="Setup Time (minutes)")
    time_stop = models.DecimalField(max_digits=10, decimal_places=2, default=0.0,
                                  help_text="Cleanup Time (minutes)")
    
    # Location and Resources
    default_capacity = models.DecimalField(max_digits=10, decimal_places=2, default=1.0,
                                         help_text="Default Capacity")
    
    # OEE (Overall Equipment Effectiveness)
    oee_target = models.DecimalField(max_digits=5, decimal_places=2, default=90.0,
                                   help_text="OEE Target (%)")
    
    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    
    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['active']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(capacity__gt=0),
                name='mrp_workcenter_capacity_positive'
            ),
            models.CheckConstraint(
                check=models.Q(time_efficiency__gt=0),
                name='mrp_workcenter_efficiency_positive'
            ),
        ]
    
    def __str__(self):
        return self.name


class MrpRouting(BaseModel):
    """Routing - equivalent to mrp.routing in Odoo"""
    
    # Basic Information
    name = models.CharField(max_length=255, help_text="Routing Name")
    code = models.CharField(max_length=50, blank=True, help_text="Reference")
    active = models.BooleanField(default=True, help_text="Active")
    note = models.TextField(blank=True, help_text="Description")
    
    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    
    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['active']),
        ]
    
    def __str__(self):
        return self.name


class MrpRoutingWorkcenter(BaseModel):
    """Routing Operation - equivalent to mrp.routing.workcenter in Odoo"""
    
    # Routing Reference
    routing_id = models.ForeignKey(MrpRouting, on_delete=models.CASCADE, 
                                  related_name='operation_ids')
    
    # Basic Information
    name = models.CharField(max_length=255, help_text="Operation Name")
    workcenter_id = models.ForeignKey(MrpWorkcenter, on_delete=models.CASCADE,
                                     help_text="Work Center")
    sequence = models.IntegerField(default=100, help_text="Sequence")
    note = models.TextField(blank=True, help_text="Description")
    
    # Time Configuration
    time_mode = models.CharField(max_length=20, choices=[
        ('auto', 'Compute based on tracked time'),
        ('manual', 'Set duration manually'),
    ], default='auto', help_text="Duration Computation")
    
    time_mode_batch = models.IntegerField(default=10, help_text="Based on")
    time_cycle_manual = models.DecimalField(max_digits=10, decimal_places=2, default=60.0,
                                          help_text="Manual Duration (minutes)")
    time_cycle = models.DecimalField(max_digits=10, decimal_places=2, default=60.0,
                                   help_text="Duration (minutes)")
    
    # Batch Configuration
    batch = models.CharField(max_length=20, choices=[
        ('no', 'Once all products are processed'),
        ('yes', 'Once some products are processed'),
    ], default='no', help_text="Next Operation")
    batch_size = models.DecimalField(max_digits=10, decimal_places=2, default=1.0,
                                   help_text="Batch Size")
    
    # Quality and Skills
    quality_point_ids = models.ManyToManyField('QualityPoint', blank=True,
                                             help_text="Quality Control Points")
    skill_ids = models.ManyToManyField('HrSkill', blank=True,
                                     help_text="Required Skills")
    
    class Meta:
        indexes = [
            models.Index(fields=['routing_id', 'sequence']),
            models.Index(fields=['workcenter_id']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(time_cycle__gt=0),
                name='mrp_routing_workcenter_time_positive'
            ),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.workcenter_id.name})"


# ===== MANUFACTURING ORDER MODELS =====

class MrpProduction(BaseModel):
    """Manufacturing Order - equivalent to mrp.production in Odoo"""

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('progress', 'In Progress'),
        ('to_close', 'To Close'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('0', 'Not urgent'),
        ('1', 'Normal'),
        ('2', 'Urgent'),
        ('3', 'Very Urgent'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, default='New', help_text="Reference")
    origin = models.CharField(max_length=255, blank=True, help_text="Source Document")
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')
    priority = models.CharField(max_length=1, choices=PRIORITY_CHOICES, default='1')

    # Product Information
    product_id = models.ForeignKey(Product, on_delete=models.CASCADE, help_text="Product to Produce")
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE,
                                       help_text="Product Template")
    product_qty = models.DecimalField(max_digits=20, decimal_places=4, default=1.0,
                                     help_text="Quantity to Produce")
    product_uom_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                                      help_text="Product Unit of Measure")
    qty_producing = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                       help_text="Currently Producing")
    qty_produced = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                      help_text="Quantity Produced")

    # BOM and Routing
    bom_id = models.ForeignKey(MrpBom, on_delete=models.SET_NULL, null=True, blank=True,
                              help_text="Bill of Materials")
    routing_id = models.ForeignKey(MrpRouting, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Routing")

    # Dates
    date_planned_start = models.DateTimeField(help_text="Planned Start Date")
    date_planned_finished = models.DateTimeField(help_text="Planned End Date")
    date_start = models.DateTimeField(null=True, blank=True, help_text="Actual Start Date")
    date_finished = models.DateTimeField(null=True, blank=True, help_text="Actual End Date")
    date_deadline = models.DateTimeField(null=True, blank=True, help_text="Deadline")

    # Locations
    location_src_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                       related_name='mrp_productions_src',
                                       help_text="Raw Materials Location")
    location_dest_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                        related_name='mrp_productions_dest',
                                        help_text="Finished Products Location")

    # Procurement and Planning
    procurement_group_id = models.ForeignKey('sales.ProcurementGroup', on_delete=models.SET_NULL,
                                           null=True, blank=True, help_text="Procurement Group")
    # orderpoint_id = models.ForeignKey('inventory.StockWarehouseOrderpoint', on_delete=models.SET_NULL,
    #                                  null=True, blank=True, help_text="Reordering Rule")

    # Production Configuration
    picking_type_id = models.ForeignKey('purchases.StockPickingType', on_delete=models.PROTECT,
                                       help_text="Operation Type")
    reservation_state = models.CharField(max_length=20, choices=[
        ('confirmed', 'Waiting'),
        ('assigned', 'Available'),
        ('waiting', 'Waiting Another Move'),
        ('partially_available', 'Partially Available'),
    ], default='confirmed', help_text="Reservation State")

    # Analytics and Tracking
    # analytic_account_id = models.ForeignKey(AccountAnalyticAccount, on_delete=models.SET_NULL,
    #                                       null=True, blank=True, help_text="Analytic Account")
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Responsible")

    # Extra Information
    extra_cost = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                   help_text="Extra Unit Cost")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['state']),
            models.Index(fields=['product_id']),
            models.Index(fields=['date_planned_start']),
            models.Index(fields=['priority', 'date_planned_start']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(product_qty__gt=0),
                name='mrp_production_qty_positive'
            ),
        ]

    def __str__(self):
        return self.name

    def action_confirm(self):
        """Confirm the manufacturing order"""
        if self.state != 'draft':
            raise ValidationError("Only draft manufacturing orders can be confirmed.")

        # Generate sequence number if needed
        if self.name == 'New':
            self.name = self._get_sequence_number()

        # Create stock moves for components and finished product
        self._create_stock_moves()

        # Create work orders if routing exists
        if self.routing_id:
            self._create_workorders()

        # Update state
        self.state = 'confirmed'
        self.save()

        return True

    def action_assign(self):
        """Reserve raw materials"""
        if self.state not in ['confirmed', 'progress']:
            raise ValidationError("Cannot reserve materials for this manufacturing order.")

        # Reserve raw materials
        for move in self.move_raw_ids.all():
            move.action_assign()

        # Update reservation state
        self._compute_reservation_state()

        return True

    def button_plan(self):
        """Plan the manufacturing order"""
        if self.state != 'confirmed':
            raise ValidationError("Only confirmed manufacturing orders can be planned.")

        # Plan work orders
        for workorder in self.workorder_ids.all():
            workorder.button_plan()

        return True

    def button_mark_done(self):
        """Mark manufacturing order as done"""
        if self.state not in ['progress', 'to_close']:
            raise ValidationError("Cannot mark this manufacturing order as done.")

        # Validate all work orders are done
        if self.workorder_ids.filter(state__in=['pending', 'ready', 'progress']).exists():
            raise ValidationError("All work orders must be completed first.")

        # Process finished product
        self._post_inventory()

        # Update state
        self.state = 'done'
        self.date_finished = timezone.now()
        self.save()

        return True

    def _get_sequence_number(self):
        """Generate sequence number for manufacturing order"""
        # Simplified sequence generation
        last_mo = MrpProduction.objects.filter(
            company_id=self.company_id,
            name__startswith='MO'
        ).order_by('-id').first()

        if last_mo and last_mo.name.startswith('MO'):
            try:
                last_num = int(last_mo.name[2:])
                return f"MO{last_num + 1:05d}"
            except ValueError:
                pass

        return "MO00001"

    def _create_stock_moves(self):
        """Create stock moves for raw materials and finished product"""
        from inventory.models import StockMove

        # Create move for finished product
        StockMove.objects.create(
            name=f"{self.name}: {self.product_id.name}",
            product_id=self.product_id,
            product_uom_qty=self.product_qty,
            product_uom=self.product_uom_id,
            location_id=self.location_src_id,
            location_dest_id=self.location_dest_id,
            production_id=self,
            company_id=self.company_id,
            create_uid=self.create_uid,
            write_uid=self.write_uid,
        )

        # Create moves for raw materials
        if self.bom_id:
            exploded_lines = self.bom_id._compute_exploded_bom(self.product_qty)
            for line_data in exploded_lines:
                StockMove.objects.create(
                    name=f"{self.name}: {line_data['product_id'].name}",
                    product_id=line_data['product_id'],
                    product_uom_qty=line_data['product_qty'],
                    product_uom=line_data['product_uom_id'],
                    location_id=self.location_src_id,
                    location_dest_id=self.location_dest_id,  # Consumed location
                    raw_material_production_id=self,
                    company_id=self.company_id,
                    create_uid=self.create_uid,
                    write_uid=self.write_uid,
                )

    def _create_workorders(self):
        """Create work orders from routing"""
        if not self.routing_id:
            return

        for operation in self.routing_id.operation_ids.all():
            MrpWorkorder.objects.create(
                name=f"{self.name} - {operation.name}",
                production_id=self,
                operation_id=operation,
                workcenter_id=operation.workcenter_id,
                state='pending',
                qty_production=self.product_qty,
                company_id=self.company_id,
                create_uid=self.create_uid,
                write_uid=self.write_uid,
            )

    def _compute_reservation_state(self):
        """Compute reservation state based on raw material availability"""
        raw_moves = self.move_raw_ids.all()
        if not raw_moves:
            self.reservation_state = 'assigned'
            return

        assigned_moves = raw_moves.filter(state='assigned').count()
        total_moves = raw_moves.count()

        if assigned_moves == 0:
            self.reservation_state = 'confirmed'
        elif assigned_moves == total_moves:
            self.reservation_state = 'assigned'
        else:
            self.reservation_state = 'partially_available'

        self.save()

    def _post_inventory(self):
        """Post inventory moves when manufacturing is done"""
        # Process finished product move
        finished_move = self.move_finished_ids.first()
        if finished_move:
            finished_move.quantity_done = self.qty_produced or self.product_qty
            finished_move.action_done()

        # Process raw material moves
        for move in self.move_raw_ids.all():
            if move.state not in ['done', 'cancel']:
                move.quantity_done = move.product_uom_qty
                move.action_done()


# ===== WORK ORDER MODELS =====

class MrpWorkorder(BaseModel):
    """Work Order - equivalent to mrp.workorder in Odoo"""

    STATE_CHOICES = [
        ('pending', 'Waiting for another WO'),
        ('waiting', 'Waiting for components'),
        ('ready', 'Ready'),
        ('progress', 'In Progress'),
        ('done', 'Finished'),
        ('cancel', 'Cancelled'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, help_text="Work Order Name")
    production_id = models.ForeignKey(MrpProduction, on_delete=models.CASCADE,
                                     related_name='workorder_ids', help_text="Manufacturing Order")
    operation_id = models.ForeignKey(MrpRoutingWorkcenter, on_delete=models.CASCADE,
                                    help_text="Operation")
    workcenter_id = models.ForeignKey(MrpWorkcenter, on_delete=models.CASCADE,
                                     help_text="Work Center")

    # State and Progress
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='pending')
    qty_production = models.DecimalField(max_digits=20, decimal_places=4, default=1.0,
                                        help_text="Original Production Quantity")
    qty_produced = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                      help_text="Quantity Produced")
    qty_producing = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                       help_text="Currently Producing")
    qty_remaining = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                       help_text="Quantity Remaining")

    # Time Tracking
    date_planned_start = models.DateTimeField(null=True, blank=True, help_text="Planned Start")
    date_planned_finished = models.DateTimeField(null=True, blank=True, help_text="Planned End")
    date_start = models.DateTimeField(null=True, blank=True, help_text="Actual Start")
    date_finished = models.DateTimeField(null=True, blank=True, help_text="Actual End")

    # Duration and Costs
    duration_expected = models.DecimalField(max_digits=10, decimal_places=2, default=0.0,
                                          help_text="Expected Duration (minutes)")
    duration = models.DecimalField(max_digits=10, decimal_places=2, default=0.0,
                                  help_text="Real Duration (minutes)")
    duration_unit = models.DecimalField(max_digits=10, decimal_places=2, default=0.0,
                                       help_text="Duration Per Unit (minutes)")
    duration_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                          help_text="Duration Deviation (%)")

    # Costs
    costs_hour = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                   help_text="Cost per Hour")

    # Quality and Instructions
    note = models.TextField(blank=True, help_text="Instructions")
    worksheet_type = models.CharField(max_length=20, choices=[
        ('pdf', 'PDF'),
        ('google_slide', 'Google Slide'),
        ('text', 'Text'),
    ], default='text', help_text="Worksheet Type")
    worksheet = models.TextField(blank=True, help_text="Worksheet Content")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    class Meta:
        indexes = [
            models.Index(fields=['production_id', 'state']),
            models.Index(fields=['workcenter_id', 'state']),
            models.Index(fields=['date_planned_start']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(qty_production__gt=0),
                name='mrp_workorder_qty_positive'
            ),
        ]

    def __str__(self):
        return self.name

    def button_start(self):
        """Start the work order"""
        if self.state != 'ready':
            raise ValidationError("Work order must be ready to start.")

        self.state = 'progress'
        self.date_start = timezone.now()
        self.save()

        # Start production if this is the first work order
        if self.production_id.state == 'confirmed':
            self.production_id.state = 'progress'
            self.production_id.date_start = timezone.now()
            self.production_id.save()

        return True

    def button_finish(self):
        """Finish the work order"""
        if self.state != 'progress':
            raise ValidationError("Work order must be in progress to finish.")

        self.state = 'done'
        self.date_finished = timezone.now()
        self.qty_produced = self.qty_producing or self.qty_production

        # Calculate actual duration
        if self.date_start:
            duration_seconds = (timezone.now() - self.date_start).total_seconds()
            self.duration = duration_seconds / 60  # Convert to minutes

        self.save()

        # Check if all work orders are done
        production = self.production_id
        if not production.workorder_ids.filter(state__in=['pending', 'ready', 'progress']).exists():
            production.state = 'to_close'
            production.save()

        return True

    def button_plan(self):
        """Plan the work order"""
        if self.state != 'pending':
            return

        # Simple planning logic
        self.state = 'ready'
        if not self.date_planned_start:
            self.date_planned_start = timezone.now()
        if not self.date_planned_finished and self.duration_expected:
            self.date_planned_finished = self.date_planned_start + timedelta(
                minutes=float(self.duration_expected)
            )

        self.save()
        return True


# ===== BYPRODUCT MODELS =====

class MrpByproduct(BaseModel):
    """Byproduct - equivalent to mrp.bom.byproduct in Odoo"""

    # BOM Reference
    bom_id = models.ForeignKey(MrpBom, on_delete=models.CASCADE, related_name='byproduct_ids')

    # Product Information
    product_id = models.ForeignKey(Product, on_delete=models.CASCADE, help_text="Byproduct")
    product_qty = models.DecimalField(max_digits=20, decimal_places=4, default=1.0,
                                     help_text="Quantity")
    product_uom_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                                      help_text="Unit of Measure")

    # Operation
    operation_id = models.ForeignKey(MrpRoutingWorkcenter, on_delete=models.SET_NULL,
                                    null=True, blank=True, help_text="Produced in Operation")

    # Configuration
    sequence = models.IntegerField(default=1, help_text="Sequence")
    cost_share = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                   help_text="Cost Share (%)")

    class Meta:
        indexes = [
            models.Index(fields=['bom_id']),
            models.Index(fields=['product_id']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(product_qty__gt=0),
                name='mrp_byproduct_qty_positive'
            ),
        ]

    def __str__(self):
        return f"{self.product_id.name} ({self.product_qty})"


# ===== UNBUILD MODELS =====

class MrpUnbuild(BaseModel):
    """Unbuild Order - equivalent to mrp.unbuild in Odoo"""

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('done', 'Done'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, default='New', help_text="Reference")
    product_id = models.ForeignKey(Product, on_delete=models.CASCADE, help_text="Product to Unbuild")
    product_qty = models.DecimalField(max_digits=20, decimal_places=4, default=1.0,
                                     help_text="Quantity")
    product_uom_id = models.ForeignKey(ProductUom, on_delete=models.PROTECT,
                                      help_text="Unit of Measure")

    # BOM and State
    bom_id = models.ForeignKey(MrpBom, on_delete=models.SET_NULL, null=True, blank=True,
                              help_text="Bill of Materials")
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')

    # Locations
    location_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                   related_name='unbuild_orders_src',
                                   help_text="Source Location")
    location_dest_id = models.ForeignKey(StockLocation, on_delete=models.PROTECT,
                                        related_name='unbuild_orders_dest',
                                        help_text="Destination Location")

    # Lot and Serial Numbers
    lot_id = models.ForeignKey('inventory.StockLot', on_delete=models.SET_NULL, null=True, blank=True,
                              help_text="Lot/Serial Number")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['state']),
            models.Index(fields=['product_id']),
        ]

    def __str__(self):
        return self.name

    def action_unbuild(self):
        """Execute the unbuild operation"""
        if self.state != 'draft':
            raise ValidationError("Only draft unbuild orders can be processed.")

        if not self.bom_id:
            raise ValidationError("Bill of Materials is required for unbuild.")

        # Generate sequence number if needed
        if self.name == 'New':
            self.name = self._get_sequence_number()

        # Create stock moves for unbuild
        self._create_unbuild_moves()

        # Update state
        self.state = 'done'
        self.save()

        return True

    def _get_sequence_number(self):
        """Generate sequence number for unbuild order"""
        last_unbuild = MrpUnbuild.objects.filter(
            company_id=self.company_id,
            name__startswith='UB'
        ).order_by('-id').first()

        if last_unbuild and last_unbuild.name.startswith('UB'):
            try:
                last_num = int(last_unbuild.name[2:])
                return f"UB{last_num + 1:05d}"
            except ValueError:
                pass

        return "UB00001"

    def _create_unbuild_moves(self):
        """Create stock moves for unbuild operation"""
        from inventory.models import StockMove

        # Move to consume the finished product
        StockMove.objects.create(
            name=f"{self.name}: {self.product_id.name}",
            product_id=self.product_id,
            product_uom_qty=self.product_qty,
            product_uom=self.product_uom_id,
            location_id=self.location_id,
            location_dest_id=self.location_dest_id,  # Virtual location for consumption
            unbuild_id=self,
            company_id=self.company_id,
            create_uid=self.create_uid,
            write_uid=self.write_uid,
        )

        # Moves to produce the components
        if self.bom_id:
            exploded_lines = self.bom_id._compute_exploded_bom(self.product_qty)
            for line_data in exploded_lines:
                StockMove.objects.create(
                    name=f"{self.name}: {line_data['product_id'].name}",
                    product_id=line_data['product_id'],
                    product_uom_qty=line_data['product_qty'],
                    product_uom=line_data['product_uom_id'],
                    location_id=self.location_id,  # Virtual location for production
                    location_dest_id=self.location_dest_id,
                    unbuild_id=self,
                    company_id=self.company_id,
                    create_uid=self.create_uid,
                    write_uid=self.write_uid,
                )


# ===== SUPPORTING MODELS =====

class ProductAttributeValue(BaseModel):
    """Product Attribute Value - placeholder for product variants"""

    name = models.CharField(max_length=255, help_text="Attribute Value")
    attribute_id = models.ForeignKey('ProductAttribute', on_delete=models.CASCADE)

    def __str__(self):
        return self.name


class ProductAttribute(BaseModel):
    """Product Attribute - placeholder for product variants"""

    name = models.CharField(max_length=255, help_text="Attribute Name")

    def __str__(self):
        return self.name


class QualityPoint(BaseModel):
    """Quality Control Point - placeholder for quality management"""

    name = models.CharField(max_length=255, help_text="Quality Point")

    def __str__(self):
        return self.name


class HrSkill(BaseModel):
    """HR Skill - placeholder for skill management"""

    name = models.CharField(max_length=255, help_text="Skill")

    def __str__(self):
        return self.name


# Import existing models from other modules
# ProcurementGroup is in sales module
# StockPickingType is in purchases module
# StockLot and StockWarehouseOrderpoint are in inventory module
