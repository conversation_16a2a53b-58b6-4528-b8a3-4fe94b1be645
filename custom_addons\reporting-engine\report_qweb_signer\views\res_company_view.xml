<?xml version="1.0" encoding="utf-8" ?>
<!--
Copyright 2015 Tecnativa - Antonio <PERSON>
Copyright 2017 Tecnativa - Pedro M<PERSON> Baeza
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).
-->
<odoo>
    <record id="view_company_form" model="ir.ui.view">
        <field name="name">Add PDF report certificates list</field>
        <field name="inherit_id" ref="base.view_company_form" />
        <field name="model">res.company</field>
        <field name="arch" type="xml">
            <data>
                <notebook position="inside">
                    <page
                        name="pdf_sign_certificate"
                        string="Certificates (PDF signing)"
                    >
                        <field
                            name="report_certificate_ids"
                            context="{'default_company_id': active_id}"
                        />
                    </page>
                </notebook>
            </data>
        </field>
    </record>
</odoo>
