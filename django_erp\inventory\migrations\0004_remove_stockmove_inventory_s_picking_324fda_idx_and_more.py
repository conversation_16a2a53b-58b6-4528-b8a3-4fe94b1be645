# Generated by Django 4.2.21 on 2025-07-20 11:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_productpackaging'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='stockmove',
            name='inventory_s_picking_324fda_idx',
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['location_id'], name='inventory_s_locatio_286e30_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['location_dest_id'], name='inventory_s_locatio_62cfce_idx'),
        ),
        migrations.AddConstraint(
            model_name='productpackaging',
            constraint=models.CheckConstraint(check=models.Q(('qty__gt', 0)), name='packaging_positive_qty'),
        ),
    ]
