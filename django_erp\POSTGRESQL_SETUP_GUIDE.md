# PostgreSQL Setup Guide for Django ERP

## 🎯 Overview

This guide will help you migrate from SQLite to PostgreSQL for the Django ERP system with database name `django_erp`.

## 📋 Prerequisites

### 1. PostgreSQL Installation

**If PostgreSQL is not installed:**

#### Windows Installation:
1. **Download**: [PostgreSQL for Windows](https://www.postgresql.org/download/windows/)
2. **Run installer**: postgresql-15.x-x-windows-x64.exe
3. **Installation settings**:
   - Port: 5432 (default)
   - Superuser password: Set a password (remember it!)
   - Locale: Default
4. **Components**: Install all components including pgAdmin
5. **Finish**: PostgreSQL service should start automatically

#### Alternative: Using Chocolatey (Windows)
```cmd
choco install postgresql
```

#### Linux Installation:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
```

#### macOS Installation:
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql
```

### 2. Python Dependencies
```bash
pip install psycopg2-binary
```

### 3. Verify PostgreSQL Installation
```bash
# Check if PostgreSQL is running
psql --version

# Check service status (Windows)
sc query postgresql-x64-15

# Check service status (Linux)
sudo systemctl status postgresql
```

## 🚀 Quick Setup (Recommended)

### Option 1: Windows Batch Script
```cmd
cd django_erp
setup_postgresql.bat
```

### Option 2: Manual SQL Script
```bash
# Start PostgreSQL service first
# Then run:
psql -U postgres -h localhost -f setup_database.sql
python manage.py migrate
```

### Option 3: Python Setup Script
```bash
cd django_erp
python setup_postgresql.py
```

The automated setup will:
- ✅ Start PostgreSQL service
- ✅ Create database `django_erp`
- ✅ Create user `ERPUser`
- ✅ Run all migrations
- ✅ Test database connection

## 🔧 Manual Setup (Alternative)

### Step 1: Start PostgreSQL Service

**Windows:**
```cmd
# Open Services (services.msc)
# Find PostgreSQL service and start it
# OR use command line:
net start postgresql-x64-14
```

**Linux/Mac:**
```bash
sudo systemctl start postgresql
# OR
brew services start postgresql
```

### Step 2: Create Database and User

**Connect to PostgreSQL:**
```bash
psql -U postgres -h localhost
```

**Create Database and User:**
```sql
-- Create database
CREATE DATABASE django_erp;

-- Create user
CREATE USER ERPUser WITH PASSWORD 'ERPUser';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE django_erp TO ERPUser;
ALTER USER ERPUser CREATEDB;

-- Exit
\q
```

### Step 3: Update Django Settings

The settings are already configured in `django_erp/settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'django_erp',
        'USER': 'ERPUser',
        'PASSWORD': 'ERPUser',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### Step 4: Install PostgreSQL Adapter

```bash
pip install psycopg2-binary
```

### Step 5: Run Migrations

```bash
cd django_erp
python manage.py makemigrations
python manage.py migrate
```

### Step 6: Create Superuser

```bash
python manage.py createsuperuser
```

### Step 7: Test Connection

```bash
python manage.py shell
```

```python
from django.db import connection
cursor = connection.cursor()
cursor.execute("SELECT version();")
print(cursor.fetchone())
```

## 🔍 Verification Steps

### 1. Check Database Connection
```bash
python manage.py dbshell
```

### 2. List Tables
```sql
\dt
```

### 3. Check Migration Status
```bash
python manage.py showmigrations
```

### 4. Run Development Server
```bash
python manage.py runserver
```

## 📊 Database Configuration Details

### Connection Parameters
- **Database Name**: `django_erp`
- **Username**: `ERPUser`
- **Password**: `ERPUser`
- **Host**: `localhost`
- **Port**: `5432`
- **Engine**: `django.db.backends.postgresql`

### Security Notes
- ⚠️ Change default passwords in production
- ⚠️ Use environment variables for sensitive data
- ⚠️ Configure proper firewall rules
- ⚠️ Enable SSL for production

## 🛠️ Troubleshooting

### Common Issues

#### 1. PostgreSQL Service Not Running
```bash
# Windows
net start postgresql-x64-14

# Linux
sudo systemctl start postgresql
```

#### 2. Authentication Failed
- Check username/password in settings.py
- Verify user exists in PostgreSQL
- Check pg_hba.conf for authentication method

#### 3. Database Does Not Exist
```sql
CREATE DATABASE django_erp;
```

#### 4. Permission Denied
```sql
GRANT ALL PRIVILEGES ON DATABASE django_erp TO ERPUser;
```

#### 5. psycopg2 Installation Issues
```bash
# Windows
pip install psycopg2-binary

# Linux (if binary fails)
sudo apt-get install libpq-dev python3-dev
pip install psycopg2
```

### Connection Test Commands

#### Test PostgreSQL Connection
```bash
psql -U ERPUser -d django_erp -h localhost
```

#### Test Django Connection
```bash
python manage.py shell -c "from django.db import connection; print(connection.vendor)"
```

## 📈 Performance Optimization

### PostgreSQL Configuration
Add to `postgresql.conf`:
```ini
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# Connection settings
max_connections = 100

# Logging
log_statement = 'all'
log_duration = on
```

### Django Settings
Add to `settings.py`:
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'django_erp',
        'USER': 'ERPUser',
        'PASSWORD': 'ERPUser',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
        'CONN_MAX_AGE': 60,
    }
}
```

## 🔐 Production Security

### Environment Variables
Create `.env` file:
```env
DB_NAME=django_erp
DB_USER=ERPUser
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432
```

### Updated Settings
```python
import os
from dotenv import load_dotenv

load_dotenv()

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
    }
}
```

## ✅ Success Checklist

- [ ] PostgreSQL service running
- [ ] Database `django_erp` created
- [ ] User `ERPUser` created with proper privileges
- [ ] psycopg2 installed
- [ ] All migrations applied successfully
- [ ] Database connection test passed
- [ ] Django superuser created
- [ ] Development server starts without errors

## 🎉 Next Steps

After successful setup:
1. **Run the development server**: `python manage.py runserver`
2. **Access admin panel**: http://localhost:8000/admin/
3. **Test all modules**: Create test data in each module
4. **Run comprehensive tests**: `python manage.py test`
5. **Set up production environment** with proper security

Your Django ERP system is now running on PostgreSQL! 🚀
