# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_chart_update
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-02-01 03:44+0000\n"
"PO-Revision-Date: 2018-02-01 03:44+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Hebrew (https://www.transifex.com/oca/teams/23907/he/)\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__account_group_ids
msgid "Account Groups"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__account_field_ids
msgid "Account fields"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_update_charts_accounts_account_group
msgid ""
"Account group that needs to be updated (new or updated in the template)."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__update_account_group_id
msgid "Account group to update"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Account groups"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__account_group_field_ids
msgid "Account groups fields"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__account_group_matching_ids
msgid "Account groups matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_update_charts_accounts_account
msgid "Account that needs to be updated (new or updated in the template)."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__update_account_id
msgid "Account to update"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__account_ids
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Accounts"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__account_matching_ids
msgid "Accounts matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__chart_template
msgid "Chart Template"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Chart of Accounts"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Close"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__code_digits
msgid "Code Digits"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__company_id
msgid "Company"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts__state__init
msgid "Configuration"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Create/Update"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__create_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__create_uid
msgid "Created by"
msgstr "נוצר על ידי"

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__create_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__deleted_taxes
msgid "Deactivated taxes"
msgstr ""

#. module: account_chart_update
#. odoo-python
#: code:addons/account_chart_update/wizard/wizard_chart_update.py:0
#, python-format
msgid "Differences in these fields: %s."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__display_name
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__display_name
msgid "Display Name"
msgstr "השם המוצג"

#. module: account_chart_update
#: model:ir.model.fields,help:account_chart_update.field_wizard_update_charts_accounts__update_account_group
msgid ""
"Existing account groups are updated. Account groups are searched by "
"prefix_code_start."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,help:account_chart_update.field_wizard_update_charts_accounts__update_account
msgid "Existing accounts are updated. Accounts are searched by code."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,help:account_chart_update.field_wizard_update_charts_accounts__update_fiscal_position
msgid ""
"Existing fiscal positions are updated. Fiscal positions are searched by name."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,help:account_chart_update.field_wizard_update_charts_accounts__update_tax
msgid "Existing taxes are updated. Taxes are searched by name."
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Field options"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__fp_field_ids
msgid "Fiscal position fields"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_update_charts_accounts_fiscal_position
msgid ""
"Fiscal position that needs to be updated (new or updated in the template)."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__update_fiscal_position_id
msgid "Fiscal position to update"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Fiscal positions"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__fp_matching_ids
msgid "Fiscal positions matching"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "General options"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid ""
"Here you can select the fields you want to check if\n"
"                            they have been updated in the templates."
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Here you can set the matching order."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__id
msgid "ID"
msgstr "מזהה"

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid ""
"If you leave these options set, the wizard will\n"
"                                not just create new records, but also "
"update\n"
"                                records with changes (i.e. different tax "
"amount)"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__write_uid
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על ידי"

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__write_date
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__write_date
msgid "Last Updated on"
msgstr "עודכן לאחרונה על"

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Log"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__matching_value
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__matching_value
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__matching_value
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__matching_value
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__matching_value
msgid "Matching Value"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__log
msgid "Messages and Errors"
msgstr ""

#. module: account_chart_update
#. odoo-python
#: code:addons/account_chart_update/wizard/wizard_chart_update.py:0
#, python-format
msgid "Missing XML-ID."
msgstr ""

#. module: account_chart_update
#. odoo-python
#: code:addons/account_chart_update/wizard/wizard_chart_update.py:0
#, python-format
msgid "Name or description not found."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__new_account_groups
msgid "New Account Groups"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__new_accounts
msgid "New Accounts"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__new_taxes
msgid "New Taxes"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_account__type__new
msgid "New account"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_account_group__type__new
msgid "New account group"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_fiscal_position__type__new
msgid "New fiscal position"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__new_fps
msgid "New fiscal positions"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_tax__type__new
msgid "New tax"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Next"
msgstr ""

#. module: account_chart_update
#. odoo-python
#: code:addons/account_chart_update/wizard/wizard_chart_update.py:0
#, python-format
msgid "No account found with this code."
msgstr ""

#. module: account_chart_update
#. odoo-python
#: code:addons/account_chart_update/wizard/wizard_chart_update.py:0
#, python-format
msgid "No fiscal position found with this name."
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Note: Only the changed fields are updated."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__notes
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__notes
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__notes
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__notes
msgid "Notes"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Previous"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Records to create/update"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__rejected_new_account_number
msgid "Rejected New Account Number"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__rejected_updated_account_number
msgid "Rejected Updated Account Number"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.res_config_settings_view_form
msgid "Reload for new elements"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts__state__ready
msgid "Select records to update"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__sequence
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__sequence
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__sequence
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__sequence
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__sequence
msgid "Sequence"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__state
msgid "Status"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Summary of created objects"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Summary of updated objects"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__tax_field_ids
msgid "Tax fields"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_update_charts_accounts_tax
msgid "Tax that needs to be updated (new or updated in the template)."
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_tax__type__deleted
msgid "Tax to deactivate"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__update_tax_id
msgid "Tax to update"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__tax_ids
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Taxes"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__tax_matching_ids
msgid "Taxes matching"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid ""
"This wizard will update your accounts, taxes and\n"
"                            fiscal positions according to the selected "
"chart\n"
"                            template"
msgstr ""

#. module: account_chart_update
#. odoo-python
#: code:addons/account_chart_update/wizard/wizard_chart_update.py:0
#, python-format
msgid "To deactivate: not in the template"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__type
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__type
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__type
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__type
msgid "Type"
msgstr "סוג"

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__type_tax_use
msgid "Type Tax Use"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__update_account_group
msgid "Update account groups"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__update_account
msgid "Update accounts"
msgstr ""

#. module: account_chart_update
#: model:ir.actions.act_window,name:account_chart_update.action_wizard_update_chart
#: model_terms:ir.ui.view,arch_db:account_chart_update.res_config_settings_view_form
msgid "Update chart of accounts"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_group_matching__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_account_matching__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_fp_matching__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_matching__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_tax_matching__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__update_chart_wizard_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__update_chart_wizard_id
msgid "Update chart wizard"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__update_fiscal_position
msgid "Update fiscal positions"
msgstr ""

#. module: account_chart_update
#: model_terms:ir.ui.view,arch_db:account_chart_update.view_update_multi_chart
msgid "Update records?"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__update_tax
msgid "Update taxes"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__updated_account_groups
msgid "Updated Account Groups"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__updated_accounts
msgid "Updated Accounts"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__updated_taxes
msgid "Updated Taxes"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_account_group__type__updated
msgid "Updated accoung group"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_account__type__updated
msgid "Updated account"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_fiscal_position__type__updated
msgid "Updated fiscal position"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts__updated_fps
msgid "Updated fiscal positions"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts_tax__type__updated
msgid "Updated tax"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_account_group_matching
msgid "Wizard Account Group Matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_account_matching
msgid "Wizard Account Matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_fp_matching
msgid "Wizard Fiscal Position Matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_matching
msgid "Wizard Matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_tax_matching
msgid "Wizard Tax Matching"
msgstr ""

#. module: account_chart_update
#: model:ir.model,name:account_chart_update.model_wizard_update_charts_accounts
msgid "Wizard Update Charts Accounts"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields.selection,name:account_chart_update.selection__wizard_update_charts_accounts__state__done
msgid "Wizard completed"
msgstr ""

#. module: account_chart_update
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account__xml_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_account_group__xml_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_fiscal_position__xml_id
#: model:ir.model.fields,field_description:account_chart_update.field_wizard_update_charts_accounts_tax__xml_id
msgid "Xml"
msgstr ""

#~ msgid "Last Modified on"
#~ msgstr "תאריך שינוי אחרון"
