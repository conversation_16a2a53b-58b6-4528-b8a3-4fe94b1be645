<?xml version="1.0" encoding="utf-8" ?>
<!--
Copyright 2015 Tecnativa - Antonio <PERSON>
Copyright 2017 Tecnativa - Pedro M<PERSON> Baeza
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).
-->
<odoo>
    <record id="view_report_certificate_form" model="ir.ui.view">
        <field name="name">report.certificate.form</field>
        <field name="model">report.certificate</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" />
                        <h1>
                            <field name="name" />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="path" />
                            <field name="password_file" />
                            <field name="model_id" />
                            <field name="signing_method" />
                        </group>
                        <group>
                            <field
                                name="domain"
                                widget="domain"
                                options="{'model': 'model_name', 'in_dialog': True}"
                                context="{'skip_search_count': 1}"
                            />
                            <field name="model_name" invisible="1" />
                            <field name="allow_only_one" />
                            <field name="attachment" />
                            <field
                                name="company_id"
                                widget="selection"
                                groups="base.group_multi_company"
                            />
                            <field name="action_report_ids" widget="many2many_tags" />
                        </group>
                        <group invisible="signing_method != 'endesive'">
                            <field
                                name="endesive_certificate_mail"
                                required="signing_method == 'endesive'"
                            />
                            <field
                                name="endesive_certificate_location"
                                required="signing_method == 'endesive'"
                            />
                            <field
                                name="endesive_certificate_reason"
                                required="signing_method == 'endesive'"
                            />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_report_certificate_tree" model="ir.ui.view">
        <field name="name">report.certificate.tree</field>
        <field name="model">report.certificate</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="path" />
                <field name="model_id" />
                <field name="domain" />
                <field name="company_id" />
            </tree>
        </field>
    </record>
    <record id="action_report_certificate" model="ir.actions.act_window">
        <field name="name">PDF certificates</field>
        <field name="res_model">report.certificate</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_report_certificate"
        name="PDF certificates"
        parent="base.reporting_menuitem"
        action="action_report_certificate"
    />
</odoo>
