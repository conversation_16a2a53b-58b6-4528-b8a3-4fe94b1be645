# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import json


class FinancialStatementAnalytics(models.Model):
    _name = 'financial.statement.analytics'
    _description = 'Financial Statement with Analytics'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_to desc, id desc'

    name = fields.Char(
        string='Statement Name',
        required=True,
        tracking=True,
        default=lambda self: _('Financial Statement %s') % fields.Date.today().strftime('%Y-%m-%d')
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        required=True,
        default=lambda self: self.env.company
    )
    
    statement_type = fields.Selection([
        ('balance_sheet', 'Statement of Financial Position (Balance Sheet)'),
        ('income_statement', 'Statement of Comprehensive Income'),
        ('cash_flow', 'Statement of Cash Flows'),
        ('equity_changes', 'Statement of Changes in Equity'),
        ('complete_set', 'Complete Set with Analytics')
    ], string='Statement Type', required=True, default='complete_set', tracking=True)
    
    reporting_period = fields.Selection([
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('half_yearly', 'Half-Yearly'),
        ('annual', 'Annual')
    ], string='Reporting Period', required=True, default='annual')
    
    date_from = fields.Date(
        string='Period From',
        required=True,
        default=lambda self: fields.Date.today().replace(month=1, day=1)
    )
    
    date_to = fields.Date(
        string='Period To',
        required=True,
        default=lambda self: fields.Date.today().replace(month=12, day=31)
    )
    
    comparative_period = fields.Boolean(
        string='Include Comparative Period',
        default=True,
        help='Include previous period for comparison'
    )
    
    comparative_date_from = fields.Date(string='Comparative From')
    comparative_date_to = fields.Date(string='Comparative To')
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        required=True,
        default=lambda self: self.env.company.currency_id
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('generated', 'Generated'),
        ('reviewed', 'Reviewed'),
        ('published', 'Published')
    ], string='Status', default='draft', tracking=True)
    
    # Analytics Configuration
    enable_analytics = fields.Boolean(
        string='Enable Analytics',
        default=True,
        help='Enable advanced analytics and ratio calculations'
    )
    
    enable_charts = fields.Boolean(
        string='Enable Charts',
        default=True,
        help='Enable interactive charts and graphs'
    )
    
    enable_drill_down = fields.Boolean(
        string='Enable Drill Down',
        default=True,
        help='Enable drill-down from summary to detail'
    )
    
    # Statement Lines
    statement_line_ids = fields.One2many(
        'financial.statement.line.analytics',
        'statement_id',
        string='Statement Lines'
    )
    
    # Analytics Data
    ratio_analysis_ids = fields.One2many(
        'financial.ratio.analysis',
        'statement_id',
        string='Ratio Analysis'
    )
    
    # Dashboard Data (stored as JSON)
    dashboard_data = fields.Text(
        string='Dashboard Data',
        help='JSON data for analytics dashboard'
    )
    
    # Computed Analytics Fields
    total_assets = fields.Monetary(
        string='Total Assets',
        compute='_compute_financial_totals',
        store=True,
        currency_field='currency_id'
    )
    
    total_liabilities = fields.Monetary(
        string='Total Liabilities',
        compute='_compute_financial_totals',
        store=True,
        currency_field='currency_id'
    )
    
    total_equity = fields.Monetary(
        string='Total Equity',
        compute='_compute_financial_totals',
        store=True,
        currency_field='currency_id'
    )
    
    total_revenue = fields.Monetary(
        string='Total Revenue',
        compute='_compute_financial_totals',
        store=True,
        currency_field='currency_id'
    )
    
    net_income = fields.Monetary(
        string='Net Income',
        compute='_compute_financial_totals',
        store=True,
        currency_field='currency_id'
    )
    
    # Key Ratios (computed)
    current_ratio = fields.Float(
        string='Current Ratio',
        compute='_compute_key_ratios',
        store=True,
        digits=(12, 2)
    )
    
    quick_ratio = fields.Float(
        string='Quick Ratio',
        compute='_compute_key_ratios',
        store=True,
        digits=(12, 2)
    )
    
    debt_to_equity_ratio = fields.Float(
        string='Debt to Equity Ratio',
        compute='_compute_key_ratios',
        store=True,
        digits=(12, 2)
    )
    
    return_on_equity = fields.Float(
        string='Return on Equity (%)',
        compute='_compute_key_ratios',
        store=True,
        digits=(12, 2)
    )
    
    return_on_assets = fields.Float(
        string='Return on Assets (%)',
        compute='_compute_key_ratios',
        store=True,
        digits=(12, 2)
    )
    
    # Audit Trail
    prepared_by = fields.Many2one('res.users', string='Prepared By', default=lambda self: self.env.user)
    prepared_date = fields.Datetime(string='Prepared Date', default=fields.Datetime.now)
    reviewed_by = fields.Many2one('res.users', string='Reviewed By')
    reviewed_date = fields.Datetime(string='Reviewed Date')
    approved_by = fields.Many2one('res.users', string='Approved By')
    approved_date = fields.Datetime(string='Approved Date')
    
    @api.depends('statement_line_ids.current_amount', 'statement_line_ids.line_section')
    def _compute_financial_totals(self):
        """Compute key financial totals from statement lines"""
        for record in self:
            # Initialize totals
            total_assets = total_liabilities = total_equity = 0.0
            total_revenue = net_income = 0.0
            
            for line in record.statement_line_ids:
                if line.line_section in ['current_assets', 'non_current_assets']:
                    total_assets += line.current_amount
                elif line.line_section in ['current_liabilities', 'non_current_liabilities']:
                    total_liabilities += line.current_amount
                elif line.line_section == 'equity':
                    total_equity += line.current_amount
                elif line.line_section == 'revenue':
                    total_revenue += line.current_amount
                elif line.line_section == 'net_income':
                    net_income += line.current_amount
            
            record.total_assets = total_assets
            record.total_liabilities = total_liabilities
            record.total_equity = total_equity
            record.total_revenue = total_revenue
            record.net_income = net_income
    
    @api.depends('total_assets', 'total_liabilities', 'total_equity', 'net_income')
    def _compute_key_ratios(self):
        """Compute key financial ratios"""
        for record in self:
            # Current Assets and Current Liabilities
            current_assets = sum(record.statement_line_ids.filtered(
                lambda l: l.line_section == 'current_assets'
            ).mapped('current_amount'))
            
            current_liabilities = sum(record.statement_line_ids.filtered(
                lambda l: l.line_section == 'current_liabilities'
            ).mapped('current_amount'))
            
            quick_assets = sum(record.statement_line_ids.filtered(
                lambda l: l.line_section == 'current_assets' and l.name not in ['Inventory', 'Prepaid Expenses']
            ).mapped('current_amount'))
            
            # Calculate ratios
            record.current_ratio = current_assets / current_liabilities if current_liabilities else 0.0
            record.quick_ratio = quick_assets / current_liabilities if current_liabilities else 0.0
            record.debt_to_equity_ratio = record.total_liabilities / record.total_equity if record.total_equity else 0.0
            record.return_on_equity = (record.net_income / record.total_equity * 100) if record.total_equity else 0.0
            record.return_on_assets = (record.net_income / record.total_assets * 100) if record.total_assets else 0.0
    
    @api.onchange('date_from', 'date_to')
    def _onchange_dates(self):
        """Auto-calculate comparative period dates"""
        if self.date_from and self.date_to and self.comparative_period:
            period_days = (self.date_to - self.date_from).days
            self.comparative_date_to = self.date_from - timedelta(days=1)
            self.comparative_date_from = self.comparative_date_to - timedelta(days=period_days)
    
    def action_generate_statement(self):
        """Generate financial statement with analytics"""
        self.ensure_one()
        self.state = 'in_progress'
        
        # Clear existing lines
        self.statement_line_ids.unlink()
        
        # Generate statement lines based on type
        if self.statement_type in ['balance_sheet', 'complete_set']:
            self._generate_balance_sheet_lines()
        
        if self.statement_type in ['income_statement', 'complete_set']:
            self._generate_income_statement_lines()
        
        if self.statement_type in ['cash_flow', 'complete_set']:
            self._generate_cash_flow_lines()
        
        if self.statement_type in ['equity_changes', 'complete_set']:
            self._generate_equity_changes_lines()
        
        # Generate analytics if enabled
        if self.enable_analytics:
            self._generate_ratio_analysis()
            self._generate_dashboard_data()
        
        self.state = 'generated'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Financial statement with analytics generated successfully!'),
                'type': 'success',
            }
        }

    def _generate_balance_sheet_lines(self):
        """Generate balance sheet lines with proper hierarchy"""
        lines_data = [
            # ASSETS
            {'name': 'ASSETS', 'line_type': 'header', 'sequence': 10, 'line_section': 'assets'},

            # Current Assets
            {'name': 'Current Assets', 'line_type': 'header', 'sequence': 20, 'line_section': 'current_assets'},
            {'name': 'Bank and Cash Accounts', 'line_type': 'line', 'sequence': 30, 'line_section': 'current_assets'},
            {'name': 'Receivables', 'line_type': 'header', 'sequence': 40, 'line_section': 'current_assets'},
            {'name': '100400 Debtors', 'line_type': 'line', 'sequence': 50, 'line_section': 'current_assets', 'indent_level': 1},
            {'name': 'Current Assets', 'line_type': 'line', 'sequence': 60, 'line_section': 'current_assets'},
            {'name': 'Prepayments', 'line_type': 'line', 'sequence': 70, 'line_section': 'current_assets'},
            {'name': 'Plus Fixed Assets', 'line_type': 'subtotal', 'sequence': 80, 'line_section': 'non_current_assets'},
            {'name': 'Plus Non-current Assets', 'line_type': 'subtotal', 'sequence': 90, 'line_section': 'non_current_assets'},

            # LIABILITIES
            {'name': 'LIABILITIES', 'line_type': 'header', 'sequence': 100, 'line_section': 'liabilities'},

            # Current Liabilities
            {'name': 'Current Liabilities', 'line_type': 'header', 'sequence': 110, 'line_section': 'current_liabilities'},
            {'name': 'Current Liabilities', 'line_type': 'line', 'sequence': 120, 'line_section': 'current_liabilities'},
            {'name': 'Payables', 'line_type': 'header', 'sequence': 130, 'line_section': 'current_liabilities'},
            {'name': 'Plus Non-current Liabilities', 'line_type': 'subtotal', 'sequence': 140, 'line_section': 'non_current_liabilities'},

            # EQUITY
            {'name': 'EQUITY', 'line_type': 'header', 'sequence': 150, 'line_section': 'equity'},
            {'name': 'Unallocated Earnings', 'line_type': 'line', 'sequence': 160, 'line_section': 'equity'},
            {'name': 'Current Year Unallocated Earnings', 'line_type': 'line', 'sequence': 170, 'line_section': 'equity'},
        ]

        for line_data in lines_data:
            self._create_statement_line(line_data)

    def _generate_income_statement_lines(self):
        """Generate income statement lines"""
        lines_data = [
            {'name': 'Revenue', 'line_type': 'header', 'sequence': 10, 'line_section': 'revenue'},
            {'name': 'Cost of Sales', 'line_type': 'line', 'sequence': 20, 'line_section': 'cost_of_sales'},
            {'name': 'Gross Profit', 'line_type': 'subtotal', 'sequence': 30, 'line_section': 'gross_profit'},
            {'name': 'Operating Expenses', 'line_type': 'header', 'sequence': 40, 'line_section': 'operating_expenses'},
            {'name': 'Operating Income', 'line_type': 'subtotal', 'sequence': 50, 'line_section': 'operating_income'},
            {'name': 'Finance Costs', 'line_type': 'line', 'sequence': 60, 'line_section': 'finance_costs'},
            {'name': 'Net Income', 'line_type': 'total', 'sequence': 70, 'line_section': 'net_income'},
        ]

        for line_data in lines_data:
            self._create_statement_line(line_data)

    def _generate_cash_flow_lines(self):
        """Generate cash flow statement lines"""
        lines_data = [
            {'name': 'Cash Flows from Operating Activities', 'line_type': 'header', 'sequence': 10, 'line_section': 'operating_activities'},
            {'name': 'Net Income', 'line_type': 'line', 'sequence': 20, 'line_section': 'operating_activities'},
            {'name': 'Depreciation and Amortization', 'line_type': 'line', 'sequence': 30, 'line_section': 'operating_activities'},
            {'name': 'Changes in Working Capital', 'line_type': 'line', 'sequence': 40, 'line_section': 'operating_activities'},
            {'name': 'Net Cash from Operating Activities', 'line_type': 'subtotal', 'sequence': 50, 'line_section': 'operating_activities'},

            {'name': 'Cash Flows from Investing Activities', 'line_type': 'header', 'sequence': 60, 'line_section': 'investing_activities'},
            {'name': 'Net Cash from Investing Activities', 'line_type': 'subtotal', 'sequence': 70, 'line_section': 'investing_activities'},

            {'name': 'Cash Flows from Financing Activities', 'line_type': 'header', 'sequence': 80, 'line_section': 'financing_activities'},
            {'name': 'Net Cash from Financing Activities', 'line_type': 'subtotal', 'sequence': 90, 'line_section': 'financing_activities'},

            {'name': 'Net Change in Cash', 'line_type': 'total', 'sequence': 100, 'line_section': 'net_cash_change'},
        ]

        for line_data in lines_data:
            self._create_statement_line(line_data)

    def _generate_equity_changes_lines(self):
        """Generate statement of changes in equity lines"""
        lines_data = [
            {'name': 'Share Capital', 'line_type': 'header', 'sequence': 10, 'line_section': 'share_capital'},
            {'name': 'Retained Earnings', 'line_type': 'header', 'sequence': 20, 'line_section': 'retained_earnings'},
            {'name': 'Other Comprehensive Income', 'line_type': 'header', 'sequence': 30, 'line_section': 'other_comprehensive_income'},
            {'name': 'Total Equity', 'line_type': 'total', 'sequence': 40, 'line_section': 'total_equity'},
        ]

        for line_data in lines_data:
            self._create_statement_line(line_data)

    def _create_statement_line(self, line_data):
        """Create a statement line with calculated amounts"""
        # Get account data based on line section and name
        current_amount = self._calculate_line_amount(line_data)
        comparative_amount = self._calculate_comparative_amount(line_data) if self.comparative_period else 0.0

        line_data.update({
            'statement_id': self.id,
            'current_amount': current_amount,
            'comparative_amount': comparative_amount,
            'currency_id': self.currency_id.id,
        })

        self.env['financial.statement.line.analytics'].create(line_data)

    def _calculate_line_amount(self, line_data):
        """Calculate amount for a statement line based on account data"""
        # This is a simplified calculation - in real implementation,
        # you would map line_data to specific account codes and calculate actual amounts

        # Sample calculation logic
        if line_data.get('line_section') == 'current_assets':
            if 'Debtors' in line_data.get('name', ''):
                return 1670385.83
            elif 'Bank' in line_data.get('name', ''):
                return 0.0
            elif 'Current Assets' in line_data.get('name', ''):
                return 96130.37
        elif line_data.get('line_section') == 'current_liabilities':
            if 'Current Liabilities' in line_data.get('name', ''):
                return 735685.76
        elif line_data.get('line_section') == 'equity':
            if 'Unallocated Earnings' in line_data.get('name', ''):
                return 949542.00

        return 0.0

    def _calculate_comparative_amount(self, line_data):
        """Calculate comparative period amount"""
        # Similar logic as _calculate_line_amount but for comparative period
        return self._calculate_line_amount(line_data) * 0.85  # Sample: 85% of current period

    def _generate_ratio_analysis(self):
        """Generate comprehensive ratio analysis"""
        ratios_data = [
            {
                'ratio_category': 'liquidity',
                'ratio_name': 'Current Ratio',
                'ratio_value': self.current_ratio,
                'benchmark_value': 2.0,
                'interpretation': 'Good' if self.current_ratio >= 1.5 else 'Needs Attention'
            },
            {
                'ratio_category': 'liquidity',
                'ratio_name': 'Quick Ratio',
                'ratio_value': self.quick_ratio,
                'benchmark_value': 1.0,
                'interpretation': 'Good' if self.quick_ratio >= 1.0 else 'Needs Attention'
            },
            {
                'ratio_category': 'leverage',
                'ratio_name': 'Debt to Equity',
                'ratio_value': self.debt_to_equity_ratio,
                'benchmark_value': 0.5,
                'interpretation': 'Good' if self.debt_to_equity_ratio <= 0.5 else 'High Leverage'
            },
            {
                'ratio_category': 'profitability',
                'ratio_name': 'Return on Equity',
                'ratio_value': self.return_on_equity,
                'benchmark_value': 15.0,
                'interpretation': 'Excellent' if self.return_on_equity >= 15.0 else 'Good' if self.return_on_equity >= 10.0 else 'Needs Improvement'
            },
            {
                'ratio_category': 'profitability',
                'ratio_name': 'Return on Assets',
                'ratio_value': self.return_on_assets,
                'benchmark_value': 10.0,
                'interpretation': 'Excellent' if self.return_on_assets >= 10.0 else 'Good' if self.return_on_assets >= 5.0 else 'Needs Improvement'
            },
        ]

        # Clear existing ratio analysis
        self.ratio_analysis_ids.unlink()

        for ratio_data in ratios_data:
            ratio_data['statement_id'] = self.id
            self.env['financial.ratio.analysis'].create(ratio_data)

    def _generate_dashboard_data(self):
        """Generate JSON data for analytics dashboard"""
        dashboard_data = {
            'financial_summary': {
                'total_assets': self.total_assets,
                'total_liabilities': self.total_liabilities,
                'total_equity': self.total_equity,
                'net_income': self.net_income,
            },
            'key_ratios': {
                'current_ratio': self.current_ratio,
                'quick_ratio': self.quick_ratio,
                'debt_to_equity': self.debt_to_equity_ratio,
                'roe': self.return_on_equity,
                'roa': self.return_on_assets,
            },
            'charts_data': {
                'asset_composition': self._get_asset_composition_data(),
                'liability_equity_composition': self._get_liability_equity_data(),
                'trend_analysis': self._get_trend_analysis_data(),
            }
        }

        self.dashboard_data = json.dumps(dashboard_data)

    def _get_asset_composition_data(self):
        """Get data for asset composition chart"""
        current_assets = sum(self.statement_line_ids.filtered(
            lambda l: l.line_section == 'current_assets'
        ).mapped('current_amount'))

        non_current_assets = sum(self.statement_line_ids.filtered(
            lambda l: l.line_section == 'non_current_assets'
        ).mapped('current_amount'))

        return {
            'labels': ['Current Assets', 'Non-Current Assets'],
            'data': [current_assets, non_current_assets]
        }

    def _get_liability_equity_data(self):
        """Get data for liability and equity composition chart"""
        return {
            'labels': ['Total Liabilities', 'Total Equity'],
            'data': [self.total_liabilities, self.total_equity]
        }

    def _get_trend_analysis_data(self):
        """Get trend analysis data for multiple periods"""
        # This would typically fetch data from multiple periods
        # For now, return sample data
        return {
            'periods': ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024'],
            'revenue': [1000000, 1100000, 1200000, 1300000],
            'net_income': [100000, 120000, 140000, 160000],
            'total_assets': [5000000, 5200000, 5400000, 5600000]
        }

    def action_view_analytics_dashboard(self):
        """Open analytics dashboard"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Financial Analytics Dashboard'),
            'res_model': 'financial.analytics.dashboard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_statement_id': self.id}
        }

    def action_export_excel(self):
        """Export statement to Excel with analytics"""
        # For now, export as PDF until Excel export is properly implemented
        return {
            'type': 'ir.actions.report',
            'report_name': 'financial_statements_analytics.report_financial_statement_analytics_template',
            'report_type': 'qweb-pdf',
        }

    def action_submit_for_review(self):
        """Submit statement for review"""
        self.state = 'reviewed'
        self.reviewed_by = self.env.user
        self.reviewed_date = fields.Datetime.now()

    def action_approve(self):
        """Approve statement"""
        self.state = 'published'
        self.approved_by = self.env.user
        self.approved_date = fields.Datetime.now()

    def action_reset_to_draft(self):
        """Reset to draft"""
        self.state = 'draft'
