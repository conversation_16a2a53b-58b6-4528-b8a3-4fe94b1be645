<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Market Sizing Wizard Form View -->
    <record id="view_market_sizing_wizard_form" model="ir.ui.view">
        <field name="name">financial.planning.market.sizing.wizard.form</field>
        <field name="model">financial.planning.market.sizing.wizard</field>
        <field name="arch" type="xml">
            <form string="Market Sizing Analysis Wizard">
                <sheet>
                    <div class="oe_title">
                        <h1>Market Sizing Analysis</h1>
                        <h2>
                            <field name="name" placeholder="Analysis Name"/>
                        </h2>
                    </div>
                    
                    <!-- Step 1: Market Definition -->
                    <div invisible="step != 'market_definition'">
                        <h3>Step 1: Market Definition</h3>
                        <group>
                            <group string="Market Information">
                                <field name="industry_sector"/>
                                <field name="product_category"/>
                                <field name="target_customer_segment"/>
                            </group>
                            <group string="Market Characteristics">
                                <field name="competitive_intensity"/>
                                <field name="market_maturity"/>
                                <field name="market_growth_rate" widget="percentage"/>
                            </group>
                        </group>
                        <group>
                            <field name="description" placeholder="Describe your product/service and target market..."/>
                        </group>
                    </div>

                    <!-- Step 2: Geographic Scope -->
                    <div invisible="step != 'geographic_scope'">
                        <h3>Step 2: Geographic Scope</h3>
                        <group>
                            <group string="Target Markets">
                                <field name="country_ids" widget="many2many_tags"/>
                                <field name="city_ids" widget="many2many_tags"/>
                            </group>
                            <group string="Population Preview">
                                <field name="target_population" widget="float" digits="[16,1]" readonly="1"/>
                                <field name="addressable_population_percentage" widget="percentage"/>
                                <field name="addressable_population" widget="float" digits="[16,1]" readonly="1"/>
                            </group>
                        </group>
                    </div>
                    
                    <!-- Step 3: TAM Calculation -->
                    <div invisible="step != 'tam_calculation'">
                        <h3>Step 3: Total Available Market (TAM)</h3>
                        <group>
                            <group string="TAM Calculation Method">
                                <field name="tam_calculation_method"/>
                                <field name="currency_id"/>
                            </group>
                        </group>

                        <!-- Population-based TAM -->
                        <div invisible="tam_calculation_method != 'population_based'">
                            <group>
                                <group string="Population-Based Calculation">
                                    <field name="addressable_population" widget="float" digits="[16,1]" readonly="1"/>
                                    <field name="average_spending_per_person" widget="monetary"/>
                                </group>
                                <group string="Calculated TAM">
                                    <field name="calculated_tam" widget="monetary" readonly="1"/>
                                </group>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Formula:</strong> TAM = Addressable Population × Average Annual Spending per Person
                            </div>
                        </div>

                        <!-- Manual TAM -->
                        <div invisible="tam_calculation_method != 'manual'">
                            <group>
                                <group string="Manual TAM Entry">
                                    <field name="manual_tam" widget="monetary"/>
                                </group>
                            </group>
                        </div>
                    </div>
                    
                    <!-- Step 4: SAM & SOM Calculation -->
                    <div invisible="step != 'sam_som_calculation'">
                        <h3>Step 4: Serviceable Markets</h3>

                        <group>
                            <group string="Serviceable Available Market (SAM)">
                                <field name="geographic_reach_percentage" widget="percentage"/>
                                <field name="business_model_fit_percentage" widget="percentage"/>
                                <field name="calculated_sam" widget="monetary" readonly="1"/>
                            </group>
                            <group string="Addressable Market (SOM)">
                                <field name="realistic_market_share" widget="percentage"/>
                                <field name="calculated_som" widget="monetary" readonly="1"/>
                            </group>
                        </group>

                        <div class="alert alert-info" role="alert">
                            <strong>SAM Formula:</strong> SAM = TAM × Geographic Reach % × Business Model Fit %<br/>
                            <strong>SOM Formula:</strong> SOM = SAM × Realistic Market Share %
                        </div>
                    </div>

                    <!-- Step 5: Review -->
                    <div invisible="step != 'review'">
                        <h3>Step 5: Review &amp; Create Analysis</h3>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="card-title mb-0">Total Available Market</h6>
                                    </div>
                                    <div class="card-body">
                                        <h4 class="text-primary">
                                            <field name="calculated_tam" widget="monetary" readonly="1"/>
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="card-title mb-0">Serviceable Available Market</h6>
                                    </div>
                                    <div class="card-body">
                                        <h4 class="text-success">
                                            <field name="calculated_sam" widget="monetary" readonly="1"/>
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="card-title mb-0">Addressable Market</h6>
                                    </div>
                                    <div class="card-body">
                                        <h4 class="text-warning">
                                            <field name="calculated_som" widget="monetary" readonly="1"/>
                                        </h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="card-title mb-0">Market Overview</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Industry:</strong> <field name="industry_sector" readonly="1"/></p>
                                        <p><strong>Customer Segment:</strong> <field name="target_customer_segment" readonly="1"/></p>
                                        <p><strong>Target Countries:</strong> <field name="country_ids" widget="many2many_tags" readonly="1"/></p>
                                        <p><strong>Market Growth:</strong> <field name="market_growth_rate" widget="percentage" readonly="1"/></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="card-title mb-0">Population Metrics</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Target Population:</strong> <field name="target_population" widget="float" digits="[16,1]" readonly="1"/>M</p>
                                        <p><strong>Addressable Population:</strong> <field name="addressable_population" widget="float" digits="[16,1]" readonly="1"/>M</p>
                                        <p><strong>Market Share Target:</strong> <field name="realistic_market_share" widget="percentage" readonly="1"/></p>
                                        <p><strong>Competition Level:</strong> <field name="competitive_intensity" readonly="1"/></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hidden step field -->
                    <field name="step" invisible="1"/>
                </sheet>
                
                <footer>
                    <div class="row">
                        <div class="col-md-6">
                            <button name="action_previous_step" string="Previous" type="object" class="btn-secondary" invisible="step == 'market_definition'"/>
                        </div>
                        <div class="col-md-6 text-right">
                            <button name="action_next_step" string="Next" type="object" class="btn-primary" invisible="step == 'review'"/>
                            <button name="action_create_analysis" string="Create Analysis" type="object" class="btn-success" invisible="step != 'review'"/>
                        </div>
                    </div>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Market Sizing Wizard Action -->
    <record id="action_market_sizing_wizard" model="ir.actions.act_window">
        <field name="name">Market Sizing Analysis Wizard</field>
        <field name="res_model">financial.planning.market.sizing.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_market_sizing_wizard_form"/>
    </record>

</odoo>
