@echo off
echo Stopping PostgreSQL service...
net stop postgresql-x64-17

echo Starting PostgreSQL in single-user mode to reset password...
echo Please wait...

echo Creating temporary pg_hba.conf backup...
copy "D:\PostgreSQL\17\data\pg_hba.conf" "D:\PostgreSQL\17\data\pg_hba.conf.backup"

echo Modifying pg_hba.conf for trust authentication...
powershell -Command "(Get-Content 'D:\PostgreSQL\17\data\pg_hba.conf') -replace 'scram-sha-256', 'trust' | Set-Content 'D:\PostgreSQL\17\data\pg_hba.conf'"

echo Starting PostgreSQL service...
net start postgresql-x64-17

echo Waiting for PostgreSQL to start...
timeout /t 5

echo Connecting to PostgreSQL to set password...
"D:\PostgreSQL\17\bin\psql.exe" -U postgres -c "ALTER USER postgres PASSWORD 'postgres';"

echo Restoring original pg_hba.conf...
copy "D:\PostgreSQL\17\data\pg_hba.conf.backup" "D:\PostgreSQL\17\data\pg_hba.conf"

echo Restarting PostgreSQL service...
net stop postgresql-x64-17
net start postgresql-x64-17

echo Password reset complete! The postgres password is now 'postgres'
pause
