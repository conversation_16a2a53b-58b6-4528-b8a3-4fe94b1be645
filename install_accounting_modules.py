#!/usr/bin/env python3
"""
Script to install additional accounting modules in Odoo
"""

import subprocess
import sys
import time

def run_odoo_command(modules, action="init"):
    """Run Odoo command to install/update modules"""
    cmd = [
        "py", "-3.13", "odoo-bin",
        "--config=odoo.conf",
        f"--{action}={modules}",
        "--stop-after-init",
        "--without-demo=all"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ Successfully installed/updated: {modules}")
            return True
        else:
            print(f"❌ Error installing {modules}:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout installing {modules}")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🚀 Installing Odoo Accounting Modules...")
    
    # Core accounting modules (already installed)
    core_modules = [
        "account",
        "account_payment", 
        "account_check_printing",
        "account_debit_note",
        "account_edi",
        "account_payment_term",
        "account_tax_python",
        "analytic"
    ]
    
    # Additional useful accounting modules
    additional_modules = [
        "account_audit_trail",
        "account_lock",
        "account_qr_code_emv",
        "account_qr_code_sepa",
        "base_account_budget"
    ]
    
    print("Core accounting modules should already be installed.")
    print("Installing additional accounting modules...")
    
    for module in additional_modules:
        print(f"\n📦 Installing {module}...")
        success = run_odoo_command(module, "init")
        if not success:
            print(f"⚠️  Failed to install {module}, continuing with others...")
        time.sleep(2)
    
    print("\n✅ Accounting module installation complete!")
    print("\n📋 Next steps:")
    print("1. Start Odoo: py -3.13 odoo-bin --config=odoo.conf")
    print("2. Open browser: http://localhost:8069")
    print("3. Login with admin credentials")
    print("4. Go to Apps menu to see installed accounting modules")
    print("5. Configure your company and chart of accounts")

if __name__ == "__main__":
    main()
