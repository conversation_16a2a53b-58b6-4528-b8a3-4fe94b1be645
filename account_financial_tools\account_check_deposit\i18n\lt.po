# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_check_deposit
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-25 11:46+0000\n"
"PO-Revision-Date: 2018-01-25 11:46+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Lithuanian (https://www.transifex.com/oca/teams/23907/lt/)\n"
"Language: lt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"(n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_check_deposit
#: model:ir.actions.report,print_report_name:account_check_deposit.report_account_check_deposit
msgid ""
"'check_deposit-%s%s' % (object.name, object.state == 'draft' and '-draft' or "
"'')"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "<b>Bank Account Number to Credit:</b>"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "<b>Check Currency:</b>"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "<b>Date:</b>"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "<b>Number of checks:</b>"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "<b>Total:</b>"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.constraint,message:account_check_deposit.constraint_account_check_deposit_name_company_unique
msgid "A check deposit with this reference already exists in this company."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_ids
msgid "Activities"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_state
msgid "Activity State"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Amount"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
msgid "Are you sure you want to go back to draft?"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
msgid "Back to Draft"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__bank_journal_id
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Bank Account"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Bank:"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Beneficiary:"
msgstr ""

#. module: account_check_deposit
#: model:ir.actions.report,name:account_check_deposit.report_account_check_deposit
#: model:ir.model,name:account_check_deposit.model_account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_move_line__check_deposit_id
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
msgid "Check Deposit"
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid "Check Deposit %s"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Check Deposit n°"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__journal_id
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Check Journal"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__check_payment_ids
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
msgid "Check Payments"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Checks Deposit"
msgstr ""

#. module: account_check_deposit
#: model:ir.actions.act_window,name:account_check_deposit.action_check_deposit_tree
#: model:ir.ui.menu,name:account_check_deposit.menu_check_deposit_tree
msgid "Checks Deposits"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__company_id
msgid "Company"
msgstr "Įmonė"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__currency_id
msgid "Currency"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Date"
msgstr "Data"

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Debtor"
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid "Deposit '%s' has already been credited on the bank account."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__deposit_date
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Deposit Date"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__display_name
msgid "Display Name"
msgstr "Vaizduojamas pavadinimas"

#. module: account_check_deposit
#: model:ir.model.fields.selection,name:account_check_deposit.selection__account_check_deposit__state__done
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Done"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields.selection,name:account_check_deposit.selection__account_check_deposit__state__draft
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Draft"
msgstr "Juodraštis"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
#, python-format
msgid "Get All Received Checks"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.view_check_deposit_search
msgid "Group By"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__has_message
msgid "Has Message"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__id
msgid "ID"
msgstr "ID"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__in_hand_check_account_id
msgid "In Hand Check Account"
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid ""
"In the configuration of journal '%s', in the 'Incoming Payments' tab, you "
"must configure an Outstanding Receipts Account for the payment method "
"'Manual (inbound)'."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__move_id
msgid "Journal Entry"
msgstr ""

#. module: account_check_deposit
#: model:ir.model,name:account_check_deposit.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__line_ids
msgid "Lines"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "List of checks:"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_ids
msgid "Messages"
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid "Missing 'Outstanding Receipts Account' on the company '%s'."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__name
msgid "Name"
msgstr "Pavadinimas"

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid "New"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__check_count
msgid "Number of Checks"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
msgid "Print"
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.report_checkdeposit
msgid "Reference"
msgstr "Numeris"

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__state
msgid "Status"
msgstr "Būsena"

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid ""
"The check with amount %(amount)s and reference '%(ref)s' is in currency "
"%(check_currency)s but the deposit is in currency %(deposit_currency)s."
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid ""
"The deposit '%s' is in valid state, so you must cancel it before deleting it."
msgstr ""

#. module: account_check_deposit
#. odoo-python
#: code:addons/account_check_deposit/models/account_check_deposit.py:0
#, python-format
msgid ""
"There are no received checks in account '%(account)s' in currency "
"'%(currency)s' that are not already in this check deposit."
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__total_amount
msgid "Total Amount"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_check_deposit
#: model_terms:ir.ui.view,arch_db:account_check_deposit.account_check_deposit_view_form
msgid "Validate"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,field_description:account_check_deposit.field_account_check_deposit__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_check_deposit
#: model:ir.model.fields,help:account_check_deposit.field_account_check_deposit__website_message_ids
msgid "Website communication history"
msgstr ""

#~ msgid "Journal"
#~ msgstr "Žurnalas"

#~ msgid "Last Modified on"
#~ msgstr "Paskutinį kartą keista"
