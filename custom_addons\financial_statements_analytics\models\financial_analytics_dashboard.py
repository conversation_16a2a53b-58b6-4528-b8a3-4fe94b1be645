# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import json


class FinancialAnalyticsDashboard(models.TransientModel):
    _name = 'financial.analytics.dashboard'
    _description = 'Financial Analytics Dashboard'

    statement_id = fields.Many2one(
        'financial.statement.analytics',
        string='Financial Statement',
        required=True
    )
    
    dashboard_type = fields.Selection([
        ('overview', 'Financial Overview'),
        ('ratios', 'Ratio Analysis'),
        ('trends', 'Trend Analysis'),
        ('comparisons', 'Comparative Analysis'),
        ('drill_down', 'Drill Down Analysis'),
    ], string='Dashboard Type', default='overview')
    
    # Dashboard Data Fields
    financial_summary_data = fields.Text(
        string='Financial Summary Data',
        compute='_compute_dashboard_data'
    )
    
    ratio_analysis_data = fields.Text(
        string='Ratio Analysis Data',
        compute='_compute_dashboard_data'
    )
    
    trend_analysis_data = fields.Text(
        string='Trend Analysis Data',
        compute='_compute_dashboard_data'
    )
    
    chart_data = fields.Text(
        string='Chart Data',
        compute='_compute_dashboard_data'
    )
    
    # Key Performance Indicators
    total_assets = fields.Monetary(
        string='Total Assets',
        related='statement_id.total_assets',
        currency_field='currency_id'
    )
    
    total_liabilities = fields.Monetary(
        string='Total Liabilities',
        related='statement_id.total_liabilities',
        currency_field='currency_id'
    )
    
    total_equity = fields.Monetary(
        string='Total Equity',
        related='statement_id.total_equity',
        currency_field='currency_id'
    )
    
    net_income = fields.Monetary(
        string='Net Income',
        related='statement_id.net_income',
        currency_field='currency_id'
    )
    
    current_ratio = fields.Float(
        string='Current Ratio',
        related='statement_id.current_ratio'
    )
    
    quick_ratio = fields.Float(
        string='Quick Ratio',
        related='statement_id.quick_ratio'
    )
    
    debt_to_equity_ratio = fields.Float(
        string='Debt to Equity',
        related='statement_id.debt_to_equity_ratio'
    )
    
    return_on_equity = fields.Float(
        string='ROE %',
        related='statement_id.return_on_equity'
    )
    
    return_on_assets = fields.Float(
        string='ROA %',
        related='statement_id.return_on_assets'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        related='statement_id.currency_id'
    )
    
    @api.depends('statement_id', 'dashboard_type')
    def _compute_dashboard_data(self):
        """Compute dashboard data based on statement and type"""
        for record in self:
            if not record.statement_id:
                record.financial_summary_data = '{}'
                record.ratio_analysis_data = '{}'
                record.trend_analysis_data = '{}'
                record.chart_data = '{}'
                continue
            
            # Financial Summary Data
            record.financial_summary_data = json.dumps({
                'total_assets': record.total_assets,
                'total_liabilities': record.total_liabilities,
                'total_equity': record.total_equity,
                'net_income': record.net_income,
                'working_capital': record._get_working_capital(),
                'debt_ratio': record.total_liabilities / record.total_assets if record.total_assets else 0,
                'equity_ratio': record.total_equity / record.total_assets if record.total_assets else 0,
            })
            
            # Ratio Analysis Data
            ratios_data = []
            for ratio in record.statement_id.ratio_analysis_ids:
                ratios_data.append({
                    'category': ratio.ratio_category,
                    'name': ratio.ratio_name,
                    'value': ratio.ratio_value,
                    'benchmark': ratio.benchmark_value,
                    'interpretation': ratio.interpretation,
                    'color': ratio.color_code,
                    'trend': ratio.trend_direction,
                    'trend_percentage': ratio.trend_percentage,
                })
            
            record.ratio_analysis_data = json.dumps(ratios_data)
            
            # Trend Analysis Data
            record.trend_analysis_data = json.dumps(record._get_trend_data())
            
            # Chart Data
            record.chart_data = json.dumps({
                'asset_composition': record._get_asset_composition_chart(),
                'liability_equity_pie': record._get_liability_equity_pie(),
                'ratio_radar': record._get_ratio_radar_chart(),
                'trend_line': record._get_trend_line_chart(),
                'comparative_bar': record._get_comparative_bar_chart(),
            })
    
    def _get_working_capital(self):
        """Calculate working capital"""
        current_assets = sum(self.statement_id.statement_line_ids.filtered(
            lambda l: l.line_section == 'current_assets'
        ).mapped('current_amount'))
        
        current_liabilities = sum(self.statement_id.statement_line_ids.filtered(
            lambda l: l.line_section == 'current_liabilities'
        ).mapped('current_amount'))
        
        return current_assets - current_liabilities
    
    def _get_trend_data(self):
        """Get trend analysis data"""
        # This would typically fetch historical data
        # For now, return sample trend data
        return {
            'periods': ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024'],
            'metrics': {
                'total_assets': [5000000, 5200000, 5400000, 5600000, 5800000],
                'total_revenue': [1000000, 1100000, 1200000, 1300000, 1400000],
                'net_income': [100000, 120000, 140000, 160000, 180000],
                'current_ratio': [1.8, 1.9, 2.0, 2.1, 2.27],
                'roe': [14.5, 15.2, 15.8, 16.2, 16.8],
            }
        }
    
    def _get_asset_composition_chart(self):
        """Get asset composition chart data"""
        current_assets = sum(self.statement_id.statement_line_ids.filtered(
            lambda l: l.line_section == 'current_assets'
        ).mapped('current_amount'))
        
        non_current_assets = sum(self.statement_id.statement_line_ids.filtered(
            lambda l: l.line_section == 'non_current_assets'
        ).mapped('current_amount'))
        
        return {
            'type': 'doughnut',
            'data': {
                'labels': ['Current Assets', 'Non-Current Assets'],
                'datasets': [{
                    'data': [current_assets, non_current_assets],
                    'backgroundColor': ['#36A2EB', '#FF6384'],
                    'borderWidth': 2,
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': True,
                        'text': 'Asset Composition'
                    },
                    'legend': {
                        'position': 'bottom'
                    }
                }
            }
        }
    
    def _get_liability_equity_pie(self):
        """Get liability and equity pie chart data"""
        return {
            'type': 'pie',
            'data': {
                'labels': ['Total Liabilities', 'Total Equity'],
                'datasets': [{
                    'data': [self.total_liabilities, self.total_equity],
                    'backgroundColor': ['#FF6384', '#4BC0C0'],
                    'borderWidth': 2,
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': True,
                        'text': 'Capital Structure'
                    },
                    'legend': {
                        'position': 'bottom'
                    }
                }
            }
        }
    
    def _get_ratio_radar_chart(self):
        """Get ratio radar chart data"""
        ratios = self.statement_id.ratio_analysis_ids
        
        # Normalize ratios to 0-100 scale for radar chart
        normalized_data = []
        labels = []
        
        for ratio in ratios[:6]:  # Limit to 6 ratios for readability
            labels.append(ratio.ratio_name)
            # Normalize based on benchmark (benchmark = 50, excellent = 100)
            if ratio.benchmark_value:
                normalized_value = min(100, (ratio.ratio_value / ratio.benchmark_value) * 50)
            else:
                normalized_value = 50
            normalized_data.append(normalized_value)
        
        return {
            'type': 'radar',
            'data': {
                'labels': labels,
                'datasets': [{
                    'label': 'Current Performance',
                    'data': normalized_data,
                    'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'pointBackgroundColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2,
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': True,
                        'text': 'Financial Ratios Performance'
                    }
                },
                'scales': {
                    'r': {
                        'beginAtZero': True,
                        'max': 100
                    }
                }
            }
        }
    
    def _get_trend_line_chart(self):
        """Get trend line chart data"""
        trend_data = self._get_trend_data()
        
        return {
            'type': 'line',
            'data': {
                'labels': trend_data['periods'],
                'datasets': [
                    {
                        'label': 'Total Assets',
                        'data': trend_data['metrics']['total_assets'],
                        'borderColor': '#36A2EB',
                        'backgroundColor': 'rgba(54, 162, 235, 0.1)',
                        'yAxisID': 'y',
                    },
                    {
                        'label': 'Net Income',
                        'data': trend_data['metrics']['net_income'],
                        'borderColor': '#4BC0C0',
                        'backgroundColor': 'rgba(75, 192, 192, 0.1)',
                        'yAxisID': 'y1',
                    }
                ]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': True,
                        'text': 'Financial Trends'
                    }
                },
                'scales': {
                    'y': {
                        'type': 'linear',
                        'display': True,
                        'position': 'left',
                    },
                    'y1': {
                        'type': 'linear',
                        'display': True,
                        'position': 'right',
                        'grid': {
                            'drawOnChartArea': False,
                        },
                    }
                }
            }
        }
    
    def _get_comparative_bar_chart(self):
        """Get comparative bar chart data"""
        current_data = []
        comparative_data = []
        labels = []
        
        # Get key line items for comparison
        key_lines = self.statement_id.statement_line_ids.filtered(
            lambda l: l.line_type in ['subtotal', 'total'] and l.current_amount != 0
        )[:8]  # Limit to 8 items
        
        for line in key_lines:
            labels.append(line.name)
            current_data.append(line.current_amount)
            comparative_data.append(line.comparative_amount)
        
        return {
            'type': 'bar',
            'data': {
                'labels': labels,
                'datasets': [
                    {
                        'label': 'Current Period',
                        'data': current_data,
                        'backgroundColor': '#36A2EB',
                    },
                    {
                        'label': 'Comparative Period',
                        'data': comparative_data,
                        'backgroundColor': '#FF6384',
                    }
                ]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'title': {
                        'display': True,
                        'text': 'Period Comparison'
                    }
                },
                'scales': {
                    'y': {
                        'beginAtZero': True
                    }
                }
            }
        }
    
    def action_refresh_dashboard(self):
        """Refresh dashboard data"""
        self._compute_dashboard_data()
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
    
    def action_export_dashboard(self):
        """Export dashboard to PDF"""
        return self.env.ref('financial_statements_analytics.action_report_dashboard').report_action(self)
    
    def action_drill_down_assets(self):
        """Drill down into assets"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Asset Details'),
            'res_model': 'financial.statement.line.analytics',
            'view_mode': 'tree,form',
            'domain': [
                ('statement_id', '=', self.statement_id.id),
                ('line_section', 'in', ['current_assets', 'non_current_assets'])
            ],
            'context': {'group_by': 'line_section'}
        }
    
    def action_drill_down_liabilities(self):
        """Drill down into liabilities"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Liability Details'),
            'res_model': 'financial.statement.line.analytics',
            'view_mode': 'tree,form',
            'domain': [
                ('statement_id', '=', self.statement_id.id),
                ('line_section', 'in', ['current_liabilities', 'non_current_liabilities'])
            ],
            'context': {'group_by': 'line_section'}
        }
