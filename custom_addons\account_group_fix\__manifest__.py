{
    'name': 'Account Group Fix',
    'version': '********.0',
    'category': 'Accounting',
    'summary': 'Fix Account Group field to be editable',
    'description': """
        This module fixes the Account Group field to be editable in the Chart of Accounts.
        By default, Odoo automatically computes the group based on account code prefixes,
        but this module allows manual selection for analytics purposes.
    """,
    'depends': ['account'],
    'data': [
        'views/account_account_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
