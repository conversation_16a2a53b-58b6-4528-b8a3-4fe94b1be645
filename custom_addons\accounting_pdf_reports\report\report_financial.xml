<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="report_financial">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.internal_layout">
                    <div class="page">
                        <h2 t-esc="data['account_report_id'][1]"/>

                        <div class="row mt32 mb32">
                            <div class="col-4">
                                <strong>Target Moves:</strong>
                                <p>
                                    <span t-if="data['target_move'] == 'all'">All Entries</span>
                                    <span t-if="data['target_move'] == 'posted'">All Posted Entries</span>
                                </p>
                            </div>
                            <div class="col-4">
                                <p>
                                    <t t-if="data['date_from']"><strong>Date from :</strong> <span t-esc="data['date_from']"/><br/></t>
                                    <t t-if="data['date_to']"><strong>Date to :</strong> <span t-esc="data['date_to']"/></t>
                                </p>
                            </div>
                        </div>

                        <table class="table table-sm table-reports" t-if="data['debit_credit'] == 1">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th class="text-end">Debit</th>
                                    <th class="text-end">Credit</th>
                                    <th class="text-end">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="get_account_lines" t-as="a">
                                    <t t-if="a['level'] != 0">
                                        <t t-if="int(a.get('level')) &gt; 3"><t t-set="style" t-value="'font-weight: normal;'"/></t>
                                        <t t-if="not int(a.get('level')) &gt; 3"><t t-set="style" t-value="'font-weight: bold;'"/></t>

                                        <td>
                                            <span style="color: white;" t-esc="'..' * int(a.get('level', 0))"/>
                                            <span t-att-style="style" t-esc="a.get('name')"/>
                                        </td>
                                        <td class="text-end" style="white-space: text-nowrap;">
                                            <span t-att-style="style" t-esc="a.get('debit')" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                        <td class="text-end" style="white-space: text-nowrap;">
                                            <span t-att-style="style" t-esc="a.get('credit')" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                        <td class="text-end" style="white-space: text-nowrap;">
                                            <span t-att-style="style" t-esc="a.get('balance')" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </t>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-sm table-reports" t-if="not data['enable_filter'] and not data['debit_credit']">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th class="text-end">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="get_account_lines" t-as="a">
                                    <t t-if="a['level'] != 0">
                                        <t t-if="int(a.get('level')) &gt; 3"><t t-set="style" t-value="'font-weight: normal;'"/></t>
                                        <t t-if="not int(a.get('level')) &gt; 3"><t t-set="style" t-value="'font-weight: bold;'"/></t>

                                        <td>
                                            <span style="color: white;" t-esc="'..' * int(a.get('level', 0))"/>
                                            <span t-att-style="style" t-esc="a.get('name')"/>
                                        </td>
                                        <td class="text-end"><span t-att-style="style" t-esc="a.get('balance')" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/></td>
                                    </t>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-sm table-reports" t-if="data['enable_filter'] == 1 and not data['debit_credit']">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th class="text-end">Balance</th>
                                    <th class="text-end"><span t-esc="data['label_filter']"/></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="get_account_lines" t-as="a">
                                    <t t-if="a['level'] != 0">
                                        <t t-if="int(a.get('level')) &gt; 3"><t t-set="style" t-value="'font-weight: normal;'"/></t>
                                        <t t-if="not int(a.get('level')) &gt; 3"><t t-set="style" t-value="'font-weight: bold;'"/></t>
                                        <td>
                                            <span style="color: white;" t-esc="'..'"/>
                                            <span t-att-style="style" t-esc="a.get('name')"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-att-style="style" t-esc="a.get('balance')" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-att-style="style" t-esc="a.get('balance_cmp')" t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </t>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </t>
            </t>
        </t>
    </template>

</odoo>
