=================
Assets Management
=================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:87299b1f978048d507cf0f980e0de648ba3e8cf1eb763ca448917b58b01d50a8
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--financial--tools-lightgray.png?logo=github
    :target: https://github.com/OCA/account-financial-tools/tree/17.0/account_asset_management
    :alt: OCA/account-financial-tools
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-financial-tools-17-0/account-financial-tools-17-0-account_asset_management
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-financial-tools&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This Module manages the assets owned by a company. It will keep track of
depreciation's occurred on those assets. And it allows to create
accounting entries from the depreciation lines.

The full asset life-cycle is managed (from asset creation to asset
removal).

Assets can be created manually as well as automatically (via the
creation of an accounting entry on the asset account).

Depreciation Journal Entries can be created manually in the "Deprecation
Board" tab, or automatically by two ways:

- Using the "Invoicing/Assets/Compute Assets" wizard.
- Activating the "Asset Management: Generate assets" cron.

These options are compatibles each other.

The module contains a large number of functional enhancements compared
to the standard account_asset module from Odoo.

**Table of contents**

.. contents::
   :local:

Usage
=====

The module in NOT compatible with the standard account_asset module.

Changelog
=========

14.0.1.0.0 (2021-01-08)
-----------------------

   - [BREAKING] Removed all functionality associated with
     account.fiscal.year

13.0.3.0.0 (2021-07-06)
-----------------------

- Allow to reverse the posting of a depreciation line instead of
  deleting the journal entry.

13.0.2.0.0 (2021-02-19)
-----------------------

- Add support for multi-company

13.0.1.0.0 (2019-10-21)
-----------------------

- Python code and views were adapted to be compatible with v13.
- When assets are created through accounting journal items, they are
  created when the journal items is posted.
- When a Bill Invoice is created or modified, at the time it is saved,
  for each line that has an Asset profile and Quantity 'N' greater than
  1, it will be replaced by 'N' lines identical to it but with quantity
  1. This was done to maintain the same behavior as in the previous
  version, in which for each asset created there is a Journal Item. In
  addition, this solution does not change the data model which does not
  cause migration scripts.
- The configuration option was removed so the only function of that is
  to allow the module to be uninstalled by unchecking that configuration
  option.
- Tests were adapted.

********.0 (2019-10-21)
-----------------------

- [IMP] Add option to calculate depreciation table by days

********.0 (2019-01-13)
-----------------------

- [BREAKING] account.asset: parent_path has replaced parent_left &
  parent_right (TODO: migration script)
- [BREAKING] account.asset.recompute.trigger: depends on date_range.py
  (TODO: re-implement in account_fiscal_year.py)

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-financial-tools/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-financial-tools/issues/new?body=module:%20account_asset_management%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Noviat

Contributors
------------

- OpenERP SA
- Luc De Meyer (Noviat)
- Frédéric Clementi (camptocamp)
- Florian Dacosta (Akretion)
- Stéphane Bidoul (Acsone)
- Adrien Peiffer (Acsone)
- Akim Juillerat <<EMAIL>>
- Henrik Norlin (Apps2GROW)
- Maxence Groine <<EMAIL>>
- Kitti Upariphutthiphong <<EMAIL>>
- Saran Lim. <<EMAIL>>
- `Tecnativa <https://www.tecnativa.com>`__:

  - Ernesto Tejeda
  - Pedro M. Baeza
  - João Marques
  - Víctor Martínez

- `ForgeFlow <https://www.forgeflow.com>`__:

  - Jordi Ballester <<EMAIL>>
  - Miquel Raïch <<EMAIL>>

- `Sygel <https://www.sygel.es>`__:

  - Manuel Regidor <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-financial-tools <https://github.com/OCA/account-financial-tools/tree/17.0/account_asset_management>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
