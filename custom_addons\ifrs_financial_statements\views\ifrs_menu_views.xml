<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Main IFRS Menu -->
        <menuitem id="menu_ifrs_main" 
                  name="IFRS Financial Statements" 
                  sequence="25"
                  web_icon="ifrs_financial_statements,static/description/icon.png"
                  groups="ifrs_financial_statements.group_ifrs_user"/>

        <!-- Financial Statements Submenu -->
        <menuitem id="menu_ifrs_statements" 
                  name="Financial Statements" 
                  parent="menu_ifrs_main" 
                  sequence="10"/>

        <menuitem id="menu_ifrs_financial_statement" 
                  name="IFRS Statements" 
                  parent="menu_ifrs_statements" 
                  action="action_ifrs_financial_statement" 
                  sequence="10"/>

        <!-- Compliance Submenu -->
        <menuitem id="menu_ifrs_compliance" 
                  name="Compliance" 
                  parent="menu_ifrs_main" 
                  sequence="20"/>

        <menuitem id="menu_ifrs_compliance_checks" 
                  name="Compliance Checks" 
                  parent="menu_ifrs_compliance" 
                  action="action_ifrs_compliance_check" 
                  sequence="10"/>

        <!-- Templates Submenu -->
        <menuitem id="menu_ifrs_templates" 
                  name="Templates" 
                  parent="menu_ifrs_main" 
                  sequence="30"
                  groups="ifrs_financial_statements.group_ifrs_manager"/>

        <menuitem id="menu_ifrs_report_templates" 
                  name="Report Templates" 
                  parent="menu_ifrs_templates" 
                  action="action_ifrs_report_template" 
                  sequence="10"/>

        <!-- Reports Submenu -->
        <menuitem id="menu_ifrs_reports" 
                  name="Reports" 
                  parent="menu_ifrs_main" 
                  sequence="40"/>

        <!-- Configuration Submenu -->
        <menuitem id="menu_ifrs_configuration" 
                  name="Configuration" 
                  parent="menu_ifrs_main" 
                  sequence="50"
                  groups="ifrs_financial_statements.group_ifrs_manager"/>

    </data>
</odoo>
