<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="budget_vs_actual_report_document">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.company_id.partner_id.lang)"/>
            <div class="page">
                <div class="oe_structure"/>
                
                <!-- Report Header -->
                <div class="row">
                    <div class="col-12">
                        <h2 class="text-center">
                            <span t-field="doc.name"/>
                        </h2>
                    </div>
                </div>
                
                <!-- Report Information -->
                <div class="row mt-3">
                    <div class="col-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Period:</strong></td>
                                <td><span t-field="doc.date_from"/> to <span t-field="doc.date_to"/></td>
                            </tr>
                            <tr t-if="doc.budget_id">
                                <td><strong>Budget:</strong></td>
                                <td><span t-field="doc.budget_id.name"/></td>
                            </tr>
                            <tr>
                                <td><strong>Company:</strong></td>
                                <td><span t-field="doc.company_id.name"/></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Total Budget:</strong></td>
                                <td class="text-right">
                                    <span t-field="doc.total_budget_amount" 
                                          t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Total Actual:</strong></td>
                                <td class="text-right">
                                    <span t-field="doc.total_actual_amount" 
                                          t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Total Variance:</strong></td>
                                <td class="text-right">
                                    <span t-field="doc.total_variance" 
                                          t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"
                                          t-attf-class="#{doc.total_variance >= 0 and 'text-success' or 'text-danger'}"/>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Variance %:</strong></td>
                                <td class="text-right">
                                    <span t-esc="'{:.2f}%'.format(doc.total_variance_percent)"
                                          t-attf-class="#{doc.total_variance_percent >= 0 and 'text-success' or 'text-danger'}"/>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Report Details -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Budget vs Actual Details</h4>
                        <table class="table table-sm table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Account Code</th>
                                    <th>Account Name</th>
                                    <th class="text-right">Budget Amount</th>
                                    <th class="text-right">Actual Amount</th>
                                    <th class="text-right">Variance</th>
                                    <th class="text-right">Variance %</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="doc.line_ids" t-as="line">
                                    <tr>
                                        <td><span t-field="line.account_code"/></td>
                                        <td><span t-field="line.account_name"/></td>
                                        <td class="text-right">
                                            <span t-field="line.budget_amount" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                        </td>
                                        <td class="text-right">
                                            <span t-field="line.actual_amount" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                        </td>
                                        <td class="text-right">
                                            <span t-field="line.variance" 
                                                  t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"
                                                  t-attf-class="#{line.variance >= 0 and 'text-success' or 'text-danger'}"/>
                                        </td>
                                        <td class="text-right">
                                            <span t-esc="'{:.2f}%'.format(line.variance_percent)"
                                                  t-attf-class="#{line.variance_percent >= 0 and 'text-success' or 'text-danger'}"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Variance Analysis Summary -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Variance Analysis Summary</h4>
                        <div class="row">
                            <div class="col-4">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">Favorable Variance (&gt; 10%)</h6>
                                    </div>
                                    <div class="card-body">
                                        <t t-set="favorable_count" t-value="len([l for l in doc.line_ids if l.variance_percent > 10])"/>
                                        <p class="card-text">
                                            <strong t-esc="favorable_count"/> accounts
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning">
                                        <h6 class="mb-0">Within Range (±10%)</h6>
                                    </div>
                                    <div class="card-body">
                                        <t t-set="within_range_count" t-value="len([l for l in doc.line_ids if -10 &lt;= l.variance_percent &lt;= 10])"/>
                                        <p class="card-text">
                                            <strong t-esc="within_range_count"/> accounts
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0">Unfavorable Variance (&lt; -10%)</h6>
                                    </div>
                                    <div class="card-body">
                                        <t t-set="unfavorable_count" t-value="len([l for l in doc.line_ids if l.variance_percent &lt; -10])"/>
                                        <p class="card-text">
                                            <strong t-esc="unfavorable_count"/> accounts
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="oe_structure"/>
            </div>
        </t>
    </template>

    <template id="budget_vs_actual_report">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="budget_vs_actual_report.budget_vs_actual_report_document" t-lang="doc.company_id.partner_id.lang"/>
            </t>
        </t>
    </template>
</odoo>
