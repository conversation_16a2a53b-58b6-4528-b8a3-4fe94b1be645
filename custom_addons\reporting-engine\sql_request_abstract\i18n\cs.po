# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sql_request_abstract
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON><PERSON> Nemec <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-06-06 02:51+0000\n"
"PO-Revision-Date: 2017-06-06 02:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>em<PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Czech (https://www.transifex.com/oca/teams/23907/cs/)\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_needaction
msgid "Action Needed"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__group_ids
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Allowed Groups"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__user_ids
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Allowed Users"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__bi_sql_view__state__draft
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_export__state__draft
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_request_mixin__state__draft
msgid "Draft"
msgstr "Návrh"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_follower_ids
msgid "Followers"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__has_group_changed
msgid "Has Group Changed"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__has_message
msgid "Has Message"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid "It is not allowed to execute a not checked request."
msgstr ""

#. module: sql_request_abstract
#: model:res.groups,name:sql_request_abstract.group_sql_request_manager
msgid "Manager"
msgstr ""

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid ""
"Materialized View requires PostgreSQL 9.3 or greater but PostgreSQL %s is "
"currently installed."
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_ids
msgid "Messages"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__name
msgid "Name"
msgstr "Název"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__note
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Note"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Preview Results"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__query
msgid "Query"
msgstr ""

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "SQL Query"
msgstr ""

#. module: sql_request_abstract
#: model:ir.module.category,name:sql_request_abstract.category_sql_abstract
msgid "SQL Request"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model,name:sql_request_abstract.model_sql_request_mixin
msgid "SQL Request Mixin"
msgstr ""

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "SQL Settings"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__bi_sql_view__state__sql_valid
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_export__state__sql_valid
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_request_mixin__state__sql_valid
msgid "SQL Valid"
msgstr ""

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Security"
msgstr ""

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Set to Draft"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__state
msgid "State"
msgstr "Stav"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__state
msgid ""
"State of the Request:\n"
" * 'Draft': Not tested\n"
" * 'SQL Valid': SQL Request has been checked and is valid"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__has_group_changed
msgid ""
"Technical fields, used in modules that depends on this one to know if groups "
"has changed, and that according access should be updated."
msgstr ""

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid ""
"The SQL query is not valid:\n"
"\n"
" %s"
msgstr ""

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid "The query is not allowed because it contains unsafe word '%s'"
msgstr ""

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid "Unimplemented mode : '%s'"
msgstr ""

#. module: sql_request_abstract
#: model:res.groups,name:sql_request_abstract.group_sql_request_user
msgid "User"
msgstr "Uživatel"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Validate SQL Expression"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__query
msgid ""
"You can't use the following words: DELETE, DROP, CREATE, INSERT, ALTER, "
"TRUNCATE, EXECUTE, UPDATE."
msgstr ""

#~ msgid "Display Name"
#~ msgstr "Zobrazovaný název"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Naposled upraveno"
