<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="report_ifrs_statement_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="text-center">
                                <h2>
                                    <strong><t t-esc="doc.company_id.name"/></strong>
                                </h2>
                                <h3><t t-esc="doc.name"/></h3>
                                <p>
                                    For the period from <t t-esc="doc.date_from"/> to <t t-esc="doc.date_to"/>
                                </p>
                                <p>
                                    (All amounts in <t t-esc="doc.currency_id.name"/>)
                                </p>
                            </div>

                            <br/>

                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th style="width: 60%;">Description</th>
                                        <th class="text-right" style="width: 15%;">Note</th>
                                        <th class="text-right" style="width: 12.5%;">
                                            <t t-esc="doc.date_to.year"/>
                                        </th>
                                        <th class="text-right" style="width: 12.5%;" t-if="doc.comparative_period">
                                            <t t-esc="doc.comparative_date_to.year"/>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="doc.statement_line_ids" t-as="line">
                                        <tr t-att-class="'ifrs_statement_' + line.line_type">
                                            <td>
                                                <span t-att-style="'padding-left: ' + str(line.indent_level * 20) + 'px;'"
                                                      t-att-class="('font-weight-bold' if line.bold else '') + (' font-italic' if line.italic else '') + (' text-decoration-underline' if line.underline else '')">
                                                    <t t-esc="line.name"/>
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <t t-esc="line.note_number"/>
                                            </td>
                                            <td class="text-right">
                                                <t t-if="line.line_type != 'header'">
                                                    <t t-esc="'{:,.2f}'.format(line.current_amount)"/>
                                                </t>
                                            </td>
                                            <td class="text-right" t-if="doc.comparative_period">
                                                <t t-if="line.line_type != 'header'">
                                                    <t t-esc="'{:,.2f}'.format(line.comparative_amount)"/>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>

                            <br/>

                            <div class="row">
                                <div class="col-6">
                                    <p><strong>Prepared by:</strong> <t t-esc="doc.prepared_by.name"/></p>
                                    <p><strong>Date:</strong> <t t-esc="doc.preparation_date.strftime('%d/%m/%Y') if doc.preparation_date else ''"/></p>
                                </div>
                                <div class="col-6">
                                    <p t-if="doc.reviewed_by"><strong>Reviewed by:</strong> <t t-esc="doc.reviewed_by.name"/></p>
                                    <p t-if="doc.approved_by"><strong>Approved by:</strong> <t t-esc="doc.approved_by.name"/></p>
                                </div>
                            </div>

                            <div t-if="doc.notes" class="mt-4">
                                <h4>Notes to Financial Statements</h4>
                                <div><t t-raw="doc.notes"/></div>
                            </div>

                            <!-- Compliance Information -->
                            <div class="mt-4">
                                <h5>IFRS Compliance Status</h5>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Standard</th>
                                            <th>Status</th>
                                            <th>Check Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-foreach="doc.compliance_check_ids" t-as="check">
                                            <tr>
                                                <td><t t-esc="check.name"/></td>
                                                <td>
                                                    <span t-att-class="'ifrs_compliance_' + check.status">
                                                        <t t-esc="check.status"/>
                                                    </span>
                                                </td>
                                                <td><t t-esc="check.check_date.strftime('%d/%m/%Y') if check.check_date else ''"/></td>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>

    </data>
</odoo>
