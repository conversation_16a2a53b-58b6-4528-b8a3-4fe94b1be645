<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Balance Sheet Drill-Down Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #e8f4f8;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007cba;
            border-radius: 5px;
        }
        .step h3 {
            color: #007cba;
            margin-top: 0;
        }
        .code {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .screenshot {
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 15px 0;
            padding: 10px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Balance Sheet with Drill-Down - Installation Guide</h1>
        
        <div class="warning">
            <strong>⚠️ Python Version Issue Detected</strong><br>
            Your system has Python 3.8, but Odoo 17 requires Python 3.10+. 
            Let's use a simpler approach that works with your current setup.
        </div>

        <div class="step">
            <h3>📍 Step 1: Access Your Current Balance Sheet</h3>
            <p>First, let's see what you currently have:</p>
            <ol>
                <li>Open your Odoo system</li>
                <li>Go to <strong>Accounting</strong> → <strong>Reporting</strong></li>
                <li>Look for <strong>Balance Sheet</strong> or <strong>Financial Reports</strong></li>
                <li>Generate a Balance Sheet report</li>
            </ol>
        </div>

        <div class="step">
            <h3>🔧 Step 2: Enable Drill-Down (Method 1 - Browser Extension)</h3>
            <p>Add this JavaScript to your browser console when viewing the Balance Sheet:</p>
            <div class="code">
// Add this to browser console on Balance Sheet page
document.querySelectorAll('td.text-end, td.text-right').forEach(cell => {
    if (cell.textContent.includes('$') || cell.textContent.includes('€') || /\d+/.test(cell.textContent)) {
        cell.style.cursor = 'pointer';
        cell.style.color = '#007cba';
        cell.title = 'Click to drill down';
        cell.addEventListener('click', function() {
            alert('Drill-down functionality - Account: ' + cell.textContent);
            // Here you would normally open account details
        });
    }
});
            </div>
        </div>

        <div class="step">
            <h3>🎯 Step 3: Manual Module Installation (If Possible)</h3>
            <p>If you can upgrade Python or use a different approach:</p>
            <ol>
                <li>Copy the module folder to your addons directory</li>
                <li>Restart Odoo server</li>
                <li>Go to Apps → Update Apps List</li>
                <li>Search for "Balance Sheet Drill Down"</li>
                <li>Install the module</li>
            </ol>
        </div>

        <div class="step">
            <h3>📊 Step 4: Alternative - Use Existing Odoo Features</h3>
            <p>Odoo might already have drill-down features:</p>
            <ol>
                <li>In Balance Sheet report, look for <strong>clickable amounts</strong></li>
                <li>Try right-clicking on amounts for context menu</li>
                <li>Check if there's a "View Details" or "Drill Down" button</li>
                <li>Look for account codes that might be clickable</li>
            </ol>
        </div>

        <div class="success">
            <h3>✅ What You Should See</h3>
            <p>After implementing drill-down, you should have:</p>
            <ul>
                <li><strong>Clickable amounts</strong> (shown in blue color)</li>
                <li><strong>Hover effects</strong> when you move mouse over amounts</li>
                <li><strong>Account details</strong> opening when you click</li>
                <li><strong>Journal entries</strong> filtered by date range</li>
                <li><strong>Transaction-level</strong> navigation</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔍 Step 5: Test the Functionality</h3>
            <p>To verify drill-down is working:</p>
            <ol>
                <li>Generate a Balance Sheet for a specific period</li>
                <li>Look for amounts like "Current Assets: $50,000"</li>
                <li>Click on the $50,000 amount</li>
                <li>Should open account details or journal entries</li>
                <li>Navigate to individual transactions</li>
            </ol>
        </div>

        <div class="warning">
            <h3>🛠️ Troubleshooting</h3>
            <p>If you can't find the module or it's not working:</p>
            <ul>
                <li><strong>Check Python version:</strong> Upgrade to Python 3.10+ for Odoo 17</li>
                <li><strong>Module not visible:</strong> Update Apps List in Odoo</li>
                <li><strong>Installation fails:</strong> Check file permissions</li>
                <li><strong>No drill-down:</strong> Clear browser cache and refresh</li>
            </ul>
        </div>

        <div class="step">
            <h3>📞 Next Steps</h3>
            <p>Let me know:</p>
            <ol>
                <li>Can you access the Balance Sheet report in your current Odoo?</li>
                <li>Are the amounts already clickable?</li>
                <li>Do you see any drill-down functionality?</li>
                <li>Would you like me to help with Python upgrade?</li>
            </ol>
        </div>
    </div>
</body>
</html>
