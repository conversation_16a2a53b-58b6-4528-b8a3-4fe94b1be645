=========================
Account Journal Lock Date
=========================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:cd335b11f1bbc561bf62345e5d9d1c702a74199b7dd180df5da89d7be2e42346
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--financial--tools-lightgray.png?logo=github
    :target: https://github.com/OCA/account-financial-tools/tree/17.0/account_journal_lock_date
    :alt: OCA/account-financial-tools
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-financial-tools-17-0/account-financial-tools-17-0-account_journal_lock_date
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-financial-tools&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

Lock each accounting journal independently.

In addition to the lock dates provided by standard Odoo, this module
provides a 'Lock Date' and a 'Lock Date for Non-Advisers' per journal.

This module also adds a wizard that allows you to update the 'Lock Date'
and the 'Lock Date for Non-Advisers' for several Journals at the same
time.

**Table of contents**

.. contents::
   :local:

Configuration
=============

To configure this module, you need to:

1. Go to *Invoicing > Configuration > Journals*
2. Open a Journal and set the 'Lock Date' and the 'Lock Date for
   Non-Advisers' in the' Advanced Settings' tab of the form view or
   select several Journals in the list view and click on the action menu
   'Update journals lock dates' to update those dates for the selected
   journals at the same time.

Usage
=====

If the logged-in user has the access group 'Adviser', he/she will not be
able to create a journal entry if the 'Lock Date' of the journal is
greater than or equal to the journal entry.

If the logged-in user has not the access group 'Adviser', he/she will
not be able to create a journal entry if the 'Lock Date for
Non-Advisers' of the journal is greater than or equal to the journal
entry.

Known issues / Roadmap
======================

-  the module does not check that all moves prior the lock date are
   posted, this could be made as part of the wizard

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-financial-tools/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-financial-tools/issues/new?body=module:%20account_journal_lock_date%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* ACSONE SA/NV
* Tecnativa

Contributors
------------

-  `Akretion <https://www.akretion.com>`__:

   -  Benoît GUILLOT <<EMAIL>>
   -  Chafique DELLI <<EMAIL>>
   -  Alexis de Lattre <<EMAIL>>
   -  Mourad EL HADJ MIMOUNE <<EMAIL>>

-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Pedro M. Baeza
   -  Ernesto Tejeda

-  `Factor Libre <https://www.factorlibre.com>`__:

   -  Rodrigo Bonilla Martinez <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-financial-tools <https://github.com/OCA/account-financial-tools/tree/17.0/account_journal_lock_date>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
