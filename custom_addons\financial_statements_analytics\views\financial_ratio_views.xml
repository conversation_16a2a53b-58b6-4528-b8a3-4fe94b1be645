<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Financial Ratio Analysis Form View -->
        <record id="view_financial_ratio_analysis_form" model="ir.ui.view">
            <field name="name">financial.ratio.analysis.form</field>
            <field name="model">financial.ratio.analysis</field>
            <field name="arch" type="xml">
                <form string="Financial Ratio Analysis">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="ratio_name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="statement_id" readonly="1"/>
                                <field name="ratio_category"/>
                                <field name="sequence"/>
                            </group>
                            <group>
                                <field name="formula"/>
                                <field name="description"/>
                            </group>
                        </group>
                        
                        <group string="Ratio Values">
                            <group>
                                <field name="ratio_value"/>
                                <field name="comparative_ratio_value"/>
                                <field name="benchmark_value"/>
                            </group>
                            <group>
                                <field name="variance_from_benchmark"/>
                                <field name="industry_average"/>
                                <field name="interpretation" widget="badge" 
                                       decoration-success="interpretation == 'excellent'"
                                       decoration-info="interpretation == 'good'"
                                       decoration-warning="interpretation == 'needs_attention'"
                                       decoration-danger="interpretation == 'poor'"/>
                            </group>
                        </group>
                        
                        <group string="Trend Analysis">
                            <group>
                                <field name="trend_direction" widget="badge"
                                       decoration-success="trend_direction == 'improving'"
                                       decoration-warning="trend_direction == 'stable'"
                                       decoration-danger="trend_direction == 'declining'"/>
                                <field name="trend_percentage"/>
                            </group>
                            <group>
                                <field name="color_code" widget="color"/>
                            </group>
                        </group>
                        
                        <group string="Analysis">
                            <field name="interpretation_text" colspan="2"/>
                        </group>
                        
                        <group string="Recommendations">
                            <field name="recommendations" colspan="2"/>
                        </group>
                        
                        <group string="Peer Comparison">
                            <field name="peer_comparison" colspan="2"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Financial Ratio Analysis Tree View -->
        <record id="view_financial_ratio_analysis_tree" model="ir.ui.view">
            <field name="name">financial.ratio.analysis.tree</field>
            <field name="model">financial.ratio.analysis</field>
            <field name="arch" type="xml">
                <tree string="Financial Ratio Analysis" 
                      decoration-success="interpretation == 'excellent'" 
                      decoration-info="interpretation == 'good'"
                      decoration-warning="interpretation == 'needs_attention'"
                      decoration-danger="interpretation == 'poor'">
                    <field name="sequence" widget="handle"/>
                    <field name="ratio_category"/>
                    <field name="ratio_name"/>
                    <field name="ratio_value"/>
                    <field name="benchmark_value"/>
                    <field name="variance_from_benchmark"/>
                    <field name="interpretation" widget="badge"/>
                    <field name="trend_direction" widget="badge" 
                           decoration-success="trend_direction == 'improving'"
                           decoration-warning="trend_direction == 'stable'"
                           decoration-danger="trend_direction == 'declining'"/>
                    <field name="trend_percentage"/>
                </tree>
            </field>
        </record>

        <!-- Financial Ratio Analysis Kanban View -->
        <record id="view_financial_ratio_analysis_kanban" model="ir.ui.view">
            <field name="name">financial.ratio.analysis.kanban</field>
            <field name="model">financial.ratio.analysis</field>
            <field name="arch" type="xml">
                <kanban default_group_by="ratio_category" class="o_kanban_small_column">
                    <field name="ratio_name"/>
                    <field name="ratio_value"/>
                    <field name="benchmark_value"/>
                    <field name="interpretation"/>
                    <field name="trend_direction"/>
                    <field name="color_code"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="ratio_name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Value:</strong>
                                            </div>
                                            <div class="col-6">
                                                <field name="ratio_value"/>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Benchmark:</strong>
                                            </div>
                                            <div class="col-6">
                                                <field name="benchmark_value"/>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <span class="badge" t-attf-style="background-color: #{record.color_code.raw_value}">
                                                    <field name="interpretation"/>
                                                </span>
                                                <span class="badge badge-secondary ml-1">
                                                    <field name="trend_direction"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Financial Ratio Analysis Search View -->
        <record id="view_financial_ratio_analysis_search" model="ir.ui.view">
            <field name="name">financial.ratio.analysis.search</field>
            <field name="model">financial.ratio.analysis</field>
            <field name="arch" type="xml">
                <search string="Financial Ratio Analysis">
                    <field name="ratio_name"/>
                    <field name="ratio_category"/>
                    <field name="statement_id"/>
                    <filter string="Excellent" name="excellent" domain="[('interpretation', '=', 'excellent')]"/>
                    <filter string="Good" name="good" domain="[('interpretation', '=', 'good')]"/>
                    <filter string="Needs Attention" name="needs_attention" domain="[('interpretation', '=', 'needs_attention')]"/>
                    <filter string="Poor" name="poor" domain="[('interpretation', '=', 'poor')]"/>
                    <separator/>
                    <filter string="Improving" name="improving" domain="[('trend_direction', '=', 'improving')]"/>
                    <filter string="Declining" name="declining" domain="[('trend_direction', '=', 'declining')]"/>
                    <separator/>
                    <filter string="Liquidity" name="liquidity" domain="[('ratio_category', '=', 'liquidity')]"/>
                    <filter string="Profitability" name="profitability" domain="[('ratio_category', '=', 'profitability')]"/>
                    <filter string="Leverage" name="leverage" domain="[('ratio_category', '=', 'leverage')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Category" name="group_category" context="{'group_by': 'ratio_category'}"/>
                        <filter string="Interpretation" name="group_interpretation" context="{'group_by': 'interpretation'}"/>
                        <filter string="Trend" name="group_trend" context="{'group_by': 'trend_direction'}"/>
                        <filter string="Statement" name="group_statement" context="{'group_by': 'statement_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Financial Ratio Analysis Action -->
        <record id="action_financial_ratio_analysis" model="ir.actions.act_window">
            <field name="name">Financial Ratio Analysis</field>
            <field name="res_model">financial.ratio.analysis</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="view_financial_ratio_analysis_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No financial ratios found!
                </p>
                <p>
                    Financial ratios are automatically generated when you create
                    a financial statement with analytics enabled.
                </p>
            </field>
        </record>

    </data>
</odoo>
