from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
from core.models import BaseModel, Company, Partner, Currency
from accounting.models import AccountTax
from sales.models import ProductUom, StockWarehouse, ProcurementGroup

# Supporting Models for Purchase Module (defined first to avoid forward references)

class PurchaseApprovalSettings(BaseModel):
    """Purchase Approval Settings model - equivalent to res.company purchase settings in Odoo"""

    DOUBLE_VALIDATION_CHOICES = [
        ('one_step', 'Confirm purchase orders in one step'),
        ('two_step', 'Get 2 levels of approvals to confirm a purchase order'),
    ]

    PO_LOCK_CHOICES = [
        ('edit', 'Allow to edit purchase orders'),
        ('lock', 'Confirmed purchase orders are not editable'),
    ]

    company_id = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='purchase_settings')
    po_double_validation = models.CharField(max_length=20, choices=DOUBLE_VALIDATION_CHOICES,
                                          default='one_step', help_text="Purchase Order Approval")
    po_double_validation_amount = models.DecimalField(max_digits=20, decimal_places=2, default=5000.0,
                                                    help_text="Minimum amount for requiring two approvals")
    po_lock = models.CharField(max_length=10, choices=PO_LOCK_CHOICES, default='edit',
                              help_text="Purchase Order Modification")
    po_lead = models.FloatField(default=0.0, help_text="Purchase Lead Time (days)")

    def __str__(self):
        return f"Purchase Settings - {self.company_id.name}"

# Main Purchase Models

class PurchaseOrder(BaseModel):
    """Purchase Order model - equivalent to purchase.order in Odoo"""

    STATE_CHOICES = [
        ('draft', 'RFQ'),
        ('sent', 'RFQ Sent'),
        ('to approve', 'To Approve'),
        ('purchase', 'Purchase Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('0', 'Normal'),
        ('1', 'Urgent'),
    ]

    INVOICE_STATUS_CHOICES = [
        ('no', 'Nothing to Bill'),
        ('to invoice', 'Waiting Bills'),
        ('invoiced', 'Fully Billed'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, default='New', help_text="Order Reference")
    origin = models.CharField(max_length=255, blank=True, help_text="Source Document")
    partner_ref = models.CharField(max_length=255, blank=True, help_text="Vendor Reference")
    priority = models.CharField(max_length=1, choices=PRIORITY_CHOICES, default='0', db_index=True)

    # Partner Information (Vendor)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, help_text="Vendor")
    dest_address_id = models.ForeignKey(
        Partner,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='purchase_dest_orders',
        help_text="Dropship Address"
    )

    # Dates
    date_order = models.DateTimeField(default=timezone.now, help_text="Order Deadline")
    date_approve = models.DateTimeField(blank=True, null=True, help_text="Confirmation Date")
    date_planned = models.DateTimeField(blank=True, null=True, help_text="Expected Arrival")

    # State and Control
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft', db_index=True)

    # Company and Currency
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)
    currency_rate = models.FloatField(default=1.0, help_text="Currency Rate")

    # Tax and Fiscal
    tax_country_id = models.ForeignKey(
        'core.Country',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        help_text="Tax Country"
    )

    # Amounts (computed from order lines)
    amount_untaxed = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Invoicing
    invoice_status = models.CharField(max_length=20, choices=INVOICE_STATUS_CHOICES, default='no')

    # Purchase Information
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Purchase Representative")

    # Terms and Conditions
    notes = models.TextField(blank=True, help_text="Terms and conditions")

    # Fiscal Position
    fiscal_position_id = models.ForeignKey('accounting.AccountFiscalPosition', on_delete=models.SET_NULL,
                                         null=True, blank=True, help_text="Fiscal Position")

    # Payment Terms
    payment_term_id = models.ForeignKey('accounting.AccountPaymentTerm', on_delete=models.SET_NULL,
                                       null=True, blank=True, help_text="Payment Terms")

    # Incoterms
    incoterm_id = models.ForeignKey(
        'accounting.AccountIncoterms',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        help_text="International Commercial Terms"
    )

    # Procurement and Warehouse
    picking_type_id = models.ForeignKey('StockPickingType', on_delete=models.SET_NULL, null=True, blank=True,
                                       help_text="Deliver To")
    group_id = models.ForeignKey(ProcurementGroup, on_delete=models.SET_NULL, null=True, blank=True,
                                help_text="Procurement Group")

    # Receipt Status (for stock integration)
    receipt_status = models.CharField(max_length=20, choices=[
        ('pending', 'Not Received'),
        ('partial', 'Partially Received'),
        ('full', 'Fully Received'),
    ], default='pending')

    # Mail and Communication
    mail_reminder_confirmed = models.BooleanField(default=False, help_text="Reminder Confirmed")
    mail_reception_confirmed = models.BooleanField(default=False, help_text="Reception Confirmed")
    receipt_reminder_email = models.BooleanField(default=False, help_text="Receipt Reminder Email")
    reminder_date_before_receipt = models.IntegerField(default=1, help_text="Days Before Receipt")
    mail_reception_confirmed = models.BooleanField(default=False)

    class Meta:
        indexes = [
            models.Index(fields=['date_order']),
            models.Index(fields=['state']),
            models.Index(fields=['partner_id']),
            models.Index(fields=['user_id']),
            models.Index(fields=['priority']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()

        # Validate vendor is a supplier
        if self.partner_id and not self.partner_id.is_supplier:
            raise ValidationError(f"Partner {self.partner_id.name} is not configured as a vendor.")

    def save(self, *args, **kwargs):
        # Compute amounts before saving
        self._compute_amounts()
        self._compute_invoice_status()
        super().save(*args, **kwargs)

    def button_confirm(self):
        """Confirm the purchase order (RFQ to PO)"""
        if self.state not in ['draft', 'sent']:
            raise ValidationError("Only draft or sent RFQs can be confirmed.")

        # Check if approval is required
        if self._approval_allowed():
            self.button_approve()
        else:
            self.state = 'to approve'

        self.save()
        return True

    def button_approve(self, force=False):
        """Approve the purchase order"""
        if not force and not self._approval_allowed():
            raise ValidationError("You don't have the rights to approve this purchase order.")

        self.state = 'purchase'
        self.date_approve = timezone.now()

        # Generate sequence number if needed
        if self.name == 'New':
            self.name = self._get_sequence_number()

        # Check if should be locked immediately
        try:
            settings = self.company_id.purchase_settings
            if settings.po_lock == 'lock':
                self.state = 'done'
        except PurchaseApprovalSettings.DoesNotExist:
            pass

        self.save()

        # Update invoice status
        self._compute_invoice_status()

        return {}

    def button_cancel(self):
        """Cancel the purchase order"""
        # Check for related invoices
        # In real implementation, would check for vendor bills

        self.state = 'cancel'
        self.mail_reminder_confirmed = False
        self.save()

    def button_draft(self):
        """Reset to draft state"""
        self.state = 'draft'
        self.save()
        return {}

    def button_unlock(self):
        """Unlock a locked purchase order"""
        if self.state == 'done':
            self.state = 'purchase'
            self.save()

    def button_done(self):
        """Lock the purchase order"""
        self.state = 'done'
        self.priority = '0'  # Reset priority when done
        self.save()

    def _approval_allowed(self):
        """Check if the current user can approve this purchase order"""
        try:
            settings = self.company_id.purchase_settings
        except PurchaseApprovalSettings.DoesNotExist:
            # Default to one-step approval if no settings
            return True

        # One-step approval
        if settings.po_double_validation == 'one_step':
            return True

        # Two-step approval - check amount threshold
        if settings.po_double_validation == 'two_step':
            if self.amount_total < settings.po_double_validation_amount:
                return True
            else:
                # Amount exceeds threshold, requires approval
                return False

        # In real implementation, would check user groups for purchase manager
        # For now, assume approval is allowed for other cases
        return True

    def _get_sequence_number(self):
        """Generate sequence number for the purchase order"""
        # Simple sequence generation - in production, this would be more sophisticated
        last_order = PurchaseOrder.objects.filter(
            company_id=self.company_id,
            date_order__year=self.date_order.year
        ).exclude(name='New').order_by('-name').first()

        if last_order and last_order.name:
            try:
                last_num = int(last_order.name.split('/')[-1])
                return f"PO/{self.date_order.year}/{last_num + 1:04d}"
            except (ValueError, IndexError):
                pass

        return f"PO/{self.date_order.year}/0001"

    def _compute_amounts(self):
        """Compute total amounts from order lines"""
        lines = self.order_line.all()

        self.amount_untaxed = sum(line.price_subtotal for line in lines if not line.display_type)
        self.amount_tax = sum(line.price_tax for line in lines if not line.display_type)
        self.amount_total = self.amount_untaxed + self.amount_tax

    def _compute_invoice_status(self):
        """Compute invoice status based on order lines and state"""
        if self.state not in ('purchase', 'done'):
            self.invoice_status = 'no'
            return

        lines = self.order_line.filter(display_type__isnull=True)

        if not lines:
            self.invoice_status = 'no'
            return

        # Check if any lines need to be invoiced
        if any(line.qty_to_invoice > 0 for line in lines):
            self.invoice_status = 'to invoice'
        elif all(line.qty_to_invoice == 0 for line in lines):
            # Check if there are actual invoices
            # In real implementation, would check invoice_ids
            self.invoice_status = 'invoiced'
        else:
            self.invoice_status = 'no'

    def _compute_date_planned(self):
        """Compute the earliest planned date from order lines"""
        lines = self.order_line.filter(display_type__isnull=True, date_planned__isnull=False)
        if lines:
            self.date_planned = min(line.date_planned for line in lines)
        else:
            self.date_planned = None

    def button_cancel(self):
        """Cancel the purchase order"""
        if self.state == 'done':
            raise ValidationError("Cannot cancel a locked purchase order.")

        # Check for received lines
        if self.order_line.filter(qty_received__gt=0).exists():
            raise ValidationError("Cannot cancel order with received lines.")

        # Check for invoiced lines
        if self.order_line.filter(qty_invoiced__gt=0).exists():
            raise ValidationError("Cannot cancel order with invoiced lines.")

        self.state = 'cancel'
        self.save()

    def button_draft(self):
        """Reset to draft state"""
        if self.state not in ['cancel', 'to approve']:
            raise ValidationError("Only cancelled or pending approval orders can be reset to draft.")

        self.state = 'draft'
        self.save()

    def _create_picking(self):
        """Create stock picking for purchase order"""
        # This would create stock.picking for receipt
        # Simplified implementation
        pass

    def _create_bills(self, grouped=False):
        """Create vendor bills from purchase order"""
        from accounting.models import AccountMove, AccountMoveLine, AccountJournal, AccountAccount

        if self.state not in ['purchase', 'done']:
            raise ValidationError("Only confirmed orders can be billed.")

        # Get lines to bill
        lines_to_bill = self.order_line.filter(qty_to_invoice__gt=0)
        if not lines_to_bill:
            raise ValidationError("No lines to bill.")

        # Get purchase journal
        try:
            purchase_journal = AccountJournal.objects.get(
                type='purchase',
                company_id=self.company_id
            )
        except AccountJournal.DoesNotExist:
            raise ValidationError("No purchase journal found for this company.")

        # Prepare bill values
        bill_vals = self._prepare_bill()
        bill_vals.update({
            'journal_id': purchase_journal,
            'move_type': 'in_invoice',
            'state': 'draft',
        })

        # Create vendor bill
        bill = AccountMove.objects.create(**bill_vals)

        # Create bill lines
        for line in lines_to_bill:
            if line.display_type:
                # Section/note lines
                bill_line_vals = {
                    'move_id': bill,
                    'display_type': line.display_type,
                    'name': line.name,
                    'sequence': line.sequence,
                    'create_uid': line.create_uid,
                    'write_uid': line.write_uid,
                }
            else:
                # Product lines
                # Get expense account (simplified - should be from product/category)
                try:
                    expense_account = AccountAccount.objects.get(
                        code__startswith='5',  # Expense accounts
                        company_id=self.company_id
                    )
                except AccountAccount.DoesNotExist:
                    raise ValidationError("No expense account found.")

                bill_line_vals = {
                    'move_id': bill,
                    'account_id': expense_account,
                    'name': line.name,
                    'quantity': line.qty_to_invoice,
                    'price_unit': line.price_unit,
                    'debit': line.qty_to_invoice * line.price_unit,
                    'credit': 0,
                    'create_uid': line.create_uid,
                    'write_uid': line.write_uid,
                }

                # Update purchase line quantities
                line.qty_invoiced += line.qty_to_invoice
                line.save()

            AccountMoveLine.objects.create(**bill_line_vals)

        # Create payable line (vendor balance)
        try:
            payable_account = AccountAccount.objects.get(
                code='211000',  # Account Payable
                company_id=self.company_id
            )
        except AccountAccount.DoesNotExist:
            raise ValidationError("No payable account found.")

        total_amount = sum(line.qty_to_invoice * line.price_unit
                          for line in lines_to_bill if not line.display_type)

        AccountMoveLine.objects.create({
            'move_id': bill,
            'account_id': payable_account,
            'partner_id': self.partner_id,
            'name': f"Bill {bill.name}",
            'debit': 0,
            'credit': total_amount,
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        })

        # Update order invoice status
        self._compute_invoice_status()
        self.save()

        return [bill]

    def _prepare_bill(self):
        """Prepare vendor bill values from purchase order"""
        return {
            'move_type': 'in_invoice',
            'partner_id': self.partner_id.id,
            'currency_id': self.currency_id.id,
            'invoice_origin': self.name,
            'invoice_payment_term_id': self.payment_term_id.id if self.payment_term_id else None,
            'fiscal_position_id': self.fiscal_position_id.id if self.fiscal_position_id else None,
            'company_id': self.company_id.id,
            'user_id': self.user_id.id if self.user_id else None,
        }

    def _check_three_way_matching(self):
        """Check three-way matching (PO, Receipt, Invoice)"""
        # This would implement three-way matching validation
        # Simplified implementation
        return True

    def unlink(self):
        """Override unlink to add business rules"""
        for order in self:
            if order.state not in ('draft', 'cancel'):
                raise ValidationError("Cannot delete confirmed orders.")
            if order.order_line.filter(qty_received__gt=0).exists():
                raise ValidationError("Cannot delete orders with received lines.")
            if order.order_line.filter(qty_invoiced__gt=0).exists():
                raise ValidationError("Cannot delete orders with invoiced lines.")
        return super().unlink()

class PurchaseOrderLine(BaseModel):
    """Purchase Order Line model - equivalent to purchase.order.line in Odoo"""

    DISPLAY_TYPE_CHOICES = [
        ('line_section', 'Section'),
        ('line_note', 'Note'),
    ]

    # Parent order
    order_id = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='order_line')

    # Product and description
    product_id = models.ForeignKey('inventory.Product', on_delete=models.PROTECT, null=True, blank=True)
    product_template_id = models.ForeignKey(
        'inventory.ProductTemplate',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Product Template"
    )
    name = models.TextField(help_text="Description")

    # Display Type (for sections and notes)
    display_type = models.CharField(
        max_length=20,
        choices=DISPLAY_TYPE_CHOICES,
        null=True, blank=True,
        help_text="Line display type"
    )

    # Quantities
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1.0,
                                    help_text="Quantity")
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1.0,
                                        help_text="Total Quantity")
    qty_received = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Received Quantity")
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Billed Quantity")
    qty_to_invoice = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                       help_text="To Bill Quantity")

    # Unit of Measure
    product_uom = models.ForeignKey(ProductUom, on_delete=models.PROTECT, null=True, blank=True,
                                   help_text="Unit of Measure")

    # Pricing
    price_unit = models.DecimalField(max_digits=20, decimal_places=4, default=0.0, help_text="Unit Price")
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0.0, help_text="Discount (%)")

    # Computed amounts
    price_subtotal = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                       help_text="Subtotal")
    price_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                  help_text="Tax Amount")
    price_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                    help_text="Total")

    # Taxes
    taxes_id = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes")

    # Delivery and Packaging
    date_planned = models.DateTimeField(null=True, blank=True, help_text="Expected Arrival")
    product_packaging_id = models.ForeignKey(
        'inventory.ProductPackaging',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Product Packaging"
    )
    product_packaging_qty = models.FloatField(default=0.0, help_text="Packaging Quantity")

    # Received Quantities
    QTY_RECEIVED_METHOD_CHOICES = [
        ('manual', 'Manual'),
        ('stock_moves', 'Stock Moves'),
    ]
    qty_received_method = models.CharField(
        max_length=20,
        choices=QTY_RECEIVED_METHOD_CHOICES,
        default='stock_moves',
        help_text="Method to update received quantity"
    )
    qty_received_manual = models.FloatField(default=0.0, help_text="Manual Received Quantity")

    # Display and ordering
    sequence = models.IntegerField(default=10, help_text="Sequence")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Partner (inherited from order)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, help_text="Vendor")

    # Invoice lines relationship (will be added when accounting integration is complete)
    # invoice_lines = models.ManyToManyField('account.MoveLine', blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['order_id']),
            models.Index(fields=['sequence']),
            models.Index(fields=['date_planned']),
        ]

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"

    def save(self, *args, **kwargs):
        # Set company, currency, and partner from order
        if self.order_id:
            self.company_id = self.order_id.company_id
            self.currency_id = self.order_id.currency_id
            self.partner_id = self.order_id.partner_id

        # Set default planned date if not provided (but not for display lines)
        if not self.date_planned and self.order_id and not self.display_type:
            # Default to order date + lead time
            lead_days = 1  # Default lead time
            try:
                settings = self.company_id.purchase_settings
                lead_days = int(settings.po_lead) if settings.po_lead else 1
            except PurchaseApprovalSettings.DoesNotExist:
                pass
            self.date_planned = self.order_id.date_order + timedelta(days=lead_days)

        # Compute amounts
        self._compute_amounts()

        super().save(*args, **kwargs)

        # Update order totals and planned date
        if self.order_id:
            self.order_id._compute_amounts()
            self.order_id._compute_date_planned()
            self.order_id.save()

    def clean(self):
        super().clean()

        # Validate required fields for accountable lines
        if not self.display_type:
            if not self.product_uom:
                raise ValidationError("Unit of Measure is required for accountable lines.")

        # Validate forbidden values for non-accountable lines
        if self.display_type:
            if (self.price_unit != 0 or self.product_qty != 0 or
                self.product_uom):
                raise ValidationError("Non-accountable lines cannot have product-related values.")

    def _compute_amounts(self):
        """Compute line amounts including taxes"""
        if self.display_type:
            # Section and note lines have no amounts
            self.price_subtotal = Decimal('0.0')
            self.price_tax = Decimal('0.0')
            self.price_total = Decimal('0.0')
            self.qty_to_invoice = Decimal('0.0')
            return

        # Calculate base amount with discount
        base_amount = Decimal(str(self.price_unit)) * Decimal(str(self.product_qty))
        discount_amount = base_amount * (Decimal(str(self.discount)) / Decimal('100'))
        subtotal = base_amount - discount_amount

        # Calculate taxes (simplified - in real implementation would use tax engine)
        tax_amount = Decimal('0.0')
        if self.taxes_id.exists():
            # Simple tax calculation - sum all tax rates
            total_tax_rate = sum(Decimal(str(tax.amount)) for tax in self.taxes_id.all() if tax.amount_type == 'percent')
            tax_amount = subtotal * (total_tax_rate / Decimal('100'))

        # Update computed fields
        self.price_subtotal = subtotal
        self.price_tax = tax_amount
        self.price_total = subtotal + tax_amount

        # Update quantities to invoice (based on received quantity)
        self.qty_to_invoice = max(Decimal('0'), Decimal(str(self.qty_received)) - Decimal(str(self.qty_invoiced)))

    class Meta:
        constraints = [
            # Equivalent to Odoo's accountable_required_fields constraint
            models.CheckConstraint(
                check=models.Q(display_type__isnull=False) |
                      (models.Q(product_id__isnull=False) &
                       models.Q(product_uom__isnull=False) &
                       models.Q(date_planned__isnull=False)),
                name='purchase_accountable_required_fields'
            ),
            # Equivalent to Odoo's non_accountable_null_fields constraint
            models.CheckConstraint(
                check=models.Q(display_type__isnull=True) |
                      (models.Q(product_id__isnull=True) &
                       models.Q(price_unit=0) &
                       models.Q(product_qty=0) &
                       models.Q(product_uom__isnull=True) &
                       models.Q(date_planned__isnull=True)),
                name='purchase_non_accountable_null_fields'
            ),
        ]
        indexes = [
            models.Index(fields=['order_id']),
            models.Index(fields=['sequence']),
            models.Index(fields=['product_id']),
        ]

# Supporting Models for Purchase Module

class StockPickingType(BaseModel):
    """Picking Type model - equivalent to stock.picking.type in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=20, choices=[
        ('incoming', 'Receipt'),
        ('outgoing', 'Delivery'),
        ('internal', 'Internal Transfer'),
    ])
    warehouse_id = models.ForeignKey(StockWarehouse, on_delete=models.CASCADE)
    sequence = models.IntegerField(default=1)
    active = models.BooleanField(default=True)

    def __str__(self):
        return self.name
