from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta, date
from core.models import BaseModel, Company, Partner, Currency, Country, CountryState
from django.contrib.auth.models import User


class ProjectProject(BaseModel):
    """Project model - equivalent to project.project in Odoo"""
    
    PRIVACY_VISIBILITY_CHOICES = [
        ('followers', 'Invited internal users'),
        ('employees', 'All internal users'),
        ('portal', 'Invited portal users and all internal users'),
    ]
    
    # Basic information
    name = models.CharField(max_length=255, help_text="Project Name")
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    
    # Description and details
    description = models.TextField(blank=True, help_text="Project Description")
    
    # Project management
    user_id = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Project Manager")
    partner_id = models.Foreign<PERSON>ey(Partner, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Customer")
    
    # Company and privacy
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    privacy_visibility = models.CharField(max_length=20, choices=PRIVACY_VISIBILITY_CHOICES,
                                        default='employees', help_text="Privacy")
    
    # Dates
    date_start = models.DateField(null=True, blank=True, help_text="Start Date")
    date = models.DateField(null=True, blank=True, help_text="Expiration Date")
    
    # Configuration
    allow_timesheets = models.BooleanField(default=True, help_text="Timesheets")
    allow_billable_hours = models.BooleanField(default=False, help_text="Bill from Tasks")
    allow_material = models.BooleanField(default=False, help_text="Bill Materials")
    allow_milestones = models.BooleanField(default=False, help_text="Milestones")
    allow_forecast = models.BooleanField(default=False, help_text="Planning")
    
    # Analytics
    analytic_account_id = models.CharField(max_length=255, blank=True, help_text="Analytic Account")
    
    # Favorites and tags
    is_favorite = models.BooleanField(default=False)
    favorite_user_ids = models.ManyToManyField(User, blank=True, related_name='favorite_projects')
    tag_ids = models.ManyToManyField('ProjectTags', blank=True)
    
    # Color for kanban view
    color = models.IntegerField(default=0, help_text="Color Index")
    
    # Computed fields (will be calculated)
    task_count = models.IntegerField(default=0, help_text="Task Count")
    task_ids = models.ManyToManyField('ProjectTask', blank=True, related_name='projects_m2m')
    
    # Rating and collaboration
    rating_request_deadline = models.DateTimeField(null=True, blank=True)
    portal_show_rating = models.BooleanField(default=False)
    
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'company_id'],
                name='project_project_name_company_uniq'
            ),
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['user_id']),
            models.Index(fields=['partner_id']),
            models.Index(fields=['company_id']),
            models.Index(fields=['active']),
            models.Index(fields=['sequence']),
        ]
    
    def __str__(self):
        return self.name
    
    def clean(self):
        super().clean()
        
        # Validate dates
        if self.date_start and self.date and self.date < self.date_start:
            raise ValidationError("End date cannot be before start date.")
    
    def save(self, *args, **kwargs):
        # Update task count
        if self.pk:
            self.task_count = self.task_ids_related.count()
        
        super().save(*args, **kwargs)
    
    @property
    def is_closed(self):
        """Check if project is closed"""
        return not self.active
    
    def action_close(self):
        """Close the project"""
        self.active = False
        self.save()
    
    def action_open(self):
        """Open the project"""
        self.active = True
        self.save()


class ProjectTags(BaseModel):
    """Project Tags model - equivalent to project.tags in Odoo"""
    
    name = models.CharField(max_length=255, unique=True, help_text="Tag Name")
    color = models.IntegerField(default=0, help_text="Color Index")
    
    def __str__(self):
        return self.name


class ProjectTaskType(BaseModel):
    """Task Type/Stage model - equivalent to project.task.type in Odoo"""
    
    name = models.CharField(max_length=255, help_text="Stage Name")
    description = models.TextField(blank=True, help_text="Description")
    sequence = models.IntegerField(default=1, help_text="Sequence")
    
    # Project restriction
    project_ids = models.ManyToManyField(ProjectProject, blank=True,
                                        help_text="Projects")
    
    # Stage configuration
    fold = models.BooleanField(default=False, help_text="Folded in Kanban")
    rating_template_id = models.CharField(max_length=255, blank=True)
    auto_validation_kanban_state = models.BooleanField(default=False)
    
    # Mail template
    mail_template_id = models.CharField(max_length=255, blank=True)
    
    class Meta:
        ordering = ['sequence', 'name']
        indexes = [
            models.Index(fields=['sequence']),
            models.Index(fields=['name']),
        ]
    
    def __str__(self):
        return self.name


class ProjectTask(BaseModel):
    """Task model - equivalent to project.task in Odoo"""
    
    PRIORITY_CHOICES = [
        ('0', 'Normal'),
        ('1', 'High'),
    ]
    
    KANBAN_STATE_CHOICES = [
        ('normal', 'In Progress'),
        ('done', 'Ready'),
        ('blocked', 'Blocked'),
    ]
    
    # Basic information
    name = models.CharField(max_length=255, help_text="Task Title")
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    priority = models.CharField(max_length=1, choices=PRIORITY_CHOICES, default='0')
    
    # Project and stage
    project_id = models.ForeignKey(ProjectProject, on_delete=models.CASCADE,
                                  related_name='task_ids_related', help_text="Project")
    stage_id = models.ForeignKey(ProjectTaskType, on_delete=models.SET_NULL, null=True, blank=True,
                                help_text="Stage")
    
    # Assignment
    user_ids = models.ManyToManyField(User, blank=True, related_name='task_ids',
                                     help_text="Assignees")
    
    # Dates
    date_assign = models.DateTimeField(null=True, blank=True, help_text="Assigning Date")
    date_deadline = models.DateField(null=True, blank=True, help_text="Deadline")
    date_last_stage_update = models.DateTimeField(auto_now=True)
    
    # Description and notes
    description = models.TextField(blank=True, help_text="Description")
    notes = models.TextField(blank=True, help_text="Extra Notes")
    
    # Task hierarchy
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                                 help_text="Parent Task")
    child_ids = models.ManyToManyField('self', blank=True, symmetrical=False,
                                      related_name='parent_tasks')
    
    # Progress tracking
    kanban_state = models.CharField(max_length=20, choices=KANBAN_STATE_CHOICES, default='normal')
    kanban_state_label = models.CharField(max_length=255, blank=True)
    
    # Time tracking
    planned_hours = models.FloatField(default=0.0, help_text="Initially Planned Hours")
    effective_hours = models.FloatField(default=0.0, help_text="Hours Spent")
    remaining_hours = models.FloatField(default=0.0, help_text="Remaining Hours")
    progress = models.FloatField(default=0.0, help_text="Progress (%)")
    overtime = models.FloatField(default=0.0, help_text="Overtime")
    
    # Subtask progress
    subtask_effective_hours = models.FloatField(default=0.0)
    subtask_planned_hours = models.FloatField(default=0.0)
    
    # Company and partner
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    partner_id = models.ForeignKey(Partner, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Customer")
    
    # Commercial fields
    commercial_partner_id = models.ForeignKey(Partner, on_delete=models.SET_NULL, 
                                            null=True, blank=True, related_name='commercial_tasks')
    
    # Email and communication
    email_from = models.EmailField(blank=True, help_text="Email")
    email_cc = models.TextField(blank=True, help_text="Watchers Emails")
    
    # Tags and labels
    tag_ids = models.ManyToManyField(ProjectTags, blank=True)
    
    # Color for kanban
    color = models.IntegerField(default=0, help_text="Color Index")
    
    # Displayed fields
    displayed_image_id = models.CharField(max_length=255, blank=True)
    legend_blocked = models.CharField(max_length=255, default='Blocked')
    legend_done = models.CharField(max_length=255, default='Ready')
    legend_normal = models.CharField(max_length=255, default='In Progress')
    
    # Portal and access
    portal_user_names = models.TextField(blank=True)
    
    # Milestone
    milestone_id = models.ForeignKey('ProjectMilestone', on_delete=models.SET_NULL, 
                                   null=True, blank=True, help_text="Milestone")
    
    # Dependencies
    depend_on_ids = models.ManyToManyField('self', blank=True, symmetrical=False,
                                          related_name='dependent_tasks')
    
    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(planned_hours__gte=0),
                name='project_task_positive_planned_hours'
            ),
            models.CheckConstraint(
                check=models.Q(effective_hours__gte=0),
                name='project_task_positive_effective_hours'
            ),
            models.CheckConstraint(
                check=models.Q(progress__gte=0) & models.Q(progress__lte=100),
                name='project_task_progress_range'
            ),
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['project_id']),
            models.Index(fields=['stage_id']),
            models.Index(fields=['priority']),
            models.Index(fields=['date_deadline']),
            models.Index(fields=['kanban_state']),
            models.Index(fields=['active']),
            models.Index(fields=['sequence']),
            models.Index(fields=['parent_id']),
        ]
    
    def __str__(self):
        return f"[{self.project_id.name}] {self.name}"
    
    def clean(self):
        super().clean()
        
        # Validate that task cannot be its own parent
        if self.parent_id == self:
            raise ValidationError("Task cannot be its own parent.")
        
        # Validate progress range
        if not (0 <= self.progress <= 100):
            raise ValidationError("Progress must be between 0 and 100.")
    
    def save(self, *args, **kwargs):
        # Set company from project if not set
        if not hasattr(self, 'company_id') or not self.company_id_id:
            if self.project_id:
                self.company_id = self.project_id.company_id

        # Set partner from project if not set
        if not hasattr(self, 'partner_id') or not self.partner_id_id:
            if self.project_id and self.project_id.partner_id:
                self.partner_id = self.project_id.partner_id
                self.commercial_partner_id = self.project_id.partner_id

        # Calculate remaining hours
        if self.planned_hours and self.effective_hours:
            self.remaining_hours = max(0, self.planned_hours - self.effective_hours)

        # Calculate progress based on hours
        if self.planned_hours > 0:
            self.progress = min(100, (self.effective_hours / self.planned_hours) * 100)

        super().save(*args, **kwargs)
    
    def action_close(self):
        """Close the task"""
        # Find a 'done' stage or create one
        done_stage = ProjectTaskType.objects.filter(
            name__icontains='done'
        ).first()
        
        if done_stage:
            self.stage_id = done_stage
        
        self.kanban_state = 'done'
        self.progress = 100.0
        self.save()
    
    def action_open(self):
        """Reopen the task"""
        self.kanban_state = 'normal'
        if self.progress == 100.0:
            self.progress = 0.0
        self.save()


class ProjectMilestone(BaseModel):
    """Milestone model - equivalent to project.milestone in Odoo"""

    name = models.CharField(max_length=255, help_text="Milestone Name")
    project_id = models.ForeignKey(ProjectProject, on_delete=models.CASCADE,
                                  related_name='milestone_ids', help_text="Project")

    # Dates
    deadline = models.DateField(help_text="Deadline")

    # Progress
    is_reached = models.BooleanField(default=False, help_text="Reached")

    # Description
    description = models.TextField(blank=True, help_text="Description")

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'project_id'],
                name='project_milestone_name_project_uniq'
            ),
        ]
        indexes = [
            models.Index(fields=['project_id']),
            models.Index(fields=['deadline']),
            models.Index(fields=['is_reached']),
        ]

    def __str__(self):
        return f"[{self.project_id.name}] {self.name}"

    def action_reach(self):
        """Mark milestone as reached"""
        self.is_reached = True
        self.save()

    def action_unreach(self):
        """Mark milestone as not reached"""
        self.is_reached = False
        self.save()


class AccountAnalyticLine(BaseModel):
    """Timesheet model - equivalent to account.analytic.line in Odoo"""

    name = models.CharField(max_length=255, help_text="Description")
    date = models.DateField(default=timezone.now, help_text="Date")

    # Time tracking
    unit_amount = models.FloatField(default=0.0, help_text="Quantity")
    product_uom_id = models.CharField(max_length=255, default='Hours', help_text="Unit of Measure")

    # Project and task
    project_id = models.ForeignKey(ProjectProject, on_delete=models.CASCADE,
                                  related_name='timesheet_ids', help_text="Project")
    task_id = models.ForeignKey(ProjectTask, on_delete=models.CASCADE, null=True, blank=True,
                               related_name='timesheet_ids', help_text="Task")

    # Employee and user
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, help_text="User")
    employee_id = models.CharField(max_length=255, blank=True, help_text="Employee")  # Will link to HR later

    # Company and partner
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    partner_id = models.ForeignKey(Partner, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Customer")

    # Financial
    amount = models.DecimalField(max_digits=20, decimal_places=2, default=0.0, help_text="Amount")
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Analytics
    account_id = models.CharField(max_length=255, blank=True, help_text="Analytic Account")
    general_account_id = models.CharField(max_length=255, blank=True, help_text="General Account")

    # Validation
    validated = models.BooleanField(default=False, help_text="Validated")

    # Tags
    tag_ids = models.ManyToManyField('AccountAnalyticTag', blank=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(unit_amount__gte=0),
                name='account_analytic_line_positive_amount'
            ),
        ]
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['project_id']),
            models.Index(fields=['task_id']),
            models.Index(fields=['user_id']),
            models.Index(fields=['company_id']),
        ]

    def __str__(self):
        return f"{self.name} - {self.unit_amount}h"

    def clean(self):
        super().clean()

        # Validate that unit amount is positive
        if self.unit_amount < 0:
            raise ValidationError("Time spent cannot be negative.")

    def save(self, *args, **kwargs):
        # Set company from project if not set
        if not hasattr(self, 'company_id') or not self.company_id_id:
            if self.project_id:
                self.company_id = self.project_id.company_id

        # Set partner from project if not set
        if not hasattr(self, 'partner_id') or not self.partner_id_id:
            if self.project_id and self.project_id.partner_id:
                self.partner_id = self.project_id.partner_id

        # Set currency from company if not set
        if not hasattr(self, 'currency_id') or not self.currency_id_id:
            if hasattr(self, 'company_id') and self.company_id:
                self.currency_id = self.company_id.currency_id

        super().save(*args, **kwargs)

        # Update task effective hours
        if self.task_id:
            total_hours = AccountAnalyticLine.objects.filter(task_id=self.task_id).aggregate(
                total=models.Sum('unit_amount')
            )['total'] or 0.0

            ProjectTask.objects.filter(id=self.task_id.id).update(effective_hours=total_hours)


class AccountAnalyticTag(BaseModel):
    """Analytic Tag model - equivalent to account.analytic.tag in Odoo"""

    name = models.CharField(max_length=255, unique=True, help_text="Tag Name")
    color = models.IntegerField(default=0, help_text="Color Index")
    active = models.BooleanField(default=True)

    def __str__(self):
        return self.name
