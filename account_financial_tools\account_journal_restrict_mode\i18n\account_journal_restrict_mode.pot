# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_journal_restrict_mode
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_journal_restrict_mode
#: model:ir.model.fields,help:account_journal_restrict_mode.field_account_journal__restrict_mode_hash_table
msgid ""
"If ticked, the accounting entry or invoice receives a hash as soon as it is "
"posted and cannot be modified anymore."
msgstr ""

#. module: account_journal_restrict_mode
#: model:ir.model,name:account_journal_restrict_mode.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_journal_restrict_mode
#. odoo-python
#: code:addons/account_journal_restrict_mode/models/account_journal.py:0
#, python-format
msgid "Journal %s must have Lock Posted Entries enabled."
msgstr ""

#. module: account_journal_restrict_mode
#: model:ir.model.fields,field_description:account_journal_restrict_mode.field_account_journal__restrict_mode_hash_table
msgid "Lock Posted Entries with Hash"
msgstr ""
