# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_xlsx
#
# Translators:
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-05 17:53+0000\n"
"PO-Revision-Date: 2018-01-05 17:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: French (https://www.transifex.com/oca/teams/23907/fr/)\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_abstract
msgid "Abstract XLSX Report"
msgstr ""

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_partner_xlsx
msgid "Partner XLSX Report"
msgstr ""

#. module: report_xlsx
#: model:ir.actions.report,name:report_xlsx.partner_xlsx
msgid "Print to XLSX"
msgstr "Imprimer en XLSX"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_ir_actions_report__report_type
msgid "Report Type"
msgstr ""

#. module: report_xlsx
#: model:ir.model.fields,help:report_xlsx.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""

#. module: report_xlsx
#: model:ir.model.fields.selection,name:report_xlsx.selection__ir_actions_report__report_type__xlsx
msgid "XLSX"
msgstr ""

#, python-format
#~ msgid "%s model was not found"
#~ msgstr "model '%s' n'a pas été trouvé"

#~ msgid "Display Name"
#~ msgstr "Nom"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Dernière modification le"

#~ msgid "report.report_xlsx.partner_xlsx"
#~ msgstr "report.report_xlsx.partner_xlsx"

#~ msgid "ir.actions.report"
#~ msgstr "ir.actions.report"

#~ msgid "report.report_xlsx.abstract"
#~ msgstr "report.report_xlsx.abstract"
