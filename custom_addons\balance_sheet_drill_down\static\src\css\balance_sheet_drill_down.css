/* Balance Sheet Drill-Down Styles */

/* Drill-down amount styling */
.drill-down-amount {
    cursor: pointer !important;
    color: #007cba !important;
    transition: all 0.2s ease;
    position: relative;
}

.drill-down-amount:hover {
    background-color: #f0f8ff !important;
    text-decoration: underline !important;
    color: #005a8b !important;
    border-radius: 3px;
    padding: 2px 4px;
}

.drill-down-amount:active {
    background-color: #e6f3ff !important;
    transform: scale(0.98);
}

/* Add a small icon to indicate clickable amounts */
.drill-down-amount::after {
    content: "🔍";
    font-size: 10px;
    margin-left: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.drill-down-amount:hover::after {
    opacity: 0.7;
}

/* Balance Sheet specific styling */
.financial-report-table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.financial-report-table th {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #495057;
}

.financial-report-table td {
    border: 1px solid #dee2e6;
    padding: 8px;
    vertical-align: top;
}

/* Section headers (ASSETS, LIABILITIES, EQUITY) */
.financial-report-table tr.section-header td {
    background-color: #e9ecef;
    font-weight: bold;
    font-size: 1.1em;
    color: #495057;
    border-top: 2px solid #6c757d;
}

/* Sub-section styling */
.financial-report-table tr.sub-section td {
    background-color: #f8f9fa;
    font-weight: 600;
    padding-left: 20px;
}

/* Account line styling */
.financial-report-table tr.account-line td {
    padding-left: 40px;
}

/* Total lines */
.financial-report-table tr.total-line td {
    border-top: 2px solid #495057;
    border-bottom: 2px solid #495057;
    font-weight: bold;
    background-color: #f8f9fa;
}

/* Grand total */
.financial-report-table tr.grand-total td {
    border-top: 3px double #495057;
    border-bottom: 3px double #495057;
    font-weight: bold;
    font-size: 1.1em;
    background-color: #e9ecef;
}

/* Amount columns alignment */
.financial-report-table td.amount {
    text-align: right;
    font-family: 'Courier New', monospace;
    white-space: nowrap;
}

/* Zero amounts styling */
.financial-report-table td.amount.zero {
    color: #6c757d;
    font-style: italic;
}

/* Negative amounts styling */
.financial-report-table td.amount.negative {
    color: #dc3545;
}

/* Loading indicator for drill-down */
.drill-down-loading {
    position: relative;
}

.drill-down-loading::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip for drill-down hints */
.drill-down-tooltip {
    position: relative;
    display: inline-block;
}

.drill-down-tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.drill-down-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Print-friendly styles */
@media print {
    .drill-down-amount::after {
        display: none;
    }
    
    .drill-down-amount {
        color: black !important;
        cursor: default !important;
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .financial-report-table {
        font-size: 14px;
    }
    
    .financial-report-table th,
    .financial-report-table td {
        padding: 6px 4px;
    }
    
    .drill-down-amount::after {
        font-size: 8px;
        margin-left: 3px;
    }
}
