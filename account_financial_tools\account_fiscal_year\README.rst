===================
Account Fiscal Year
===================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:447e2f1c7b2151b56196120a7f8e32166d807fdb3f499ddcf3881b0103e6ceeb
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Production%2FStable-green.png
    :target: https://odoo-community.org/page/development-status
    :alt: Production/Stable
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--financial--tools-lightgray.png?logo=github
    :target: https://github.com/OCA/account-financial-tools/tree/17.0/account_fiscal_year
    :alt: OCA/account-financial-tools
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-financial-tools-17-0/account-financial-tools-17-0-account_fiscal_year
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-financial-tools&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows to create and edit fiscal years from the menu:

Invoicing > Configuration > Accounting > Fiscal Years

.. figure:: https://raw.githubusercontent.com/OCA/account-financial-tools/17.0/account_fiscal_year/static/description/account_fiscal_year_form.png

The start and end dates of the current fiscal years are then available
in the company tree and form views.

.. figure:: https://raw.githubusercontent.com/OCA/account-financial-tools/17.0/account_fiscal_year/static/description/res_company_tree.png

**Table of contents**

.. contents::
   :local:

Known issues / Roadmap
======================

The account.fiscal.year model definition and views come from the core of
Odoo v13 community because in v14, account.fiscal.year has been moved to
Odoo enterprise.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-financial-tools/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-financial-tools/issues/new?body=module:%20account_fiscal_year%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Agile Business Group
* Camptocamp SA

Contributors
------------

-  Damien Crier <<EMAIL>>
-  Laurent Mignon <<EMAIL>>
-  Lorenzo Battistini <https://github.com/eLBati>
-  Andrea Stirpe <<EMAIL>>
-  Simone Rubino <<EMAIL>>
-  `Pesol <https://www.pesol.es>`__:

   -  Pedro Evaristo Gonzalez Sanchez <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-eLBati| image:: https://github.com/eLBati.png?size=40px
    :target: https://github.com/eLBati
    :alt: eLBati

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-eLBati| 

This module is part of the `OCA/account-financial-tools <https://github.com/OCA/account-financial-tools/tree/17.0/account_fiscal_year>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
