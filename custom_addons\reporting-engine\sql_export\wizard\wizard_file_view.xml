<?xml version="1.0" encoding="utf-8" ?>
<odoo>
        <record id="sql_file_wizard_view_form" model="ir.ui.view">
            <field name="name">sql.file.wizard.view.form</field>
            <field name="model">sql.file.wizard</field>
            <field name="arch" type="xml">
                <form string="Csv File">
                    <field
                    name="query_properties"
                    nolabel="1"
                    columns="1"
                    hideKanbanOption="1"
                    required="1"
                />
                    <separator
                    string="Export file"
                    colspan="4"
                    invisible="not binary_file"
                />
                    <field name="binary_file" filename="file_name" />
                    <field name="file_name" invisible="1" />
                    <field name="sql_export_id" invisible="1" />
                    <footer>
                        <button
                        name="export_sql"
                        string="Export"
                        type="object"
                        icon="fa-download"
                    />
                        or
                        <button
                        special="cancel"
                        string="Cancel"
                        type="object"
                        icon="fa-times"
                    />
                    </footer>
                </form>
            </field>
        </record>
</odoo>
