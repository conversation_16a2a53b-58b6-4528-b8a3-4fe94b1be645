<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Financial Statement Analytics Report -->
        <record id="action_report_financial_statement_analytics" model="ir.actions.report">
            <field name="name">Financial Statement with Analytics</field>
            <field name="model">financial.statement.analytics</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">financial_statements_analytics.report_financial_statement_analytics_template</field>
            <field name="report_file">financial_statements_analytics.report_financial_statement_analytics_template</field>
            <field name="binding_model_id" ref="model_financial_statement_analytics"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="base.paperformat_euro"/>
        </record>

        <!-- Financial Analytics Dashboard Report -->
        <record id="action_report_financial_analytics_dashboard" model="ir.actions.report">
            <field name="name">Financial Analytics Dashboard</field>
            <field name="model">financial.analytics.dashboard</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">financial_statements_analytics.report_financial_analytics_dashboard_template</field>
            <field name="report_file">financial_statements_analytics.report_financial_analytics_dashboard_template</field>
            <field name="binding_model_id" ref="model_financial_analytics_dashboard"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="base.paperformat_euro"/>
        </record>

        <!-- Financial Ratio Analysis Report -->
        <record id="action_report_financial_ratio_analysis" model="ir.actions.report">
            <field name="name">Financial Ratio Analysis</field>
            <field name="model">financial.ratio.analysis</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">financial_statements_analytics.report_financial_ratio_analysis_template</field>
            <field name="report_file">financial_statements_analytics.report_financial_ratio_analysis_template</field>
            <field name="binding_model_id" ref="model_financial_ratio_analysis"/>
            <field name="binding_type">report</field>
            <field name="paperformat_id" ref="base.paperformat_euro"/>
        </record>



    </data>
</odoo>
