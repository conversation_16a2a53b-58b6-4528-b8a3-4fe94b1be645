# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_wkhtmltopdf_param
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-08-17 02:52+0000\n"
"PO-Revision-Date: 2022-06-15 18:05+0000\n"
"Last-Translator: jabe<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Catalan (https://www.transifex.com/oca/teams/23907/ca/)\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__create_date
msgid "Created on"
msgstr "Creat el"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat__custom_params
msgid "Custom Parameters"
msgstr "Paràmetres personalitzats"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,help:report_wkhtmltopdf_param.field_report_paperformat__custom_params
msgid "Custom Parameters passed forward as wkhtmltopdf command arguments"
msgstr ""
"Paràmetres personalitzats que es pasen com a arguments de l'ordre wkhtmltopdf"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__display_name
msgid "Display Name"
msgstr "Veure el nom"

#. module: report_wkhtmltopdf_param
#. odoo-python
#: code:addons/report_wkhtmltopdf_param/models/report_paperformat.py:0
#, python-format
msgid "Failed to create a PDF using the provided parameters."
msgstr "Ha fallat la creació d'un PDF amb els paràmetres proporcionats."

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__id
msgid "ID"
msgstr "ID"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__write_uid
msgid "Last Updated by"
msgstr "Darrera Actualització per"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__write_date
msgid "Last Updated on"
msgstr "Darrera Actualització el"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__name
msgid "Name"
msgstr "Nom"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__paperformat_id
msgid "Paper Format"
msgstr "Format de paper"

#. module: report_wkhtmltopdf_param
#: model:ir.model,name:report_wkhtmltopdf_param.model_report_paperformat
msgid "Paper Format Config"
msgstr "Configuració del format de paper"

#. module: report_wkhtmltopdf_param
#: model:ir.model,name:report_wkhtmltopdf_param.model_ir_actions_report
msgid "Report Action"
msgstr "Acció d'informe"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,help:report_wkhtmltopdf_param.field_report_paperformat_parameter__name
msgid "The command argument name. Remember to add prefix -- or -"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__value
msgid "Value"
msgstr "Valor"

#. module: report_wkhtmltopdf_param
#: model:ir.model,name:report_wkhtmltopdf_param.model_report_paperformat_parameter
msgid "wkhtmltopdf parameters"
msgstr "paràmetres wkhtmltopdf"

#~ msgid "Last Modified on"
#~ msgstr "Darrera modificació el"
