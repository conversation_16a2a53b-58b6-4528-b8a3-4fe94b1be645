# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_daily_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 06:44+0000\n"
"PO-Revision-Date: 2022-07-06 00:10+0200\n"
"Last-Translator: <PERSON>ylvain Lc\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.1\n"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>End Date:</strong>"
msgstr "<strong>Date de fin :</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Journals:</strong>"
msgstr "<strong>Journaux:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Trié par:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Date de début:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Déplacements cibles:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
msgid "Account Bank Book"
msgstr "Journal de compte bancaire"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "Account Cash Book"
msgstr "Journal de caisse"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Account Day Book"
msgstr "Journal de comptes"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__account_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__account_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__account_ids
msgid "Accounts"
msgstr "Comptes"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__all
msgid "All"
msgstr "Toute"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__target_move__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__target_move__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_daybook_report__target_move__all
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "All Entries"
msgstr "Toutes les écritures"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Balance"
msgstr "Équilibre"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_bankbook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_bank_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_bankbook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_bankbook
msgid "Bank Book"
msgstr "Livret de banque"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_bankbook_report
msgid "Bank Book Report"
msgstr "Rapport sur le livre de banque"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Cancel"
msgstr "Annuler"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_cashbook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_cash_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_cashbook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_cashbook
msgid "Cash Book"
msgstr "Livre de caisse"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_cashbook_report
msgid "Cash Book Report"
msgstr "Rapport de livre de caisse"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__create_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__create_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__create_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__create_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__create_date
msgid "Created on"
msgstr "Créé sur"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Credit"
msgstr "Crédit"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Currency"
msgstr "Devise"

#. module: om_account_daily_reports
#: model:ir.ui.menu,name:om_account_daily_reports.menu_finance_daily_reports
msgid "Daily Reports"
msgstr "Rapports quotidiens"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__sortby__sort_date
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__sortby__sort_date
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Date"
msgstr "Fecha"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_daybook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_day_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_daybook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_daybook
msgid "Day Book"
msgstr "Carnet de jour"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_daybook_report
msgid "Day Book Report"
msgstr "Rapport journalier"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Debit"
msgstr "Débit"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__display_account
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__display_account
msgid "Display Accounts"
msgstr "Afficher les comptes"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__display_name
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__display_name
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__display_name
msgid "Display Name"
msgstr "Afficher un nom"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__date_to
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__date_to
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__date_to
msgid "End Date"
msgstr "Date de fin"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Entry Label"
msgstr "Étiquette d'entrée"

#. module: om_account_daily_reports
#: code:addons/om_account_daily_reports/report/report_bankbook.py:0
#: code:addons/om_account_daily_reports/report/report_cashbook.py:0
#: code:addons/om_account_daily_reports/report/report_daybook.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""
"Le contenu du formulaire est manquant, le rapport ne peut pas être "
"imprimé."

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__id
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__id
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__id
msgid "ID"
msgstr "ID"

#. module: om_account_daily_reports
#: model:ir.model.fields,help:om_account_daily_reports.field_account_bankbook_report__initial_balance
#: model:ir.model.fields,help:om_account_daily_reports.field_account_cashbook_report__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Si vous avez sélectionné la date, ce champ vous permet d'ajouter une "
"ligne pour afficher le montant de débit / crédit / solde qui précède "
"le filtre que vous avez défini."

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__initial_balance
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__initial_balance
msgid "Include Initial Balances"
msgstr "Inclure les soldes initiaux"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "JRNL"
msgstr "JRNL"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr "Journal & partenair"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "Journal and Partner"
msgstr "Journal et partenaire"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__journal_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__journal_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__journal_ids
msgid "Journals"
msgstr "Journaux"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report____last_update
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report____last_update
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__write_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__write_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__write_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__write_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Move"
msgstr "Mouvement"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Partner"
msgstr "Partenaire"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__target_move__posted
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__target_move__posted
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_daybook_report__target_move__posted
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Posted Entries"
msgstr "Entrées publiées"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Print"
msgstr "Imprimer"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Ref"
msgstr "Réf"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Report Options"
msgstr "Options du rapport"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__sortby
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__sortby
msgid "Sort by"
msgstr "Trier par"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__date_from
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__date_from
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__date_from
msgid "Start Date"
msgstr "Date de début"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__target_move
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__target_move
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__target_move
msgid "Target Moves"
msgstr "Mouvements cibles"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__not_zero
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "Avec solde n'est pas égal à 0"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__movement
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__movement
msgid "With movements"
msgstr "Avec des mouvements"
