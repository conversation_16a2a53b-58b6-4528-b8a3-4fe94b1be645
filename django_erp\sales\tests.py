from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from django.utils import timezone
from core.models import Company, Country, Currency, Partner
from accounting.models import AccountTax
from .models import (
    SaleOrder, SaleOrderLine, SalesTeam, ProductPricelist,
    ProductUom, ProductUomCategory, DeliveryCarrier, StockWarehouse
)
from accounting.models import AccountFiscalPosition, AccountPaymentTerm

class SalesModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='salesuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test Sales Company',
            code='TSC',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create customer
        self.customer = Partner.objects.create(
            name='Test Customer',
            customer_rank=1,
            credit_limit=Decimal('10000.00'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Create sales team
        self.sales_team = SalesTeam.objects.create(
            name='Sales Team 1',
            code='ST1',
            company_id=self.company,
            user_id=self.user,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create pricelist
        self.pricelist = ProductPricelist.objects.create(
            name='Public Pricelist',
            currency_id=self.currency,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create UOM category and UOM
        self.uom_category = ProductUomCategory.objects.create(
            name='Unit',
            create_uid=self.user,
            write_uid=self.user
        )

        self.uom_unit = ProductUom.objects.create(
            name='Unit',
            category_id=self.uom_category,
            uom_type='reference',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create warehouse
        self.warehouse = StockWarehouse.objects.create(
            name='Main Warehouse',
            code='WH',
            company_id=self.company,
            partner_id=self.customer,  # Using customer as address for simplicity
            create_uid=self.user,
            write_uid=self.user
        )

        # Create tax
        self.sales_tax = AccountTax.objects.create(
            name='Sales Tax 10%',
            type_tax_use='sale',
            amount_type='percent',
            amount=Decimal('10.0'),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create sales journal for invoicing
        from accounting.models import AccountJournal, AccountAccount

        # Create bank account for journal
        self.bank_account = AccountAccount.objects.create(
            code='101000',
            name='Bank Account',
            account_type='asset_current',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create income account for sales
        self.income_account = AccountAccount.objects.create(
            code='400000',
            name='Sales Revenue',
            account_type='income',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create receivable account for customers
        self.receivable_account = AccountAccount.objects.create(
            code='120000',
            name='Accounts Receivable',
            account_type='asset_receivable',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        self.sales_journal = AccountJournal.objects.create(
            name='Sales Journal',
            code='SAL',
            type='sale',
            company_id=self.company,
            default_account_id=self.bank_account,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create product category, template and product
        from inventory.models import ProductCategory, ProductTemplate, Product

        self.product_category = ProductCategory.objects.create(
            name='Test Category',
            create_uid=self.user,
            write_uid=self.user
        )

        self.product_template = ProductTemplate.objects.create(
            name='Test Product',
            detailed_type='product',  # Stockable product
            categ_id=self.product_category,
            uom_id=self.uom_unit,
            uom_po_id=self.uom_unit,
            list_price=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        self.product = Product.objects.create(
            product_tmpl_id=self.product_template,
            create_uid=self.user,
            write_uid=self.user
        )

        # Alias for compatibility
        self.uom = self.uom_unit

    def test_sale_order_creation(self):
        """Test sale order creation and basic functionality"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        self.assertEqual(order.name, 'New')
        self.assertEqual(order.state, 'draft')
        self.assertEqual(order.invoice_status, 'no')
        self.assertEqual(str(order), 'New')

    def test_sale_order_confirmation(self):
        """Test sale order confirmation process"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line
        line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product',
            product_uom_qty=Decimal('2.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        line.tax_id.add(self.sales_tax)

        # Confirm order
        order.action_confirm()

        self.assertEqual(order.state, 'sale')
        self.assertNotEqual(order.name, 'New')
        self.assertTrue(order.name.startswith('SO/'))
        self.assertIsNotNone(order.date_order)

    def test_sale_order_line_amounts(self):
        """Test sale order line amount calculations"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line with discount and tax
        line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product with Discount',
            product_uom_qty=Decimal('2.0'),
            price_unit=Decimal('100.0'),
            discount=Decimal('10.0'),  # 10% discount
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        line.tax_id.add(self.sales_tax)
        # Recompute amounts after adding tax
        line._compute_amounts()
        line.save()

        # Check computed amounts
        expected_subtotal = Decimal('180.0')  # (100 * 2) - 10% discount
        expected_tax = Decimal('18.0')  # 10% tax on subtotal
        expected_total = Decimal('198.0')  # subtotal + tax

        self.assertEqual(line.price_subtotal, expected_subtotal)
        self.assertEqual(line.price_tax, expected_tax)
        self.assertEqual(line.price_total, expected_total)
        self.assertEqual(line.price_reduce, Decimal('90.0'))  # 100 - 10% discount

    def test_sale_order_totals(self):
        """Test sale order total calculations"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add multiple order lines
        line1 = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Product 1',
            product_uom_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        line1.tax_id.add(self.sales_tax)
        # Recompute amounts after adding tax
        line1._compute_amounts()
        line1.save()

        line2 = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Product 2',
            product_uom_qty=Decimal('2.0'),
            price_unit=Decimal('50.0'),
            discount=Decimal('20.0'),  # 20% discount
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )
        line2.tax_id.add(self.sales_tax)
        # Recompute amounts after adding tax
        line2._compute_amounts()
        line2.save()

        # Manually trigger amount computation to ensure it's calculated
        order._compute_amounts()
        order.save()

        # Check order totals
        expected_untaxed = Decimal('180.00')  # 100 + (50*2 - 20%)
        expected_tax = Decimal('18.00')  # 10% of untaxed
        expected_total = Decimal('198.00')  # untaxed + tax

        self.assertEqual(order.amount_untaxed, expected_untaxed)
        self.assertEqual(order.amount_tax, expected_tax)
        self.assertEqual(order.amount_total, expected_total)

    def test_sale_order_line_validation(self):
        """Test sale order line validation rules"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test that accountable line requires UOM
        with self.assertRaises(ValidationError):
            line = SaleOrderLine(
                order_id=order,
                name='Invalid Line',
                product_uom_qty=Decimal('1.0'),
                price_unit=Decimal('100.0'),
                # Missing product_uom
                create_uid=self.user,
                write_uid=self.user
            )
            line.full_clean()

        # Test that section line cannot have product values
        with self.assertRaises(ValidationError):
            line = SaleOrderLine(
                order_id=order,
                name='Section Line',
                display_type='line_section',
                price_unit=Decimal('100.0'),  # Should be 0 for section lines
                create_uid=self.user,
                write_uid=self.user
            )
            line.full_clean()

    def test_sale_order_state_transitions(self):
        """Test sale order state transitions"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test cancel from draft
        order.action_cancel()
        self.assertEqual(order.state, 'cancel')

        # Test reset to draft
        order.action_draft()
        self.assertEqual(order.state, 'draft')

        # Test confirmation requires order lines for proper testing
        # (in real scenario, empty orders might be allowed)
        line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Test Product',
            product_uom_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        order.action_confirm()
        self.assertEqual(order.state, 'sale')

    def test_credit_limit_validation(self):
        """Test customer credit limit validation"""
        # Create customer with low credit limit
        low_credit_customer = Partner.objects.create(
            name='Low Credit Customer',
            customer_rank=1,
            credit_limit=Decimal('100.00'),  # Low limit
            create_uid=self.user,
            write_uid=self.user
        )

        # Create sale order exceeding credit limit
        order = SaleOrder.objects.create(
            partner_id=low_credit_customer,
            partner_invoice_id=low_credit_customer,
            partner_shipping_id=low_credit_customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            credit_limit_check=True,  # Enable credit limit checking
            create_uid=self.user,
            write_uid=self.user
        )

        # Add expensive line
        line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Expensive Product',
            product_uom_qty=Decimal('1.0'),
            price_unit=Decimal('200.0'),  # Exceeds credit limit
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        # Should raise validation error when confirming
        with self.assertRaises(ValidationError):
            order.action_confirm()

    def test_section_and_note_lines(self):
        """Test section and note lines functionality"""
        # Create sale order
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add section line
        section_line = SaleOrderLine.objects.create(
            order_id=order,
            name='=== PRODUCTS ===',
            display_type='line_section',
            sequence=1,
            # For display lines, all product-related fields must be 0/NULL
            product_uom_qty=0,
            price_unit=0,
            customer_lead=0,
            product_uom=None,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add note line
        note_line = SaleOrderLine.objects.create(
            order_id=order,
            name='Special delivery instructions',
            display_type='line_note',
            sequence=2,
            # For display lines, all product-related fields must be 0/NULL
            product_uom_qty=0,
            price_unit=0,
            customer_lead=0,
            product_uom=None,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add product line
        product_line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,  # Required for accountable lines
            name='Actual Product',
            product_uom_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            sequence=3,
            create_uid=self.user,
            write_uid=self.user
        )

        # Check that section and note lines have zero amounts
        self.assertEqual(section_line.price_subtotal, Decimal('0.0'))
        self.assertEqual(note_line.price_subtotal, Decimal('0.0'))
        self.assertEqual(product_line.price_subtotal, Decimal('100.0'))

        # Check order total only includes product lines
        order.refresh_from_db()
        self.assertEqual(order.amount_untaxed, Decimal('100.0'))

    def test_supporting_models(self):
        """Test supporting models creation and relationships"""
        # Test Sales Team
        team = SalesTeam.objects.create(
            name='Test Team',
            code='TT',
            company_id=self.company,
            user_id=self.user,
            create_uid=self.user,
            write_uid=self.user
        )
        team.member_ids.add(self.user)
        self.assertEqual(str(team), 'Test Team')
        self.assertIn(self.user, team.member_ids.all())

        # Test Fiscal Position
        fiscal_pos = AccountFiscalPosition.objects.create(
            name='Domestic',
            company_id=self.company,
            auto_apply=True,
            create_uid=self.user,
            write_uid=self.user
        )
        self.assertEqual(str(fiscal_pos), 'Domestic')

        # Test Payment Terms
        payment_term = AccountPaymentTerm.objects.create(
            name='30 Days',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        self.assertEqual(str(payment_term), '30 Days')

        # Test Delivery Carrier
        carrier = DeliveryCarrier.objects.create(
            name='Standard Delivery',
            delivery_type='fixed',
            fixed_price=Decimal('15.0'),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        self.assertEqual(str(carrier), 'Standard Delivery')

    def test_sequence_generation(self):
        """Test sequence number generation"""
        # Create and confirm first order
        order1 = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        line1 = SaleOrderLine.objects.create(
            order_id=order1,
            product_id=self.product,  # Required for accountable lines
            name='Product 1',
            product_uom_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        order1.action_confirm()
        first_name = order1.name

        # Create and confirm second order
        order2 = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        line2 = SaleOrderLine.objects.create(
            order_id=order2,
            product_id=self.product,  # Required for accountable lines
            name='Product 2',
            product_uom_qty=Decimal('1.0'),
            price_unit=Decimal('100.0'),
            product_uom=self.uom_unit,
            create_uid=self.user,
            write_uid=self.user
        )

        order2.action_confirm()
        second_name = order2.name

        # Check that sequence numbers are incremental
        self.assertTrue(first_name.startswith('SO/'))
        self.assertTrue(second_name.startswith('SO/'))
        self.assertNotEqual(first_name, second_name)

        # Extract numbers and verify increment
        first_num = int(first_name.split('/')[-1])
        second_num = int(second_name.split('/')[-1])
        self.assertEqual(second_num, first_num + 1)

    def test_enhanced_order_confirmation_workflow(self):
        """Test enhanced order confirmation with delivery creation"""
        # Create a sales order with stockable product
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            pricelist_id=self.pricelist,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add order line with stockable product
        line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,
            name='Test Product',
            product_uom_qty=Decimal('5.0'),
            product_uom=self.uom,
            price_unit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertEqual(order.state, 'draft')
        self.assertEqual(order.invoice_status, 'no')
        self.assertEqual(order.delivery_status, 'no')

        # Confirm the order
        order.action_confirm()

        # Test confirmed state
        self.assertEqual(order.state, 'sale')
        self.assertNotEqual(order.name, 'New')
        self.assertIsNotNone(order.date_order)

        # Test status updates
        order._compute_invoice_status()
        order._compute_delivery_status()

        # Should be ready to invoice and deliver
        self.assertIn(order.invoice_status, ['to invoice', 'no'])
        self.assertIn(order.delivery_status, ['to_deliver', 'no'])

    def test_order_validation_before_confirmation(self):
        """Test order validation before confirmation"""
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            pricelist_id=self.pricelist,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test validation with no lines
        with self.assertRaises(ValidationError):
            order._validate_before_confirmation()

        # Add line without product (display_type line)
        display_line = SaleOrderLine.objects.create(
            order_id=order,
            name='Section Header',
            display_type='line_section',
            # For display lines, all product-related fields must be 0/NULL
            product_uom_qty=0,
            price_unit=0,
            customer_lead=0,
            product_uom=None,
            create_uid=self.user,
            write_uid=self.user
        )

        # Should still fail validation (no product lines)
        # The display_type line should not count as a product line
        with self.assertRaises(ValidationError):
            order._validate_before_confirmation()

        # Add proper product line
        product_line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,
            name='Test Product',
            product_uom_qty=Decimal('1.0'),
            product_uom=self.uom,
            price_unit=Decimal('100.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Should pass validation now
        try:
            order._validate_before_confirmation()
        except ValidationError:
            self.fail("Validation should pass with proper product line")

    def test_order_status_computation(self):
        """Test order status computation methods"""
        order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            pricelist_id=self.pricelist,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test with no lines
        order._compute_invoice_status()
        order._compute_delivery_status()
        self.assertEqual(order.invoice_status, 'no')
        self.assertEqual(order.delivery_status, 'no')

        # Add line
        line = SaleOrderLine.objects.create(
            order_id=order,
            product_id=self.product,
            name='Test Product',
            product_uom_qty=Decimal('3.0'),
            product_uom=self.uom,
            price_unit=Decimal('200.0'),
            create_uid=self.user,
            write_uid=self.user
        )

        # Recompute status
        order._compute_invoice_status()
        order._compute_delivery_status()

        # Should have something to invoice/deliver
        self.assertNotEqual(order.invoice_status, 'no')

    def test_subscription_order_creation(self):
        """Test subscription order creation and recurring invoice generation"""
        from datetime import date, timedelta

        # Create subscription order
        subscription_order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            is_subscription=True,
            subscription_management='create',
            recurring_rule_type='monthly',
            recurring_interval=1,
            next_invoice_date=date.today(),
            create_uid=self.user,
            write_uid=self.user
        )

        # Add subscription line
        SaleOrderLine.objects.create(
            order_id=subscription_order,
            product_id=self.product,
            name='Monthly Subscription',
            product_uom_qty=1,
            price_unit=100.0,
            product_uom=self.uom_unit,
            is_subscription_line=True,
            subscription_start_date=date.today(),
            subscription_end_date=date.today() + timedelta(days=365),
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Confirm subscription order
        subscription_order.action_confirm()

        # Test subscription properties
        self.assertTrue(subscription_order.is_subscription)
        self.assertEqual(subscription_order.recurring_rule_type, 'monthly')
        self.assertEqual(subscription_order.recurring_interval, 1)

        # Test recurring invoice creation
        invoices = subscription_order._create_recurring_invoices()
        self.assertEqual(len(invoices), 1)

        # Check that next invoice date was updated
        subscription_order.refresh_from_db()
        expected_next_date = date.today() + timedelta(days=30)  # Approximately monthly
        self.assertGreater(subscription_order.next_invoice_date, date.today())

    def test_milestone_billing(self):
        """Test milestone-based billing"""
        from datetime import date, timedelta

        # Create milestone order
        milestone_order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            milestone_billing=True,
            create_uid=self.user,
            write_uid=self.user
        )

        # Add milestone lines
        milestone1 = SaleOrderLine.objects.create(
            order_id=milestone_order,
            product_id=self.product,
            name='Milestone 1: Planning',
            product_uom_qty=1,
            price_unit=1000.0,
            product_uom=self.uom_unit,
            is_milestone=True,
            milestone_name='Planning Phase',
            milestone_date=date.today() + timedelta(days=30),
            milestone_percentage=30.0,
            milestone_reached=True,  # Mark as reached
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        milestone2 = SaleOrderLine.objects.create(
            order_id=milestone_order,
            product_id=self.product,
            name='Milestone 2: Development',
            product_uom_qty=1,
            price_unit=2000.0,
            product_uom=self.uom_unit,
            is_milestone=True,
            milestone_name='Development Phase',
            milestone_date=date.today() + timedelta(days=60),
            milestone_percentage=70.0,
            milestone_reached=False,  # Not reached yet
            company_id=self.company,
            currency_id=self.currency,
            create_uid=self.user,
            write_uid=self.user
        )

        # Confirm milestone order
        milestone_order.action_confirm()

        # Test milestone invoice creation (only reached milestones)
        invoices = milestone_order._create_milestone_invoices()
        self.assertEqual(len(invoices), 1)

        # Check that only milestone 1 was invoiced
        milestone1.refresh_from_db()
        milestone2.refresh_from_db()
        self.assertGreater(milestone1.qty_invoiced, 0)
        self.assertEqual(milestone2.qty_invoiced, 0)

    def test_approval_workflow(self):
        """Test order approval workflow"""
        # Create order requiring approval
        approval_order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test initial state
        self.assertEqual(approval_order.approval_status, 'draft')

        # Request approval
        approval_order.action_request_approval()
        self.assertEqual(approval_order.approval_status, 'pending')

        # Approve order
        approval_order.action_approve(self.user)
        self.assertEqual(approval_order.approval_status, 'approved')
        self.assertEqual(approval_order.approved_by, self.user)
        self.assertIsNotNone(approval_order.approved_date)

        # Test rejection workflow
        rejection_order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            create_uid=self.user,
            write_uid=self.user
        )

        rejection_order.action_request_approval()
        rejection_order.action_reject(self.user, "Budget exceeded")
        self.assertEqual(rejection_order.approval_status, 'rejected')

    def test_credit_limit_check(self):
        """Test credit limit checking"""
        # Set credit limit on customer (would be done via partner model enhancement)
        # For now, we'll test the method exists and basic functionality

        credit_order = SaleOrder.objects.create(
            partner_id=self.customer,
            partner_invoice_id=self.customer,
            partner_shipping_id=self.customer,
            company_id=self.company,
            currency_id=self.currency,
            pricelist_id=self.pricelist,
            user_id=self.user,
            team_id=self.sales_team,
            warehouse_id=self.warehouse,
            credit_limit_check=True,
            amount_total=5000.0,
            create_uid=self.user,
            write_uid=self.user
        )

        # Test credit limit check method exists
        result = credit_order._check_credit_limit()
        self.assertIsInstance(result, bool)
