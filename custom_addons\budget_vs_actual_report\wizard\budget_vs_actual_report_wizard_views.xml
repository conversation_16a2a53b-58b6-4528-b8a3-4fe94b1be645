<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Budget vs Actual Report Wizard Form View -->
    <record id="view_budget_vs_actual_report_wizard_form" model="ir.ui.view">
        <field name="name">budget.vs.actual.report.wizard.form</field>
        <field name="model">budget.vs.actual.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Budget vs Actual Report">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Report Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Report Period">
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group string="Filters">
                            <field name="budget_id"/>
                            <field name="report_type"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Account Selection">
                            <group>
                                <field name="account_ids" widget="many2many_tags" placeholder="Leave empty to include all accounts"/>
                                <field name="analytic_account_ids" widget="many2many_tags" placeholder="Leave empty to include all analytic accounts"/>
                            </group>
                        </page>
                        
                        <page string="Display Options">
                            <group>
                                <group string="Show Options">
                                    <field name="show_zero_budget"/>
                                    <field name="show_zero_actual"/>
                                </group>
                                <group string="Variance Filter">
                                    <field name="variance_threshold" widget="percentage"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                
                <footer>
                    <div class="row">
                        <div class="col-md-3">
                            <button name="action_quick_monthly_report" string="Monthly Report" type="object" class="btn-secondary"/>
                        </div>
                        <div class="col-md-3">
                            <button name="action_quick_quarterly_report" string="Quarterly Report" type="object" class="btn-secondary"/>
                        </div>
                        <div class="col-md-3">
                            <button name="action_quick_yearly_report" string="Annual Report" type="object" class="btn-secondary"/>
                        </div>
                        <div class="col-md-3">
                            <button name="action_generate_report" string="Generate Report" type="object" class="btn-primary"/>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <button name="action_print_report" string="Print PDF" type="object" class="btn-secondary"/>
                        </div>
                        <div class="col-md-6">
                            <button name="action_export_excel" string="Export Excel" type="object" class="btn-secondary"/>
                        </div>
                    </div>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Budget vs Actual Report Wizard Action -->
    <record id="action_budget_vs_actual_report_wizard" model="ir.actions.act_window">
        <field name="name">Budget vs Actual Report</field>
        <field name="res_model">budget.vs.actual.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_budget_vs_actual_report_wizard_form"/>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_budget_vs_actual_report"
              name="Budget vs Actual Report"
              parent="account.account_reports_management_menu"
              action="action_budget_vs_actual_report_wizard"
              sequence="25"/>
</odoo>
