<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Financial Statement Analytics Form View -->
        <record id="view_financial_statement_analytics_form" model="ir.ui.view">
            <field name="name">financial.statement.analytics.form</field>
            <field name="model">financial.statement.analytics</field>
            <field name="arch" type="xml">
                <form string="Financial Statement with Analytics">
                    <header>
                        <button name="action_generate_statement" type="object"
                                string="Generate Statement" class="btn-primary"
                                invisible="state not in ['draft', 'in_progress']"/>
                        <button name="action_view_analytics_dashboard" type="object"
                                string="Analytics Dashboard" class="btn-info"
                                invisible="state == 'draft'"/>
                        <button name="action_submit_for_review" type="object"
                                string="Submit for Review" class="btn-secondary"
                                invisible="state != 'generated'"/>
                        <button name="action_approve" type="object"
                                string="Approve &amp; Publish" class="btn-success"
                                invisible="state != 'reviewed'"
                                groups="financial_statements_analytics.group_financial_analytics_manager"/>
                        <button name="action_reset_to_draft" type="object"
                                string="Reset to Draft" class="btn-warning"
                                invisible="state == 'draft'"/>
                        <button name="action_export_excel" type="object"
                                string="Export Excel" class="btn-secondary"
                                invisible="state == 'draft'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,generated,reviewed,published"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_analytics_dashboard" type="object"
                                    class="oe_stat_button" icon="fa-dashboard"
                                    invisible="state == 'draft'">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Analytics</span>
                                    <span class="o_stat_text">Dashboard</span>
                                </div>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Financial Statement Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="statement_type"/>
                                <field name="reporting_period"/>
                                <field name="currency_id" groups="base.group_multi_currency"/>
                            </group>
                            <group>
                                <field name="date_from"/>
                                <field name="date_to"/>
                                <field name="comparative_period"/>
                                <field name="comparative_date_from" invisible="not comparative_period"/>
                                <field name="comparative_date_to" invisible="not comparative_period"/>
                            </group>
                        </group>
                        
                        <group string="Analytics Configuration">
                            <group>
                                <field name="enable_analytics"/>
                                <field name="enable_charts" invisible="not enable_analytics"/>
                            </group>
                            <group>
                                <field name="enable_drill_down" invisible="not enable_analytics"/>
                            </group>
                        </group>
                        
                        <!-- Key Financial Metrics -->
                        <group string="Key Financial Metrics" invisible="state == 'draft'">
                            <group>
                                <field name="total_assets" widget="monetary"/>
                                <field name="total_liabilities" widget="monetary"/>
                                <field name="total_equity" widget="monetary"/>
                            </group>
                            <group>
                                <field name="total_revenue" widget="monetary"/>
                                <field name="net_income" widget="monetary"/>
                            </group>
                        </group>
                        
                        <!-- Key Ratios -->
                        <group string="Key Financial Ratios" invisible="state == 'draft' or not enable_analytics">
                            <group>
                                <field name="current_ratio"/>
                                <field name="quick_ratio"/>
                                <field name="debt_to_equity_ratio"/>
                            </group>
                            <group>
                                <field name="return_on_equity"/>
                                <field name="return_on_assets"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Statement Lines" invisible="state == 'draft'">
                                <field name="statement_line_ids" context="{'default_statement_id': active_id}">
                                    <tree editable="bottom" decoration-bf="line_type == 'header'" 
                                          decoration-it="line_type == 'subtotal'" decoration-danger="line_type == 'total'">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="line_type"/>
                                        <field name="line_section"/>
                                        <field name="indent_level"/>
                                        <field name="current_amount" widget="monetary" sum="Total Current"/>
                                        <field name="comparative_amount" widget="monetary" sum="Total Comparative" 
                                               invisible="not parent.comparative_period"/>
                                        <field name="variance_amount" widget="monetary" 
                                               invisible="not parent.comparative_period"/>
                                        <field name="variance_percentage" 
                                               invisible="not parent.comparative_period"/>
                                        <field name="is_expandable" invisible="not parent.enable_drill_down"/>
                                        <button name="action_drill_down" type="object" 
                                                icon="fa-search-plus" title="Drill Down"
                                                invisible="not is_expandable or not parent.enable_drill_down"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Ratio Analysis" invisible="state == 'draft' or not enable_analytics">
                                <field name="ratio_analysis_ids" context="{'default_statement_id': active_id}">
                                    <tree decoration-success="interpretation == 'excellent'" 
                                          decoration-info="interpretation == 'good'"
                                          decoration-warning="interpretation == 'needs_attention'"
                                          decoration-danger="interpretation == 'poor'">
                                        <field name="ratio_category"/>
                                        <field name="ratio_name"/>
                                        <field name="ratio_value"/>
                                        <field name="benchmark_value"/>
                                        <field name="variance_from_benchmark"/>
                                        <field name="interpretation"/>
                                        <field name="trend_direction" widget="badge" 
                                               decoration-success="trend_direction == 'improving'"
                                               decoration-warning="trend_direction == 'stable'"
                                               decoration-danger="trend_direction == 'declining'"/>
                                        <field name="trend_percentage"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Audit Trail">
                                <group>
                                    <group>
                                        <field name="prepared_by" readonly="1"/>
                                        <field name="prepared_date" readonly="1"/>
                                        <field name="reviewed_by" readonly="1"/>
                                        <field name="reviewed_date" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="approved_by" readonly="1"/>
                                        <field name="approved_date" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Financial Statement Analytics Tree View -->
        <record id="view_financial_statement_analytics_tree" model="ir.ui.view">
            <field name="name">financial.statement.analytics.tree</field>
            <field name="model">financial.statement.analytics</field>
            <field name="arch" type="xml">
                <tree string="Financial Statements with Analytics" 
                      decoration-success="state == 'published'" 
                      decoration-info="state == 'reviewed'" 
                      decoration-warning="state == 'generated'">
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="statement_type"/>
                    <field name="reporting_period"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <field name="total_assets" widget="monetary" sum="Total Assets"/>
                    <field name="net_income" widget="monetary" sum="Total Net Income"/>
                    <field name="current_ratio"/>
                    <field name="return_on_equity"/>
                    <field name="state" decoration-success="state=='published'" 
                           decoration-info="state=='reviewed'" decoration-warning="state=='generated'"/>
                    <field name="prepared_by"/>
                    <button name="action_view_analytics_dashboard" type="object" 
                            icon="fa-dashboard" title="Analytics Dashboard"
                            invisible="state == 'draft'"/>
                </tree>
            </field>
        </record>

        <!-- Financial Statement Analytics Search View -->
        <record id="view_financial_statement_analytics_search" model="ir.ui.view">
            <field name="name">financial.statement.analytics.search</field>
            <field name="model">financial.statement.analytics</field>
            <field name="arch" type="xml">
                <search string="Financial Statements with Analytics">
                    <field name="name" string="Statement Name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="statement_type"/>
                    <field name="prepared_by"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Generated" name="generated" domain="[('state', '=', 'generated')]"/>
                    <filter string="Published" name="published" domain="[('state', '=', 'published')]"/>
                    <separator/>
                    <filter string="Current Year" name="current_year" 
                            domain="[('date_to', '&gt;=', datetime.datetime.now().strftime('%Y-01-01')),
                                     ('date_to', '&lt;=', datetime.datetime.now().strftime('%Y-12-31'))]"/>
                    <filter string="My Statements" name="my_statements" 
                            domain="[('prepared_by', '=', uid)]"/>
                    <separator/>
                    <filter string="With Analytics" name="with_analytics" 
                            domain="[('enable_analytics', '=', True)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Company" name="group_company" context="{'group_by': 'company_id'}" 
                                groups="base.group_multi_company"/>
                        <filter string="Statement Type" name="group_type" context="{'group_by': 'statement_type'}"/>
                        <filter string="Reporting Period" name="group_period" context="{'group_by': 'reporting_period'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Prepared By" name="group_prepared_by" context="{'group_by': 'prepared_by'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Financial Statement Analytics Action -->
        <record id="action_financial_statement_analytics" model="ir.actions.act_window">
            <field name="name">Financial Statements with Analytics</field>
            <field name="res_model">financial.statement.analytics</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_financial_statement_analytics_search"/>
            <field name="context">{'search_default_current_year': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Financial Statement with Analytics!
                </p>
                <p>
                    Generate professional financial statements with advanced analytics,
                    interactive dashboards, and comprehensive ratio analysis.
                </p>
                <p>
                    Features include:
                    <ul>
                        <li>Professional balance sheet formatting</li>
                        <li>Interactive analytics dashboard</li>
                        <li>Comprehensive ratio analysis</li>
                        <li>Trend analysis and comparisons</li>
                        <li>Drill-down capabilities</li>
                        <li>Export to Excel and PDF</li>
                    </ul>
                </p>
            </field>
        </record>

    </data>
</odoo>
