<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Continent Data -->
        
        <record id="continent_africa" model="financial.planning.continent">
            <field name="name">Africa</field>
            <field name="code">AF</field>
            <field name="population">1340.0</field>
            <field name="area_km2">30300000</field>
            <field name="growth_potential">high</field>
            <field name="market_maturity">emerging</field>
            <field name="notes">Africa is the world's second-largest and second-most populous continent with significant growth potential.</field>
        </record>

        <record id="continent_asia" model="financial.planning.continent">
            <field name="name">Asia</field>
            <field name="code">AS</field>
            <field name="population">4641.0</field>
            <field name="area_km2">44600000</field>
            <field name="growth_potential">very_high</field>
            <field name="market_maturity">developing</field>
            <field name="notes">Asia is the largest and most populous continent with diverse economies and high growth potential.</field>
        </record>

        <record id="continent_europe" model="financial.planning.continent">
            <field name="name">Europe</field>
            <field name="code">EU</field>
            <field name="population">748.0</field>
            <field name="area_km2">10180000</field>
            <field name="growth_potential">medium</field>
            <field name="market_maturity">developed</field>
            <field name="notes">Europe is a developed continent with mature markets and stable economies.</field>
        </record>

        <record id="continent_north_america" model="financial.planning.continent">
            <field name="name">North America</field>
            <field name="code">NA</field>
            <field name="population">579.0</field>
            <field name="area_km2">24710000</field>
            <field name="growth_potential">medium</field>
            <field name="market_maturity">developed</field>
            <field name="notes">North America includes developed economies with mature markets.</field>
        </record>

        <record id="continent_south_america" model="financial.planning.continent">
            <field name="name">South America</field>
            <field name="code">SA</field>
            <field name="population">434.0</field>
            <field name="area_km2">17840000</field>
            <field name="growth_potential">high</field>
            <field name="market_maturity">developing</field>
            <field name="notes">South America has emerging economies with significant growth opportunities.</field>
        </record>

        <record id="continent_oceania" model="financial.planning.continent">
            <field name="name">Oceania</field>
            <field name="code">OC</field>
            <field name="population">45.0</field>
            <field name="area_km2">8600000</field>
            <field name="growth_potential">medium</field>
            <field name="market_maturity">developed</field>
            <field name="notes">Oceania includes developed island nations with stable economies.</field>
        </record>

        <record id="continent_antarctica" model="financial.planning.continent">
            <field name="name">Antarctica</field>
            <field name="code">AN</field>
            <field name="population">0.0</field>
            <field name="area_km2">14200000</field>
            <field name="growth_potential">low</field>
            <field name="market_maturity">emerging</field>
            <field name="notes">Antarctica is primarily used for scientific research with no permanent population.</field>
            <field name="active" eval="False"/>
        </record>

    </data>
</odoo>
