from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
import re
from core.models import BaseModel, Company, Partner, Currency

class AccountGroup(BaseModel):
    """Account Group model - equivalent to account.group in Odoo"""
    name = models.CharField(max_length=255)
    code_prefix_start = models.CharField(max_length=20, blank=True)
    code_prefix_end = models.CharField(max_length=20, blank=True)
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    class Meta:
        # Note: Length constraint will be handled in clean() method for cross-database compatibility
        pass

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()
        # Validate that prefix start and end have the same length
        if self.code_prefix_start and self.code_prefix_end:
            if len(self.code_prefix_start) != len(self.code_prefix_end):
                raise ValidationError("The length of the starting and ending code prefix must be the same")

class AccountAccount(BaseModel):
    """Account model - equivalent to account.account in Odoo"""

    ACCOUNT_TYPE_CHOICES = [
        ('asset_receivable', 'Receivable'),
        ('asset_cash', 'Bank and Cash'),
        ('asset_current', 'Current Assets'),
        ('asset_non_current', 'Non-current Assets'),
        ('asset_prepayments', 'Prepayments'),
        ('asset_fixed', 'Fixed Assets'),
        ('liability_payable', 'Payable'),
        ('liability_credit_card', 'Credit Card'),
        ('liability_current', 'Current Liabilities'),
        ('liability_non_current', 'Non-current Liabilities'),
        ('equity', 'Equity'),
        ('equity_unaffected', 'Current Year Earnings'),
        ('income', 'Income'),
        ('income_other', 'Other Income'),
        ('expense', 'Expenses'),
        ('expense_depreciation', 'Depreciation'),
        ('expense_direct_cost', 'Cost of Revenue'),
        ('off_balance', 'Off-Balance Sheet'),
    ]

    name = models.CharField(max_length=255, db_index=True)
    code = models.CharField(max_length=64, db_index=True)
    account_type = models.CharField(max_length=50, choices=ACCOUNT_TYPE_CHOICES)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Reconciliation and control
    reconcile = models.BooleanField(default=False, help_text="Allow reconciliation of journal items")
    deprecated = models.BooleanField(default=False)

    # Grouping
    group_id = models.ForeignKey(AccountGroup, on_delete=models.SET_NULL, null=True, blank=True)

    # Tax configuration
    tax_ids = models.ManyToManyField('AccountTax', blank=True, help_text="Default taxes for this account")

    # Notes
    note = models.TextField(blank=True, help_text="Internal notes")

    class Meta:
        unique_together = [['code', 'company_id']]
        indexes = [
            models.Index(fields=['code', 'company_id']),
            models.Index(fields=['account_type']),
        ]

    def __str__(self):
        return f"{self.code} {self.name}"

    def clean(self):
        super().clean()

        # Validate account code format (alphanumeric and dots only)
        if not re.match(r'^[a-zA-Z0-9.]+$', self.code):
            raise ValidationError("Account code can only contain alphanumeric characters and dots.")

        # Receivable/Payable accounts must be reconcilable
        if self.account_type in ('asset_receivable', 'liability_payable') and not self.reconcile:
            raise ValidationError(f"Receivable/Payable account {self.code} must be reconcilable.")

        # Off-balance accounts cannot be reconcilable or have taxes
        if self.account_type == 'off_balance':
            if self.reconcile:
                raise ValidationError("Off-Balance account cannot be reconcilable.")

class AccountJournal(BaseModel):
    """Journal model - equivalent to account.journal in Odoo"""

    JOURNAL_TYPE_CHOICES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ]

    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10)
    type = models.CharField(max_length=20, choices=JOURNAL_TYPE_CHOICES)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Default accounts
    default_account_id = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                         related_name='journal_default_account')
    suspense_account_id = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                          related_name='journal_suspense_account', null=True, blank=True)

    # Control
    account_control_ids = models.ManyToManyField(AccountAccount, blank=True,
                                               help_text="Allowed accounts for this journal")

    # Sequence and numbering
    sequence = models.IntegerField(default=10, help_text="Used to order journals")

    # Security
    restrict_mode_hash_table = models.BooleanField(default=True,
                                                 help_text="Lock posted entries with hash")

    class Meta:
        unique_together = [['code', 'company_id']]

    def __str__(self):
        return f"{self.code} - {self.name}"

class AccountTax(BaseModel):
    """Tax model - equivalent to account.tax in Odoo"""

    TAX_TYPE_CHOICES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('none', 'None'),
    ]

    TAX_SCOPE_CHOICES = [
        ('service', 'Services'),
        ('consu', 'Goods'),
    ]

    AMOUNT_TYPE_CHOICES = [
        ('group', 'Group of Taxes'),
        ('fixed', 'Fixed'),
        ('percent', 'Percentage of Price'),
        ('division', 'Percentage of Price Tax Included'),
    ]

    name = models.CharField(max_length=255)
    type_tax_use = models.CharField(max_length=20, choices=TAX_TYPE_CHOICES, default='sale')
    tax_scope = models.CharField(max_length=20, choices=TAX_SCOPE_CHOICES, default='consu')
    amount_type = models.CharField(max_length=20, choices=AMOUNT_TYPE_CHOICES, default='percent')
    amount = models.DecimalField(max_digits=16, decimal_places=4, default=0.0)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Accounts for tax computation
    invoice_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine',
                                                        related_name='invoice_tax_ids', blank=True)
    refund_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine',
                                                       related_name='refund_tax_ids', blank=True)

    # Tax group and sequence
    sequence = models.IntegerField(default=1)

    class Meta:
        unique_together = [['name', 'type_tax_use', 'tax_scope', 'company_id']]

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()
        # Tax names must be unique per company for the same type and scope
        existing = AccountTax.objects.filter(
            name=self.name,
            type_tax_use=self.type_tax_use,
            tax_scope=self.tax_scope,
            company_id=self.company_id
        ).exclude(pk=self.pk)

        if existing.exists():
            raise ValidationError(f"Tax name '{self.name}' must be unique per company for the same type and scope.")

class AccountTaxRepartitionLine(BaseModel):
    """Tax Repartition Line model - equivalent to account.tax.repartition.line in Odoo"""

    REPARTITION_TYPE_CHOICES = [
        ('base', 'Base'),
        ('tax', 'Tax'),
    ]

    factor_percent = models.DecimalField(max_digits=16, decimal_places=4, default=100.0)
    repartition_type = models.CharField(max_length=10, choices=REPARTITION_TYPE_CHOICES, default='tax')
    account_id = models.ForeignKey(AccountAccount, on_delete=models.PROTECT, null=True, blank=True)
    sequence = models.IntegerField(default=1)

    def __str__(self):
        return f"{self.repartition_type} - {self.factor_percent}%"

class AccountMove(BaseModel):
    """Journal Entry model - equivalent to account.move in Odoo"""

    MOVE_TYPE_CHOICES = [
        ('entry', 'Journal Entry'),
        ('out_invoice', 'Customer Invoice'),
        ('out_refund', 'Customer Credit Note'),
        ('in_invoice', 'Vendor Bill'),
        ('in_refund', 'Vendor Credit Note'),
        ('out_receipt', 'Sales Receipt'),
        ('in_receipt', 'Purchase Receipt'),
    ]

    STATE_CHOICES = [
        ('draft', 'Unposted'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    PAYMENT_STATE_CHOICES = [
        ('not_paid', 'Not Paid'),
        ('in_payment', 'In Payment'),
        ('paid', 'Paid'),
        ('partial', 'Partially Paid'),
        ('reversed', 'Reversed'),
        ('invoicing_legacy', 'Invoicing App Legacy'),
    ]

    # Basic fields
    name = models.CharField(max_length=255, blank=True, help_text="Journal Entry Number")
    ref = models.CharField(max_length=255, blank=True, help_text="Reference")
    date = models.DateField(default=timezone.now)

    # Type and state
    move_type = models.CharField(max_length=20, choices=MOVE_TYPE_CHOICES, default='entry')
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')

    # Journal and company
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.PROTECT)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Partner information (for invoices)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    # Invoice specific fields
    invoice_date = models.DateField(null=True, blank=True)
    invoice_date_due = models.DateField(null=True, blank=True)
    payment_reference = models.CharField(max_length=255, blank=True)
    payment_state = models.CharField(max_length=20, choices=PAYMENT_STATE_CHOICES, default='not_paid')

    # Amounts (computed from lines)
    amount_untaxed = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_residual = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Control fields
    posted_before = models.BooleanField(default=False)
    to_check = models.BooleanField(default=False)

    # Hash and security
    inalterable_hash = models.CharField(max_length=255, blank=True)
    secure_sequence_number = models.IntegerField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['date', 'name']),
            models.Index(fields=['state']),
            models.Index(fields=['move_type']),
            models.Index(fields=['partner_id']),
            models.Index(fields=['journal_id']),
        ]

    def __str__(self):
        return self.name or f"Draft Move {self.id}"

    def clean(self):
        super().clean()

        # Cannot create move in posted state
        if self.state == 'posted' and not self.pk:
            raise ValidationError("Cannot create a move already in posted state. Create draft and post after.")

        # Journal type validation
        valid_types = self._get_valid_journal_types()
        if self.journal_id and self.journal_id.type not in valid_types:
            raise ValidationError(f"Journal type {self.journal_id.type} is not valid for move type {self.move_type}")

    def _get_valid_journal_types(self):
        """Get valid journal types for this move type"""
        if self.move_type in ('out_invoice', 'out_refund', 'out_receipt'):
            return ['sale']
        elif self.move_type in ('in_invoice', 'in_refund', 'in_receipt'):
            return ['purchase']
        else:
            return ['general', 'bank', 'cash']

    def is_invoice(self, include_receipts=False):
        """Check if this move is an invoice"""
        invoice_types = ['out_invoice', 'in_invoice', 'out_refund', 'in_refund']
        if include_receipts:
            invoice_types.extend(['out_receipt', 'in_receipt'])
        return self.move_type in invoice_types

    def is_sale_document(self, include_receipts=False):
        """Check if this is a sales document"""
        sale_types = ['out_invoice', 'out_refund']
        if include_receipts:
            sale_types.append('out_receipt')
        return self.move_type in sale_types

    def is_purchase_document(self, include_receipts=False):
        """Check if this is a purchase document"""
        purchase_types = ['in_invoice', 'in_refund']
        if include_receipts:
            purchase_types.append('in_receipt')
        return self.move_type in purchase_types

    def action_post(self):
        """Post the journal entry"""
        if self.state != 'draft':
            raise ValidationError("Only draft moves can be posted.")

        # Validate that move is balanced
        if not self._check_balanced():
            raise ValidationError("Journal entry must be balanced (total debits = total credits).")

        # Update state and posted_before flag
        self.state = 'posted'
        self.posted_before = True

        # Generate sequence number if needed
        if not self.name:
            self.name = self._get_sequence_number()

        self.save()

        # Update payment state for invoices
        if self.is_invoice():
            self._compute_payment_state()

    def _check_balanced(self):
        """Check if the move is balanced (debits = credits)"""
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())
        return abs(total_debit - total_credit) < 0.01  # Allow small rounding differences

    def _get_sequence_number(self):
        """Generate sequence number for the move"""
        # Simple sequence generation - in production, this would be more sophisticated
        last_move = AccountMove.objects.filter(
            journal_id=self.journal_id,
            date__year=self.date.year
        ).order_by('-name').first()

        if last_move and last_move.name:
            try:
                last_num = int(last_move.name.split('/')[-1])
                return f"{self.journal_id.code}/{self.date.year}/{last_num + 1:04d}"
            except (ValueError, IndexError):
                pass

        return f"{self.journal_id.code}/{self.date.year}/0001"

    def _compute_payment_state(self):
        """Compute payment state for invoices"""
        if not self.is_invoice():
            self.payment_state = 'not_paid'
            return

        if abs(self.amount_residual) < 0.01:
            self.payment_state = 'paid'
        elif abs(self.amount_residual - self.amount_total) < 0.01:
            self.payment_state = 'not_paid'
        else:
            self.payment_state = 'partial'

    def _compute_amounts(self):
        """Compute total amounts from move lines"""
        lines = self.line_ids.all()

        # For invoices, compute based on invoice lines (excluding tax lines)
        if self.is_invoice():
            invoice_lines = lines.filter(display_type__isnull=True, tax_line_id__isnull=True)
            tax_lines = lines.filter(tax_line_id__isnull=False)

            if self.is_sale_document():
                self.amount_untaxed = sum(line.credit for line in invoice_lines)
                self.amount_tax = sum(line.credit for line in tax_lines)
            else:
                self.amount_untaxed = sum(line.debit for line in invoice_lines)
                self.amount_tax = sum(line.debit for line in tax_lines)

            self.amount_total = self.amount_untaxed + self.amount_tax

            # Compute residual (amount still to be paid)
            if self.is_sale_document():
                receivable_lines = lines.filter(account_id__account_type='asset_receivable')
                self.amount_residual = sum(line.balance for line in receivable_lines)
            else:
                payable_lines = lines.filter(account_id__account_type='liability_payable')
                self.amount_residual = -sum(line.balance for line in payable_lines)
        else:
            # For journal entries, just show total debits/credits
            self.amount_total = sum(line.debit for line in lines)
            self.amount_untaxed = self.amount_total
            self.amount_tax = 0.0
            self.amount_residual = 0.0

    def button_draft(self):
        """Reset move to draft state"""
        if not self.posted_before:
            raise ValidationError("Only moves that have been posted before can be reset to draft.")

        # Check if move can be unposted
        if not self._can_be_unposted():
            raise ValidationError("This move cannot be reset to draft due to business constraints.")

        self.state = 'draft'
        self.save()

    def _can_be_unposted(self):
        """Check if move can be unposted"""
        # Check for reconciled lines
        if self.line_ids.filter(reconciled=True).exists():
            return False

        # Check for locked period
        if self._is_in_locked_period():
            return False

        return True

    def _is_in_locked_period(self):
        """Check if move is in a locked period"""
        # Simplified - in real implementation would check fiscal year locks
        return False

    def button_cancel(self):
        """Cancel the move"""
        if self.state == 'posted':
            raise ValidationError("Posted moves cannot be cancelled. Reset to draft first.")

        self.state = 'cancel'
        self.save()

    def delete(self):
        """Override delete to add business rules"""
        # Check if any move is posted
        if self.state == 'posted':
            raise ValidationError("Posted moves cannot be deleted.")

        # Check for reconciled lines
        if self.line_ids.filter(reconciled=True).exists():
            raise ValidationError("Moves with reconciled lines cannot be deleted.")

        super().delete()

    def _reverse_moves(self, default_values_list=None, cancel=False):
        """Create reverse moves for this move"""
        if self.state != 'posted':
            raise ValidationError("Only posted moves can be reversed.")

        # Create reverse move
        reverse_vals = {
            'ref': f"Reversal of: {self.name}",
            'date': timezone.now().date(),
            'journal_id': self.journal_id,
            'move_type': 'entry',
            'company_id': self.company_id,
            'currency_id': self.currency_id,
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

        if default_values_list:
            reverse_vals.update(default_values_list[0] if default_values_list else {})

        reverse_move = AccountMove.objects.create(**reverse_vals)

        # Create reverse lines
        for line in self.line_ids.all():
            if not line.display_type:  # Skip section/note lines
                AccountMoveLine.objects.create(
                    move_id=reverse_move,
                    account_id=line.account_id,
                    name=f"Reversal: {line.name}",
                    debit=line.credit,  # Swap debit/credit
                    credit=line.debit,
                    amount_currency=-line.amount_currency if line.amount_currency else 0,
                    currency_id=line.currency_id,
                    partner_id=line.partner_id,
                    company_id=line.company_id,
                    create_uid=self.create_uid,
                    write_uid=self.write_uid,
                )

        # Post the reverse move
        reverse_move.action_post()

        # Reconcile original and reverse moves if cancel=True
        if cancel:
            self._reconcile_with_reverse(reverse_move)

        return reverse_move

    def _reconcile_with_reverse(self, reverse_move):
        """Reconcile original move with its reverse"""
        # Group lines by account for reconciliation
        accounts = set()
        for line in self.line_ids.all():
            if line.account_id.reconcile and not line.display_type:
                accounts.add(line.account_id)

        for account in accounts:
            original_lines = self.line_ids.filter(account_id=account, display_type__isnull=True)
            reverse_lines = reverse_move.line_ids.filter(account_id=account, display_type__isnull=True)

            # Simple reconciliation - mark as reconciled
            for line in original_lines:
                line.reconciled = True
                line.save()
            for line in reverse_lines:
                line.reconciled = True
                line.save()

    def _get_integrity_hash_fields(self):
        """Get fields that affect integrity hash"""
        return ['name', 'date', 'journal_id', 'company_id']

    def _check_hash_integrity(self):
        """Check hash integrity for posted moves"""
        # Simplified implementation
        return True

class AccountMoveLine(BaseModel):
    """Journal Item model - equivalent to account.move.line in Odoo"""

    # Parent move
    move_id = models.ForeignKey(AccountMove, on_delete=models.CASCADE, related_name='line_ids')

    # Account and partner
    account_id = models.ForeignKey(AccountAccount, on_delete=models.PROTECT)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    # Amounts
    debit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    credit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    balance = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Currency
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    amount_currency = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Description and reference
    name = models.CharField(max_length=255, help_text="Label")
    ref = models.CharField(max_length=255, blank=True, help_text="Reference")

    # Date (inherited from move but can be different for some entries)
    date = models.DateField()
    date_maturity = models.DateField(null=True, blank=True, help_text="Due date")

    # Tax information
    tax_ids = models.ManyToManyField(AccountTax, blank=True, related_name='move_line_tax_ids')
    tax_line_id = models.ForeignKey(AccountTax, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='tax_move_line_ids', help_text="Tax this line is part of")
    tax_base_amount = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Reconciliation
    reconciled = models.BooleanField(default=False)
    full_reconcile_id = models.ForeignKey('AccountFullReconcile', on_delete=models.SET_NULL,
                                        null=True, blank=True)

    # Matching and reconciliation
    matching_number = models.CharField(max_length=255, blank=True)

    # Product information (for invoice lines) - will be linked later when inventory app is created
    # product_id = models.ForeignKey('inventory.Product', on_delete=models.SET_NULL,
    #                              null=True, blank=True)
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=1.0)
    price_unit = models.DecimalField(max_digits=20, decimal_places=4, default=0.0)

    # Discount
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0.0)

    # Sequence for ordering
    sequence = models.IntegerField(default=10)

    # Display type for section and note lines
    DISPLAY_TYPE_CHOICES = [
        ('line_section', 'Section'),
        ('line_note', 'Note'),
    ]
    display_type = models.CharField(max_length=20, choices=DISPLAY_TYPE_CHOICES, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['move_id']),
            models.Index(fields=['account_id']),
            models.Index(fields=['partner_id']),
            models.Index(fields=['date']),
            models.Index(fields=['reconciled']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(debit__gte=0),
                name='check_debit_positive'
            ),
            models.CheckConstraint(
                check=models.Q(credit__gte=0),
                name='check_credit_positive'
            ),
        ]

    def __str__(self):
        return f"{self.move_id.name} - {self.account_id.code} - {self.name}"

    def save(self, *args, **kwargs):
        # Compute balance
        self.balance = Decimal(str(self.debit)) - Decimal(str(self.credit))

        # Set date from move if not provided
        if not self.date and self.move_id:
            self.date = self.move_id.date

        super().save(*args, **kwargs)

        # Update move totals
        if self.move_id:
            self.move_id._compute_amounts()

    def clean(self):
        super().clean()

        # Validate that debit and credit are not both set
        if self.debit > 0 and self.credit > 0:
            raise ValidationError("A journal item cannot have both debit and credit amounts.")

        # Validate account and journal consistency
        if (self.move_id and self.move_id.journal_id.account_control_ids.exists() and
            self.account_id not in self.move_id.journal_id.account_control_ids.all()):
            raise ValidationError(f"Account {self.account_id.code} is not allowed in journal {self.move_id.journal_id.code}")

    def reconcile(self, writeoff_acc_id=None, writeoff_journal_id=None):
        """Reconcile journal items"""
        if not self:
            return

        # Check that all lines are from reconcilable accounts
        non_reconcilable = [line for line in self if not line.account_id.reconcile]
        if non_reconcilable:
            raise ValidationError(f"Account {non_reconcilable[0].account_id.code} is not reconcilable.")

        # Check that all lines are from the same account
        accounts = set(line.account_id for line in self)
        if len(accounts) > 1:
            raise ValidationError("Cannot reconcile lines from different accounts.")

        # Check that all lines are posted
        unposted = [line for line in self if line.move_id.state != 'posted']
        if unposted:
            raise ValidationError("Cannot reconcile unposted journal items.")

        # Check balance
        total_balance = sum(line.balance for line in self)
        if abs(total_balance) > 0.01:  # Allow small rounding differences
            if not writeoff_acc_id:
                raise ValidationError(f"Cannot reconcile unbalanced items. Difference: {total_balance}")
            else:
                # Create writeoff entry
                self._create_writeoff_entry(writeoff_acc_id, writeoff_journal_id, total_balance)

        # Mark lines as reconciled
        for line in self:
            line.reconciled = True
            line.save()

    def _create_writeoff_entry(self, writeoff_acc_id, writeoff_journal_id, amount):
        """Create writeoff entry for reconciliation difference"""
        # This would create a journal entry for the writeoff amount
        # Simplified implementation
        pass

    def remove_move_reconcile(self):
        """Remove reconciliation from journal items"""
        for line in self:
            if line.reconciled:
                line.reconciled = False
                line.save()

    def _check_constrains_account_id_journal_id(self):
        """Check account and journal constraints"""
        for line in self:
            if line.display_type:
                continue

            account = line.account_id

            # Check deprecated accounts
            if account.deprecated:
                raise ValidationError(f"Account {account.code} ({account.name}) is deprecated.")

            # Check account type consistency with move type
            if line.move_id.is_sale_document():
                if account.account_type == 'liability_payable':
                    raise ValidationError(f"Account {account.code} is payable but used in sale operation.")
            elif line.move_id.is_purchase_document():
                if account.account_type == 'asset_receivable':
                    raise ValidationError(f"Account {account.code} is receivable but used in purchase operation.")

    def _check_reconcile_validity(self):
        """Check if lines can be reconciled"""
        # All lines must be from reconcilable accounts
        if not all(line.account_id.reconcile for line in self):
            raise ValidationError("All lines must be from reconcilable accounts.")

        # All lines must be posted
        if not all(line.move_id.state == 'posted' for line in self):
            raise ValidationError("All lines must be from posted moves.")

        # All lines must be from same account
        accounts = set(line.account_id for line in self)
        if len(accounts) > 1:
            raise ValidationError("All lines must be from the same account.")

    def _update_check(self):
        """Check if line can be updated"""
        if self.move_id.state == 'posted' and self.reconciled:
            raise ValidationError("Cannot modify reconciled lines in posted moves.")

        if self.move_id.state == 'posted' and self.move_id._is_in_locked_period():
            raise ValidationError("Cannot modify lines in locked period.")

    def delete(self):
        """Override delete to add business rules"""
        if self.move_id.state == 'posted':
            raise ValidationError("Cannot delete lines from posted moves.")

        if self.reconciled:
            raise ValidationError("Cannot delete reconciled lines.")

        super().delete()

class AccountFullReconcile(BaseModel):
    """Full Reconcile model - equivalent to account.full.reconcile in Odoo"""

    name = models.CharField(max_length=255)
    reconciled_line_ids = models.ManyToManyField(AccountMoveLine, related_name='full_reconcile_ids')

    def __str__(self):
        return self.name

class AccountPartialReconcile(BaseModel):
    """Partial Reconcile model - equivalent to account.partial.reconcile in Odoo"""

    debit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE,
                                    related_name='matched_debit_ids')
    credit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE,
                                     related_name='matched_credit_ids')
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    amount_currency = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    full_reconcile_id = models.ForeignKey(AccountFullReconcile, on_delete=models.CASCADE,
                                        null=True, blank=True)

    def __str__(self):
        return f"Reconcile {self.amount} between {self.debit_move_id} and {self.credit_move_id}"
