# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* report_xlsx_helper
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-10-15 16:37+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: none\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"%(__name__)s, _write_line : programming error detected while processing "
"col_specs_section %(col_specs_section)s, column %(col)s"
msgstr ""
"%(__name__)s, _write_line : error de programación detectado al procesar "
"col_specs_section %(col_specs_section)s, columna %(col)s"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/models/ir_actions_report.py:0
#, python-format
msgid "%s model was not found"
msgstr "%s modelo no fue encontrado"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ", cellvalue %s"
msgstr ", valor de celda %s"

#. module: report_xlsx_helper
#: model:ir.model,name:report_xlsx_helper.model_report_report_xlsx_abstract
msgid "Abstract XLSX Report"
msgstr "Informe XLSX en abstracto"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"Excel Sheet name '%(name)s' contains unsupported special characters: "
"'%(special_chars)s'."
msgstr ""
"Error de programación:\n"
"\n"
"El nombre de la hoja Excel '%(name)s' contiene caracteres especiales no "
"admitidos: '%(special_chars)s'."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"Excel Sheet name '%(name)s' should not exceed %(max_chars)s characters."
msgstr ""
"Error de programación:\n"
"\n"
"El nombre de la hoja Excel '%(name)s' no debe exceder %(max_chars)s "
"caracteres."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"The '%s' column is not defined in the worksheet column specifications."
msgstr ""
"Error de programación:\n"
"\n"
"La columna '%s' no está definida en las especificaciones de columna de la "
"hoja de cálculo."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"The '%s' column is not defined the worksheet column specifications."
msgstr ""
"Error de programación:\n"
"\n"
"La columna '%s' no está definida en las especificaciones de columna de la "
"hoja de cálculo."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"The 'title' parameter is mandatory when calling the '_write_ws_title' method."
msgstr ""
"Error de programación:\n"
"\n"
"El parámetro 'title' es obligatorio al llamar al método '_write_ws_title'."

#. module: report_xlsx_helper
#: model:ir.model,name:report_xlsx_helper.model_ir_actions_report
msgid "Report Action"
msgstr "Informar Acción"

#. module: report_xlsx_helper
#: model:ir.model,name:report_xlsx_helper.model_report_report_xlsx_helper_test_partner_xlsx
msgid "Test Partner XLSX Report"
msgstr "Informe XLSX del socio de prueba"

#, python-format
#~ msgid ""
#~ "%s, _write_line : programming error detected while processing "
#~ "col_specs_section %s, column %s"
#~ msgstr ""
#~ "%s, _write_line : detectado error procesando col_specs_section %s, column "
#~ "%s"

#~ msgid "Display Name"
#~ msgstr "Mostrar Nombre"

#~ msgid "ID"
#~ msgstr "ID (identificación)"

#~ msgid "Last Modified on"
#~ msgstr "Última Modificación el"

#, python-format
#~ msgid ""
#~ "Programming Error:\n"
#~ "\n"
#~ "Excel Sheet name '%s' contains unsupported special characters: '%s'."
#~ msgstr ""
#~ "Error de programación:\n"
#~ "\n"
#~ "El nombre de la hoja Excel '%s' contiene caracteres especiales no "
#~ "admitidos: '%s'."

#, python-format
#~ msgid ""
#~ "Programming Error:\n"
#~ "\n"
#~ "Excel Sheet name '%s' should not exceed %s characters."
#~ msgstr ""
#~ "Error de programación:\n"
#~ "\n"
#~ "El nombre de la hoja Excel '%s' no debe superar los %s caracteres."
