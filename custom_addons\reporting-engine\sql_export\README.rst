==========
SQL Export
==========

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:2cf03bbe1b27b9015fc6d1d57ce5b9b14951c00861e9d8518343c66a2c0b14cc
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Freporting--engine-lightgray.png?logo=github
    :target: https://github.com/OCA/reporting-engine/tree/17.0/sql_export
    :alt: OCA/reporting-engine
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/reporting-engine-17-0/reporting-engine-17-0-sql_export
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/reporting-engine&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

Allow to export data in csv files FROM sql requests. There are some
restrictions in the sql query, you can only read datas. No update,
deletion or creation are possible. A new sub menu named Sql Export is
available in the Dashboard main menu.

**Table of contents**

.. contents::
   :local:

Usage
=====

Dashboards > Sql Export

**Specific use with parameters**

-  %(company_id)s allows to set in the query the company id of the user
-  %(user_id)s allows to set in the query the user id
-  for any created property, you can use it with %(Property String)s
   syntax

Known issues / Roadmap
======================

-  Some words are prohibited and can't be used is the query in anyways,
   even in a select query:

   -  delete
   -  drop
   -  insert
   -  alter
   -  truncate
   -  execute
   -  create
   -  update

See sql_request_abstract module to fix this issue.

-  checking SQL request by execution and rollback is disabled in this
   module since variables features has been introduced. This can be
   fixed by overloading \_prepare_request_check_execution() function.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/reporting-engine/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/reporting-engine/issues/new?body=module:%20sql_export%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Akretion
* GRAP

Contributors
------------

-  `Akretion <https://www.akretion.com>`__:

   -  Florian da Costa <<EMAIL>>
   -  Mourad EL HADJ MIMOUNE <<EMAIL>>
   -  Benoît GUILLOT <<EMAIL>>

-  `Eficent <https://www.eficent.com>`__:

   -  Aaron Henriquez <<EMAIL>>

-  `Codeforward <https://www.codeforward.nl>`__:

   -  Sander Lienaerts <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-legalsylvain| image:: https://github.com/legalsylvain.png?size=40px
    :target: https://github.com/legalsylvain
    :alt: legalsylvain

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-legalsylvain| 

This module is part of the `OCA/reporting-engine <https://github.com/OCA/reporting-engine/tree/17.0/sql_export>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
