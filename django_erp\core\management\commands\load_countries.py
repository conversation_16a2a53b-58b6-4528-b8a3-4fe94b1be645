"""
Management command to load countries from Odoo data
"""

import json
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from core.models import Country

User = get_user_model()


class Command(BaseCommand):
    help = 'Load countries from Odoo data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing countries',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading countries from Odoo data...'))
        
        # Load countries from extracted JSON file
        json_file = 'countries_data.json'
        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(
                    f'Country data file {json_file} not found. '
                    'Please run: python extract_odoo_data.py first'
                )
            )
            return
        
        with open(json_file, 'r') as f:
            countries_data = json.load(f)

        created_count = 0
        updated_count = 0
        
        # Get or create admin user for create_uid/write_uid
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )
        
        with transaction.atomic():
            for country_data in countries_data:
                code = country_data['code']
                
                # Extract name from nested structure
                if isinstance(country_data['name'], dict):
                    name = country_data['name'].get('en_US', code)
                else:
                    name = country_data['name']
                
                # Prepare clean data for Django model
                clean_data = {
                    'name': name,
                    'code': code,
                    'phone_code': country_data.get('phone_code', 0),
                    'create_uid': admin_user,
                    'write_uid': admin_user,
                }
                
                try:
                    country = Country.objects.get(code=code)
                    if options['update']:
                        # Update existing country
                        for field, value in clean_data.items():
                            if field not in ['create_uid']:  # Don't update create_uid
                                setattr(country, field, value)
                        country.save()
                        updated_count += 1
                        self.stdout.write(f'Updated country: {name} ({code})')
                    else:
                        self.stdout.write(f'Country already exists: {name} ({code})')
                        
                except Country.DoesNotExist:
                    # Create new country
                    country = Country.objects.create(**clean_data)
                    created_count += 1
                    self.stdout.write(f'Created country: {name} ({code})')

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCountry loading completed!\n'
                f'Created: {created_count} countries\n'
                f'Updated: {updated_count} countries\n'
                f'Total countries in system: {Country.objects.count()}'
            )
        )
        
        # Show sample loaded countries
        self.stdout.write('\nSample loaded countries:')
        for country in Country.objects.all().order_by('name')[:10]:
            self.stdout.write(f'  {country.code} - {country.name} (+{country.phone_code})')
