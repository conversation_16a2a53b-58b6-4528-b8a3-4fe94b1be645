<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Sample Financial Planning Data -->
        
        <!-- Sample Major Cities -->
        <record id="city_new_york" model="financial.planning.city">
            <field name="name">New York City</field>
            <field name="country_id" ref="country_usa"/>
            <field name="population">8.4</field>
            <field name="area_km2">778.2</field>
            <field name="latitude">40.7128</field>
            <field name="longitude">-74.0060</field>
            <field name="is_financial_center" eval="True"/>
            <field name="is_tech_hub" eval="True"/>
            <field name="market_potential">very_high</field>
            <field name="annual_population_growth_rate">0.8</field>
            <field name="annual_economic_growth_rate">2.5</field>
        </record>

        <record id="city_london" model="financial.planning.city">
            <field name="name">London</field>
            <field name="country_id" ref="country_uk"/>
            <field name="population">9.0</field>
            <field name="area_km2">1572</field>
            <field name="latitude">51.5074</field>
            <field name="longitude">-0.1278</field>
            <field name="is_capital" eval="True"/>
            <field name="is_financial_center" eval="True"/>
            <field name="market_potential">very_high</field>
            <field name="annual_population_growth_rate">1.2</field>
            <field name="annual_economic_growth_rate">2.1</field>
        </record>

        <record id="city_tokyo" model="financial.planning.city">
            <field name="name">Tokyo</field>
            <field name="country_id" ref="country_japan"/>
            <field name="population">13.9</field>
            <field name="area_km2">2194</field>
            <field name="latitude">35.6762</field>
            <field name="longitude">139.6503</field>
            <field name="is_capital" eval="True"/>
            <field name="is_financial_center" eval="True"/>
            <field name="is_tech_hub" eval="True"/>
            <field name="market_potential">very_high</field>
            <field name="annual_population_growth_rate">0.2</field>
            <field name="annual_economic_growth_rate">1.8</field>
        </record>

        <record id="city_mumbai" model="financial.planning.city">
            <field name="name">Mumbai</field>
            <field name="country_id" ref="country_india"/>
            <field name="population">20.4</field>
            <field name="area_km2">603</field>
            <field name="latitude">19.0760</field>
            <field name="longitude">72.8777</field>
            <field name="is_financial_center" eval="True"/>
            <field name="market_potential">very_high</field>
            <field name="annual_population_growth_rate">2.1</field>
            <field name="annual_economic_growth_rate">7.2</field>
        </record>

        <record id="city_shanghai" model="financial.planning.city">
            <field name="name">Shanghai</field>
            <field name="country_id" ref="country_china"/>
            <field name="population">24.3</field>
            <field name="area_km2">6341</field>
            <field name="latitude">31.2304</field>
            <field name="longitude">121.4737</field>
            <field name="is_financial_center" eval="True"/>
            <field name="is_tech_hub" eval="True"/>
            <field name="market_potential">very_high</field>
            <field name="annual_population_growth_rate">1.3</field>
            <field name="annual_economic_growth_rate">6.8</field>
        </record>

        <record id="city_sao_paulo" model="financial.planning.city">
            <field name="name">São Paulo</field>
            <field name="country_id" ref="country_brazil"/>
            <field name="population">12.3</field>
            <field name="area_km2">1521</field>
            <field name="latitude">-23.5505</field>
            <field name="longitude">-46.6333</field>
            <field name="is_financial_center" eval="True"/>
            <field name="market_potential">high</field>
            <field name="annual_population_growth_rate">0.9</field>
            <field name="annual_economic_growth_rate">2.8</field>
        </record>

        <record id="city_lagos" model="financial.planning.city">
            <field name="name">Lagos</field>
            <field name="country_id" ref="country_nigeria"/>
            <field name="population">15.4</field>
            <field name="area_km2">1171</field>
            <field name="latitude">6.5244</field>
            <field name="longitude">3.3792</field>
            <field name="is_financial_center" eval="True"/>
            <field name="market_potential">very_high</field>
            <field name="annual_population_growth_rate">3.2</field>
            <field name="annual_economic_growth_rate">4.5</field>
        </record>

        <!-- Sample Growth Rate Data -->
        <record id="growth_rate_usa_2024_01" model="financial.planning.growth.rate">
            <field name="country_id" ref="country_usa"/>
            <field name="year">2024</field>
            <field name="month">01</field>
            <field name="population_growth_rate">0.7</field>
            <field name="gdp_growth_rate">2.3</field>
            <field name="inflation_rate">2.1</field>
            <field name="unemployment_rate">3.7</field>
            <field name="data_source">official</field>
            <field name="data_quality">high</field>
        </record>

        <record id="growth_rate_china_2024_01" model="financial.planning.growth.rate">
            <field name="country_id" ref="country_china"/>
            <field name="year">2024</field>
            <field name="month">01</field>
            <field name="population_growth_rate">0.4</field>
            <field name="gdp_growth_rate">6.1</field>
            <field name="inflation_rate">2.9</field>
            <field name="unemployment_rate">3.6</field>
            <field name="data_source">official</field>
            <field name="data_quality">high</field>
        </record>

        <record id="growth_rate_india_2024_01" model="financial.planning.growth.rate">
            <field name="country_id" ref="country_india"/>
            <field name="year">2024</field>
            <field name="month">01</field>
            <field name="population_growth_rate">1.0</field>
            <field name="gdp_growth_rate">6.8</field>
            <field name="inflation_rate">4.8</field>
            <field name="unemployment_rate">7.4</field>
            <field name="data_source">official</field>
            <field name="data_quality">high</field>
        </record>

    </data>
</odoo>
