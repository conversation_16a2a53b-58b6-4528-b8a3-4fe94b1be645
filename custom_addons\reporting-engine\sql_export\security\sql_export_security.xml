<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">

    <record model="ir.rule" id="sql_export_restric_access_user_or_group">
        <field name="name">SQL Export users and groups rules</field>
        <field name="model_id" ref="model_sql_export" />
        <field eval="1" name="perm_read" />
        <field eval="0" name="perm_create" />
        <field eval="0" name="perm_write" />
        <field eval="0" name="perm_unlink" />
        <field
            name="domain_force"
        >['|', ('user_ids','=',user.id), ('group_ids','in', [x.id for x in user.groups_id])]</field>
    </record>

</odoo>
