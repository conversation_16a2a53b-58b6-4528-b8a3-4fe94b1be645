<?xml version="1.0" encoding="utf-8" ?>
<odoo>

    <record id="view_account_payment_method_form" model="ir.ui.view">
        <field name="name">account.payment.method</field>
        <field name="model">account.payment.method</field>
        <field name="arch" type="xml">
            <form string="Payment Methods" create="0" edit="0" delete="0">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="payment_type"/>

                        </group>
                        <group>
                            <field name="code"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_account_payment_method_tree" model="ir.ui.view">
        <field name="name">account.payment.method</field>
        <field name="model">account.payment.method</field>
        <field name="arch" type="xml">
            <list string="Payment Methods" create="0" edit="0" delete="0">
                <field name="name"/>
                <field name="payment_type"/>
            </list>
        </field>
    </record>

    <record id="view_account_payment_method_search" model="ir.ui.view">
        <field name="name">account.payment.method</field>
        <field name="model">account.payment.method</field>
        <field name="arch" type="xml">
            <search string="Payment Methods">
                <field name="name" string="Payment Method"/>
                <field name="payment_type"/>
                <group expand="0" string="Group By">
                    <filter name="payment_type" string="Payment Type" context="{'group_by':'payment_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_account_payment_method" model="ir.actions.act_window">
		<field name="name">Payment Methods</field>
		<field name="res_model">account.payment.method</field>
		<field name="view_mode">list,form</field>
	</record>

    <menuitem id="menu_account_payment_method"
              action="action_account_payment_method"
              parent="account.root_payment_menu"
              sequence="30"
              groups="base.group_no_one" />

</odoo>
