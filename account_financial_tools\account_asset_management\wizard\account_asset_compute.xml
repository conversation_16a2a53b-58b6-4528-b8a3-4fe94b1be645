<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="account_asset_compute_view_form" model="ir.ui.view">
        <field name="name">account.asset.compute</field>
        <field name="model">account.asset.compute</field>
        <field name="arch" type="xml">
            <form string="Compute Asset">
                <group>
                    <field
                        name="date_end"
                        options="{'no_create': True, 'no_open': True}"
                    />
                </group>
                <footer>
                    <button
                        string="Compute"
                        name="asset_compute"
                        type="object"
                        class="oe_highlight"
                    />
                    <button string="Cancel" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record id="account_asset_compute_view_form_result" model="ir.ui.view">
        <field name="name">account.asset.compute.result</field>
        <field name="model">account.asset.compute</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <form string="Compute Assets results">
                <separator colspan="4" string="Results :" />
                <field name="note" colspan="4" nolabel="1" width="850" height="400" />
                <footer>
                    <button
                        string="View Asset Moves"
                        name="view_asset_moves"
                        type="object"
                        class="oe_highlight"
                    />
                    <button string="Cancel" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record id="account_asset_compute_action" model="ir.actions.act_window">
        <field name="name">Compute Assets</field>
        <field name="res_model">account.asset.compute</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="account_asset_compute_view_form" />
        <field name="target">new</field>
    </record>
</odoo>
