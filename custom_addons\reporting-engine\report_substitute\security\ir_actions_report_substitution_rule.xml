<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2019 ACSONE SA/NV
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <record model="ir.model.access" id="action_report_substitution_rule_user_access">
        <field name="name">action.report.substitution.rule user access</field>
        <field name="model_id" ref="model_ir_actions_report_substitution_rule" />
        <field name="perm_read" eval="1" />
        <field name="perm_create" eval="0" />
        <field name="perm_write" eval="0" />
        <field name="perm_unlink" eval="0" />
    </record>
    <record model="ir.model.access" id="action_report_substitution_rule_manager_access">
        <field name="name">action.report.substitution.rule manager access</field>
        <field name="model_id" ref="model_ir_actions_report_substitution_rule" />
        <field name="group_id" ref="base.group_system" />
        <field name="perm_read" eval="1" />
        <field name="perm_create" eval="1" />
        <field name="perm_write" eval="1" />
        <field name="perm_unlink" eval="1" />
    </record>
</odoo>
