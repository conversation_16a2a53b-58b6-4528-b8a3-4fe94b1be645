====================
SQL Request Abstract
====================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:d9671a76ee25d212c63c7f1a747acea88c2778536f30430a3e619352852e9bc9
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Freporting--engine-lightgray.png?logo=github
    :target: https://github.com/OCA/reporting-engine/tree/17.0/sql_request_abstract
    :alt: OCA/reporting-engine
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/reporting-engine-17-0/reporting-engine-17-0-sql_request_abstract
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/reporting-engine&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module provides an abstract model to manage SQL Select requests on
database. It is not usefull for itself. You can see an exemple of
implementation in the 'sql_export' module. (same repository).

Implemented features
--------------------

-  Add some restrictions in the sql request:

   -  you can only read datas. No update, deletion or creation are
      possible.
   -  some tables are not allowed, because they could contains clear
      password or keys. For the time being ('ir_config_parameter').

-  The request can be in a 'draft' or a 'SQL Valid' status. To be valid,
   the request has to be cleaned, checked and tested. All of this
   operations can be disabled in the inherited modules.

-  This module two new groups:

   -  SQL Request / User : Can see all the sql requests by default and
      execute them, if they are valid.
   -  SQL Request / Manager : has full access on sql requests.

**Table of contents**

.. contents::
   :local:

Usage
=====

Inherit the model:

.. code:: python

   from odoo import models

   class MyModel(models.model)
       _name = 'my.model'
       _inherit = ['sql.request.mixin']

       _sql_request_groups_relation = 'my_model_groups_rel'

       _sql_request_users_relation = 'my_model_users_rel'

See implementations in the modules ``bi_sql_editor`` and ``sql_export``.
(same OCA/reporting-engine repository)

Development
===========

This module add the 'pgsql' mode syntax for the ace widget. (the ace
widget is used in odoo web module, but only with the xml and python
mode).

The file is a copy of the file present here
(https://github.com/ajaxorg/ace-builds/blob/v1.12.3/src/mode-pgsql.js
(Release 18 Oct 2022)

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/reporting-engine/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/reporting-engine/issues/new?body=module:%20sql_request_abstract%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* GRAP
* Akretion

Contributors
------------

-  Florian da Costa <<EMAIL>>
-  Sylvain LE GAL (https://twitter.com/legalsylvain)
-  Alfadil Tabar (<EMAIL>)
-  Helly kapatel <<EMAIL>>
-  Nguyen Minh Chien <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-legalsylvain| image:: https://github.com/legalsylvain.png?size=40px
    :target: https://github.com/legalsylvain
    :alt: legalsylvain

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-legalsylvain| 

This module is part of the `OCA/reporting-engine <https://github.com/OCA/reporting-engine/tree/17.0/sql_request_abstract>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
