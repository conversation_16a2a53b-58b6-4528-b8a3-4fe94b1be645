# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class AccountAccount(models.Model):
    _inherit = 'account.account'

    def get_budget_vs_actual_data(self, date_from, date_to, budget_id=None):
        """
        Get budget vs actual data for this account
        Returns dict with budget_amount, actual_amount, variance, variance_percent
        """
        self.ensure_one()
        
        # Get actual amounts from account move lines
        domain = [
            ('account_id', '=', self.id),
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('move_id.state', '=', 'posted')
        ]
        
        move_lines = self.env['account.move.line'].search(domain)
        actual_amount = sum(move_lines.mapped('balance'))
        
        # Get budget amount if budget module is available
        budget_amount = 0.0
        try:
            # Check if budget.lines model exists
            if 'budget.lines' in self.env:
                budget_domain = [
                    ('general_budget_id.account_ids', 'in', [self.id]),
                    ('date_from', '<=', date_to),
                    ('date_to', '>=', date_from)
                ]
                if budget_id:
                    budget_domain.append(('budget_id', '=', budget_id))
                    
                budget_lines = self.env['budget.lines'].search(budget_domain)
                budget_amount = sum(budget_lines.mapped('planned_amount'))
        except Exception:
            # If budget module is not available, budget_amount remains 0
            pass
        
        # Calculate variance
        variance = actual_amount - budget_amount
        variance_percent = (variance / budget_amount * 100) if budget_amount != 0 else 0.0
        
        return {
            'account_id': self.id,
            'account_code': self.code,
            'account_name': self.name,
            'budget_amount': budget_amount,
            'actual_amount': actual_amount,
            'variance': variance,
            'variance_percent': variance_percent,
            'date_from': date_from,
            'date_to': date_to
        }
