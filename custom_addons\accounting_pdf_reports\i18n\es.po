# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* accounting_pdf_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-13 20:51+0000\n"
"PO-Revision-Date: 2022-05-13 18:02-0400\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"Language: es\n"
"X-Generator: Poedit 2.3\n"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid ": General ledger"
msgstr ": Libro Mayor"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid ": Trial Balance"
msgstr ": Balanza de comprobación"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>No Adeudado</span>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Analytic Accounts:</strong>"
msgstr "<strong>Cuentas analíticas:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Company:</strong>"
msgstr "<strong>Compañía:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>Desde la fecha :</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>Hasta la fecha :</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>Mostrar Cuenta:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>Mostrar Cuenta</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Apuntes ordenados por:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Diario:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Journals:</strong>"
msgstr "<strong>Diarios:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>Del Socio:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "<strong>Duración del Período (días)</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>Compra</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Ordenado Por:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Fecha de Inicio:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Movimientos señalados:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Account"
msgstr "Cuenta"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr "Contabilidad. Informe de Saldo Balance de Comprobación"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_account_report
msgid "Account Common Account Report"
msgstr "Contabilidad. Informe de Cuenta Común"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr "Contabilidad. Informe de socio Común"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr "Contabilidad. Libro Mayor de Socios"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_print_journal
msgid "Account Print Journal"
msgstr "Contabilidad. Imprimir diario"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_financial_report
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr "Reporte de Cuenta"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__account_report_id
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_reports
msgid "Account Reports"
msgstr "Reportes de Cuenta"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Account Total"
msgstr "Cuenta Total"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_type
msgid "Account Type"
msgstr "Tipo de Cuenta"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_accounting_report
msgid "Accounting Report"
msgstr "Reporte de Contabilidad"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__account_ids
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__accounts
msgid "Accounts"
msgstr "Cuentas"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_balance_view
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_aged_partner_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_trial_balance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Saldos vencidos de empresa"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr "Informe de saldos vencidos de empresa"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_payable
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_payable
msgid "Aged Payable"
msgstr "Cuentas por pagar vencidas"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_receivable
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_receivable
msgid "Aged Receivable"
msgstr "Cuentas por cobrar vencidas"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__all
msgid "All"
msgstr "Todos"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Entries"
msgstr "Todos los asientos"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Posted Entries"
msgstr "Todos los asientos validados"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All accounts"
msgstr "Todas las cuentas"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "All accounts'"
msgstr "Todas las cuentas'"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Analytic Account"
msgstr "Cuenta analitica"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__analytic_account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__analytic_account_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__analytic_account_ids
msgid "Analytic Accounts"
msgstr "Cuentas analiticas"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_assets0
msgid "Assets"
msgstr "Activos"

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_audit_reports
msgid "Audit Reports"
msgstr "Informes de auditoría"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__0
msgid "Automatic formatting"
msgstr "Formateo automático"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Balance"
msgstr "Saldo Pendiente"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_bs
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_bs
msgid "Balance Sheet"
msgstr "Hoja de Balance"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Base Amount"
msgstr "Importe base"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_tax_report_view
msgid "Cancel"
msgstr "Cancelar"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__children_ids
msgid "Children"
msgstr "Hijo"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
msgid "Childrens"
msgstr "Hijos"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Code"
msgstr "Código"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__label_filter
msgid "Column Label"
msgstr "Etiqueta de Columna"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__company_id
msgid "Company"
msgstr "Compañía"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Comparison"
msgstr "Comparación"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_date
msgid "Created on"
msgstr "Creado en"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Credit"
msgstr "Haber"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Currency"
msgstr "Moneda"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_date
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Date"
msgstr "Fecha"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from_cmp
msgid "Date From"
msgstr "Fecha desde"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to_cmp
msgid "Date To"
msgstr "Fecha hasta"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Date:"
msgstr "Fecha:"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Dates"
msgstr "Fechas"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Debit"
msgstr "Debe"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_account
msgid "Display Accounts"
msgstr "Mostrar Cuentas"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__debit_credit
msgid "Display Debit/Credit Columns"
msgstr "Mostrar Columnas de Debe/Haber"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_flat
msgid "Display children flat"
msgstr "Mostrar Cuentas"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_with_hierarchy
msgid "Display children with hierarchy"
msgstr "Mostrar Cuentas"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_detail
msgid "Display details"
msgstr "Mostrar detalles"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Drill Down Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Dynamic Accounting Reports"
msgstr "Reportes de contabilidad dinámicos"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__enable_filter
msgid "Enable Comparison"
msgstr "Habilitar Comparación"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to
msgid "End Date"
msgstr "Fecha final"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Enhanced Financial Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Asientos ordenadas por"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Entry Label"
msgstr "Nivel Básico"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Excel Reports"
msgstr "Reportes en Excel"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_expense0
msgid "Expense"
msgstr "Gasto"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__filter_cmp
msgid "Filter by"
msgstr "Filtrar por"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_financial
msgid "Financial Report"
msgstr "Reporte financiero"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid "Financial Report Style"
msgstr "Estilo de Reporte Financiero"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_financial_report_tree
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_financial
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_legal_statement
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_reports_settings
msgid "Financial Reports"
msgstr "Reportes Financieros"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""
"En el caso de las cuentas que suelen estar más cargadas que acreditadas y "
"que le gustaría imprimir como cantidades negativas en sus informes, debe "
"invertir el signo del saldo; ej .: cuenta de gastos. Lo mismo se aplica a "
"las cuentas que suelen estar más acreditadas que debitadas y que le "
"gustaría imprimir como cantidades positivas en sus informes; ej .: cuenta "
"de ingresos."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#: code:addons/accounting_pdf_reports/report/report_financial.py:0
#: code:addons/accounting_pdf_reports/report/report_general_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_journal.py:0
#: code:addons/accounting_pdf_reports/report/report_partner_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_tax.py:0
#: code:addons/accounting_pdf_reports/report/report_trial_balance.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Falta el contenido del formulario, este informe no se puede imprimir."

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_general_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_general_ledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_ledger
msgid "General Ledger"
msgstr "Libro mayor"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_general_ledger
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_general_ledger
msgid "General Ledger Report"
msgstr "Reporte de Libro Mayor"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Group By"
msgstr "Agrupar por"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__id
msgid "ID"
msgstr "ID (identificación)"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Si seleccionó la fecha, este campo le permite agregar una fila para mostrar "
"la cantidad de débito / crédito / saldo que precede al filtro que "
"estableció."

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid "Include Initial Balances"
msgstr "Incluir Saldos Iniciales"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_income0
msgid "Income"
msgstr "Ingreso"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""
"Agrega la columna de moneda en el informe si la moneda difiere de la moneda "
"de la empresa."

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__5
msgid "Italic Text (smaller)"
msgstr "Texto en cursiva (más pequeño)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "JRNL"
msgstr "JRNL"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal"
msgstr "Diario"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr "Diario & Socio"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_journal
msgid "Journal Audit Report"
msgstr "Reporte del diario de auditoría"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal Entry Number"
msgstr "Número de asiento"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Journal and Partner"
msgstr "Diario y Socio"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Journal:"
msgstr "Diario:"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__journal_ids
msgid "Journals"
msgstr "Diarios"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_print_journal_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_print_journal
msgid "Journals Audit"
msgstr "Auditoría de Libros"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal_entries
msgid "Journals Entries"
msgstr "Entradas de diarios"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Label"
msgstr "Descripción"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__level
msgid "Level"
msgstr "Nivel"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liability0
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liabilitysum0
msgid "Liability"
msgstr "Pasivo"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__1
msgid "Main Title 1 (bold, underlined)"
msgstr "Título principal 1 (negrita, subrayado)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Move"
msgstr "Asiento"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Name"
msgstr "Nombre"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Net"
msgstr "Neto"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_no
msgid "No Filters"
msgstr "Sin Filtros"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__no_detail
msgid "No detail"
msgstr "Sin detalle"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__4
msgid "Normal Text"
msgstr "Texto Normal"

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_legal_statement
msgid "PDF Reports"
msgstr "Reportes en PDF"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__parent_id
msgid "Parent"
msgstr "Principal"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Parent Report"
msgstr "Reporte Padre"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Partner"
msgstr "Empresa"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_partner_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_partnerledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_partner_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Partner Ledger"
msgstr "Libro mayor de empresa"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_partnerledger
msgid "Partner Ledger Report"
msgstr "Reporte del libro mayor de empresa"

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_partner_reports
msgid "Partner Reports"
msgstr "Reporte de empresas"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__result_selection
msgid "Partner's"
msgstr "De los Socios"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Partner:"
msgstr "Empresa:"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__partner_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__partner_ids
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Partners"
msgstr "Contactos"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Payable Accounts"
msgstr "Cuentas a pagar"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__period_length
msgid "Period Length (days)"
msgstr "Duración del Período (días)"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__1
msgid "Preserve balance sign"
msgstr "Conservar firma de balance"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Preview financial reports without downloading"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_tax_report_view
msgid "Print"
msgstr "Imprimir"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Imprimir informe con columna moneda si la moneda difiere de la de la "
"compañía"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "Beneficio (pérdida) para informar"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_pl
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_pl
msgid "Profit and Loss"
msgstr "Ganancia y Perdida"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable Accounts"
msgstr "Cuentas a cobrar"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer_supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "Cuentas por Cobrar y Por Pagar"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__reconciled
msgid "Reconciled Entries"
msgstr "Asientos conciliados"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Ref"
msgstr "Ref"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Reference:"
msgstr "Referencia:"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
msgid "Report"
msgstr "Informe"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__name
msgid "Report Name"
msgstr "Nombre de Informe"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_tax_report_view
msgid "Report Options"
msgstr "Opciones del informe"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Report Type"
msgstr "Tipo de informe"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_report_id
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_report
msgid "Report Value"
msgstr "Valor del Informe"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Reports"
msgstr "Reportes"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__-1
msgid "Reverse balance sign"
msgstr "Reversar firma de balance"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Sale"
msgstr "Venta"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sign
msgid "Sign on Reports"
msgstr "Firma en Informes"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__6
msgid "Smallest Text"
msgstr "Texto más pequeño"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__sortby
msgid "Sort by"
msgstr "Ordenar por"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__target_move
msgid "Target Moves"
msgstr "Movimientos destino"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax"
msgstr "Impuesto"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Amount"
msgstr "Importe impuesto"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Declaration"
msgstr "Declaración de impuestos"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_account_tax
#: model:ir.model,name:accounting_pdf_reports.model_account_tax_report_wizard
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_tax
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax Report"
msgstr "Reporte Impuestos"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_tax_report
msgid "Tax Reports"
msgstr "Informes de Impuestos"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""
"Esta etiqueta se mostrará en el informe para mostrar el saldo calculado "
"para el filtro de comparación dado."

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""
"Esta opción le permite obtener más detalles sobre la forma en que se "
"calculan sus saldos. Debido a que consume espacio, no permitimos usarlo "
"mientras hacemos una comparación."

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__2
msgid "Title 2 (bold)"
msgstr "Título 2 (negrita)"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__3
msgid "Title 3 (bold, smaller)"
msgstr "Título 3 (negrita, más pequeña)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Total"
msgstr "Total"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_balance_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_trial_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_balance_report
msgid "Trial Balance"
msgstr "Balance de Comprobación"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_balance_report
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_trialbalance
msgid "Trial Balance Report"
msgstr "Informe de Balance de Comprobación"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__type
msgid "Type"
msgstr "Tipo"

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#: code:addons/report/report_aged_partner.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Empresa desconocida"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__sum
msgid "View"
msgstr "Ver"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__amount_currency
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid "With Currency"
msgstr "Con moneda"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "Con saldo no es igual a 0"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With balance not equal to zero"
msgstr "Con saldo no igual a cero"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__movement
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With movements"
msgstr "Con movimientos"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""
"Puede configurar aquí el formato en el que desea que se muestre este "
"registro. Si deja el formato automático, se calculará en función de la "
"jerarquía de informes financieros (\"nivel\" de campo calculado "
"automáticamente)."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/account_general_ledger.py:0
#: code:addons/wizard/account_general_ledger.py:0
#, python-format
msgid "You must define a Start Date"
msgstr "Debes definir una Fecha de Inicio"

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#: code:addons/wizard/aged_partner.py:0
#, python-format
msgid "You must set a period length greater than 0."
msgstr "Debe establecer una duración del período superior a 0."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#: code:addons/wizard/aged_partner.py:0
#, python-format
msgid "You must set a start date."
msgstr "Debe establecer una fecha de inicio."
