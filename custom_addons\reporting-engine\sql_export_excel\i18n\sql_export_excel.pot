# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sql_export_excel
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__col_position
msgid "Column Position"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields.selection,name:sql_export_excel.selection__sql_export__file_format__excel
msgid "Excel"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__attachment_id
msgid "Excel Template"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__file_format
msgid "File Format"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__header
msgid "Header"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__attachment_id
msgid ""
"If you configure an excel file (in xlsx format) here, the result of the query will be injected in it.\n"
"It is usefull to feed data in a excel file pre-configured with calculation"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__col_position
msgid "Indicate from which column the result of the query should be injected."
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__row_position
msgid "Indicate from which row the result of the query should be injected."
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__header
msgid "Indicate if the header should be exported to the file."
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,help:sql_export_excel.field_sql_export__sheet_position
msgid ""
"Indicate the sheet's position of the excel template where the result of the "
"sql query should be injected."
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__row_position
msgid "Row Position"
msgstr ""

#. module: sql_export_excel
#: model:ir.model,name:sql_export_excel.model_sql_export
msgid "SQL export"
msgstr ""

#. module: sql_export_excel
#: model:ir.model.fields,field_description:sql_export_excel.field_sql_export__sheet_position
msgid "Sheet Position"
msgstr ""

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid ""
"The Excel Template file contains less than %s sheets Please, adjust the "
"Sheet Position parameter."
msgstr ""

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid "The column position can't be less than 1."
msgstr ""

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid "The row position can't be less than 1."
msgstr ""

#. module: sql_export_excel
#. odoo-python
#: code:addons/sql_export_excel/models/sql_export.py:0
#, python-format
msgid "The sheet position can't be less than 1."
msgstr ""
