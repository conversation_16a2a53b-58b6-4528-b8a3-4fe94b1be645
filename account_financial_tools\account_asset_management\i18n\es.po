# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset_management
#
# Translators:
# <PERSON><PERSON> Altinisik <<EMAIL>>, 2015
# <PERSON><PERSON> Junior <<EMAIL>>, 2015
# Bruno JOLIVEAU, 2015
# danimaribe<PERSON> <<EMAIL>>, 2015-2016
# <AUTHOR> <EMAIL>, 2011-2014
# Hotellook, 2014
# Jarmo <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON>, 2016
# Pedro <PERSON>ez<PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2015
# <PERSON><PERSON> Terrettaz, 2015
# yterrettaz, 2015
# <PERSON> <<EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: account-financial-tools (8.0)\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-20 01:10+0100\n"
"PO-Revision-Date: 2025-02-13 19:14+0000\n"
"Last-Translator: \"Pedro M. Baeza\" <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"\n"
"Error while processing asset '{ref}': {exception}"
msgstr ""
"\n"
"Error mientras se procesa el activo '{ref}': {exception}"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
msgid ""
"<span class=\"o_form_label oe_inline\" invisible=\"salvage_type != "
"'percent'\">%</span>"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.view_move_form
msgid "<span class=\"o_stat_text\"> Asset(s)</span>"
msgstr "<span class=\"o_stat_text\"> Activo(s)</span>"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model,name:account_asset_management.model_account_account
#, python-format
msgid "Account"
msgstr "Cuenta"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Account Asset"
msgstr "Cuenta de activo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__carry_forward_missed_depreciations
msgid "Accumulate missed depreciations"
msgstr "Acumular amortizaciones pendientes"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__active
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__active
msgid "Active"
msgstr "Activado"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Active Assets"
msgstr "Activos en curso"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Add an internal note here..."
msgstr "Añada aquí una nota interna..."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_compute__date_end
msgid "All depreciation lines prior to this date will be automatically posted"
msgstr ""
"Todas las líneas de amortización anteriores a esta fecha serán publicadas "
"automáticamente"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__allow_reversal
msgid "Allow Reversal of journal entries"
msgstr "Autorizar deshacer asientos contables"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__amount
msgid "Amount"
msgstr "Importe"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__depreciated_value
msgid "Amount Already Depreciated"
msgstr "Importe ya amortizado"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__analytic_distribution
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__analytic_distribution_search
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Búsqueda de distribución analítica"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
msgid "Analytic Information"
msgstr "Información analítica"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__analytic_precision
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__analytic_precision
msgid "Analytic Precision"
msgstr "Precisión analítica"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Archived"
msgstr "Archivado"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Are you sure ?"
msgstr "¿Está seguro?"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__asset_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_move_line__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Asset"
msgstr "Activo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "Asset '%s' Removal Journal Entry"
msgstr "Asiento de eliminación del activo '%s'"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_asset_id
msgid "Asset Account"
msgstr "Cuenta de activo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_bank_statement_line__asset_count
#: model:ir.model.fields,field_description:account_asset_management.field_account_move__asset_count
#: model:ir.model.fields,field_description:account_asset_management.field_account_payment__asset_count
msgid "Asset Count"
msgstr "Número de activos"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_group
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__asset_group_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_group_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_group_view_search
msgid "Asset Group"
msgstr "Grupo de activo"

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_group_action
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__group_ids
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__group_ids
#: model:ir.ui.menu,name:account_asset_management.account_asset_group_menu
msgid "Asset Groups"
msgstr "Grupos de activo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__line_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Asset Line"
msgstr "Línea de activo"

#. module: account_asset_management
#: model:ir.actions.server,name:account_asset_management.ir_cron_assets_generator_ir_actions_server
msgid "Asset Management: Generate assets"
msgstr "Gestión de activos: generar activos"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__name
msgid "Asset Name"
msgstr "Nombre de activo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_account__asset_profile_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__profile_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_move_line__asset_profile_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
msgid "Asset Profile"
msgstr "Categoría del activo"

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_profile_action
#: model:ir.ui.menu,name:account_asset_management.account_asset_profile_menu
msgid "Asset Profiles"
msgstr "Categorías de activo"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_line__type__remove
msgid "Asset Removal"
msgstr "Eliminación de activo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__date_remove
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__date_remove
#, python-format
msgid "Asset Removal Date"
msgstr "Fecha de eliminación del activo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_sale_id
msgid "Asset Sale Account"
msgstr "Cuenta de venta del activo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__date_start
#, python-format
msgid "Asset Start Date"
msgstr "Fecha de inicio del activo"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_line
msgid "Asset depreciation table line"
msgstr "Línea de amortización del activo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid "Asset name must be set in the label of the line."
msgstr "El nombre del activo debe establecerse en la etiqueta de la línea."

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_profile
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
msgid "Asset profile"
msgstr "Categoría del activo"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Asset removal."
msgstr "Eliminación del activo."

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_recompute_trigger
msgid "Asset table recompute triggers"
msgstr "Lanzadores de recálculo de la tabla del activo"

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_action
#: model:ir.ui.menu,name:account_asset_management.account_asset_menu
#: model:ir.ui.menu,name:account_asset_management.menu_finance_assets
#: model:ir.ui.menu,name:account_asset_management.menu_finance_config_assets
msgid "Assets"
msgstr "Activos"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Assets to be corrected"
msgstr "Activos a ser corregidos"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Número de adjuntos"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__asset_product_item
msgid ""
"By default during the validation of an invoice, an asset is created by "
"invoice line as long as an accounting entry is created by invoice line. With "
"this setting, an accounting entry will be created by product item. So, there "
"will be an asset by product item."
msgstr ""
"Por defecto, durante la validación de una factura se crea un activo por "
"línea de factura así como un apunte por línea de factura. Con esta "
"configuración se creará un apunte contable por artículo del producto. Por lo "
"tanto, habrá un activo por producto."

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__days_calc
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__days_calc
msgid "Calculate by days"
msgstr "Calcular por días"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_asset_move_reverse_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this profile "
"when created by invoices."
msgstr ""
"Marque esto si desea confirmar automáticamente los activos de esta categoría "
"cuando se crean desde las facturas."

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__child_ids
msgid "Child Asset Groups"
msgstr "Grupos de subcuentas de activos"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method_time
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method_time
msgid ""
"Choose the method to use to compute the dates and number of depreciation "
"lines.\n"
"  * Number of Years: Specify the number of years for the depreciation.\n"
"  * Number of Depreciations: Fix the number of depreciation lines and the "
"time between 2 depreciations.\n"
msgstr ""
"Seleccione el método para calcular las fechas y el número de líneas de "
"amortización.\n"
"   * Número de años: especifique el número de años de la amortización.\n"
"   * Número de amortizaciones: fije el número de líneas de amortización y el "
"tiempo entre 2 amortizaciones.\n"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method
msgid ""
"Choose the method to use to compute the depreciation lines.\n"
"  * Linear: Calculated on basis of: Depreciation Base / Number of "
"Depreciations. Depreciation Base = Purchase Value - Salvage Value.\n"
"  * Linear-Limit: Linear up to Salvage Value. Depreciation Base = Purchase "
"Value.\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor.\n"
"  * Degressive-Linear (only for Time Method = Year): Degressive becomes "
"linear when the annual linear depreciation exceeds the annual degressive "
"depreciation.\n"
"   * Degressive-Limit: Degressive up to Salvage Value. The Depreciation Base "
"is equal to the asset value."
msgstr ""
"Seleccione el método para calcular las líneas de amortización.\n"
"  * Lineal: Calculado en base a: Base Amortizable / Número de "
"Amortizaciones. Base Amortizable = Valor de Compra - Valor Residual.\n"
"  * Lineal-Limit: Lineal hasta el Valor de rescate. Base Amortizable = Valor "
"de Compra.\n"
"  * Decreciente: Calculado en base a: Valor Residual * Coeficiente de "
"Amortización.\n"
"  * Decreciente-Lineal (solo para Periodo de Tiempo = Año): Decreciente se "
"convierte en lineal cuando la amortización lineal anual supera la "
"amortización decreciente anual.\n"
"   * Decreciente-Limit: Decreciente hasta el Valor de rescate. La Base "
"Amortizable es igual al valor del activo."

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Close"
msgstr "Cerrado"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__code
msgid "Code"
msgstr "Código"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__company_id
msgid "Company"
msgstr "Compañía"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__currency_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__currency_id
msgid "Company Currency"
msgstr "Moneda la compañía"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__date_completed
msgid "Completion Date"
msgstr "Fecha de terminación"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Comput. Method"
msgstr "Método de cálculo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Computation Method"
msgstr "Método de cálculo"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Compute"
msgstr "Calcular"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form
msgid "Compute Asset"
msgstr "Calcular activo"

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_compute_action
#: model:ir.model,name:account_asset_management.model_account_asset_compute
#: model:ir.ui.menu,name:account_asset_management.account_asset_compute_menu
msgid "Compute Assets"
msgstr "Calcular amortizaciones"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_compute.py:0
#, python-format
msgid "Compute Assets errors"
msgstr "Calcular errores en los activos"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_compute.py:0
#, python-format
msgid "Compute Assets result"
msgstr "Calcular resultado de Activos"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
msgid "Compute Assets results"
msgstr "Calcular resultados de Activos"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_asset_move_reverse_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Confirm Asset"
msgstr "Confirmar activo"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Create Move"
msgstr "Crear asiento"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__asset_product_item
msgid "Create an asset by product item"
msgstr "Crear un activo por artículo de producto"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_compute.py:0
#, python-format
msgid "Created Asset Moves"
msgstr "Asientos de activos creados"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__date_end
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__line_date
msgid "Date"
msgstr "Fecha"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_recompute_trigger__date_trigger
msgid "Date of the event triggering the need to recompute the Asset Tables."
msgstr "Fecha del evento que hace recalcular las tablas del activo."

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__line_days
msgid "Days"
msgstr "Dias"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_account__asset_profile_id
msgid "Default Asset Profile when creating invoice lines with this account."
msgstr ""
"Categoría del activo por defecto cuando se creen líneas de factura con esta "
"cuenta."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive"
msgstr "Decreciente"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive  up to Salvage Value"
msgstr "Regresivo hasta valor de rescate"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_progress_factor
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_progress_factor
msgid "Degressive Factor"
msgstr "Factor decreciente"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive-Linear"
msgstr "Decreciente-Lineal"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive-Linear is only supported for Time Method = Year."
msgstr "Decreciente-Lineal sólo está soportado para el método de tiempo = Año."

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Delete/Reverse Move"
msgstr "Borrar/Deshacer movimiento"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_expense_depreciation_id
msgid "Depr. Expense Account"
msgstr "Cuenta de gastos de amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__value_depreciated
msgid "Depreciated Value"
msgstr "Valor amortizado"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_line__type__depreciate
msgid "Depreciation"
msgstr "Amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_depreciation_id
msgid "Depreciation Account"
msgstr "Cuenta de amortización"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__depreciation_base
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__depreciation_base
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_line__type__create
#, python-format
msgid "Depreciation Base"
msgstr "Base Amortización"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Depreciation Board"
msgstr "Tabla de amortización"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Depreciation Dates"
msgstr "Fechas de amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__move_id
msgid "Depreciation Entry"
msgstr "Asiento de amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__depreciation_line_ids
msgid "Depreciation Lines"
msgstr "Líneas de amortización"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Depreciation Method"
msgstr "Método de amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__name
msgid "Depreciation Name"
msgstr "Nombre de la amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_recompute_trigger__state__done
msgid "Done"
msgstr "Completado"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__draft
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Draft"
msgstr "Borrador"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Duplicate reporting entries"
msgstr "Duplicar asientos en el reporte"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_report_account_asset_management_asset_report_xls
msgid "Dynamic XLS asset report generator"
msgstr "Generador dinámico de informes de activos en XLS"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__date_to
msgid "End Date"
msgstr "Fecha de finalización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_end
msgid "Ending Date"
msgstr "Fecha de finalización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__account_move_line_ids
msgid "Entries"
msgstr "Asientos"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"Error while processing asset '{ref}': \n"
"\n"
"{tb}"
msgstr ""
"Error al procesar el activo '{ref}': \n"
"\n"
"{tb}"

#. module: account_asset_management
#: model:ir.ui.menu,name:account_asset_management.account_asset_report_menu
msgid "Financial Assets"
msgstr "Activos Financieros"

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.wiz_account_asset_report_action
#: model:ir.model,name:account_asset_management.model_wiz_account_asset_report
#: model:ir.ui.menu,name:account_asset_management.wiz_account_asset_report_menu
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
msgid "Financial Assets report"
msgstr "Informe de Activos Financieros"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_profile__salvage_type__fixed
msgid "Fixed"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Socios)"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font-Awesome p. ej. fa-tasks"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__force_date
msgid "Force accounting date"
msgstr "Forzar fecha contable"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "Gain/Loss on Sale"
msgstr "Ganancia/Pérdida en la venta"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "General"
msgstr "General"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "Generate Asset Removal entries"
msgstr "Generar asientos de eliminación de activo"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
msgid "Generate Removal entries"
msgstr "Generar asientos de eliminación"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
msgid "Generate Report"
msgstr "Generar informe"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__has_message
msgid "Has Message"
msgstr "Tiene mensaje"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__move_line_check
msgid "Has accounting entries"
msgstr "Tiene asientos contables"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "History"
msgstr "Historial"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__id
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__id
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__id
msgid "ID"
msgstr "ID"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcado, algunos mensajes tienen error de envío."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__carry_forward_missed_depreciations
msgid ""
"If create an asset in a fiscal period that is now closed\n"
"        the accumulated amount of depreciations that cannot be posted will "
"be\n"
"        carried forward to the first depreciation line of the current open\n"
"        period."
msgstr ""
"Si se crea un activo en un periodo fiscal ya cerrado,\n"
"        la cantidad de amortización acumulada que no se haya podido "
"registrar será\n"
"        llevada a la primera línea de amortización del periodo actualmente\n"
"        abierto."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_wiz_asset_move_reverse__journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "Si está vacío, utiliza el diario del asiento a ser revertido."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__use_leap_years
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__use_leap_years
msgid ""
"If not set, the system will distribute evenly the amount to amortize across "
"the years, based on the number of years. So the amount per year will be the "
"depreciation base / number of years.\n"
" If set, the system will consider if the current year is a leap year. The "
"amount to depreciate per year will be calculated as depreciation base / "
"(depreciation end date - start date + 1) * days in the current year."
msgstr ""
"Si no se marca, el sistema distribuirá de manera uniforme el importe a "
"amortizar a lo largo de los años, en función de la cantidad de años. Por lo "
"tanto, la cantidad por año será la base de depreciación / número de años.\n"
" Si se marca, el sistema considerará si el año actual es bisiesto. El "
"importe a amortizar por año se calculará como base de amortización / (fecha "
"final de la amortización - fecha inicial + 1) * días en el año actual."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__allow_reversal
msgid ""
"If set, when pressing the Delete/Reverse Move button in a posted "
"depreciation line will prompt the option to reverse the journal entry, "
"instead of deleting them."
msgstr ""
"Si se marca, cuando se presione el botón Borrar/Retroceder Movimiento en una "
"línea de amortización ya publicada, se lanzará la opción de modificar el "
"asiento en lugar de borrarlo."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "Illegal value %s in asset.method."
msgstr "Valor ilegal %s en asset.method."

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__draft
msgid "Include draft assets"
msgstr "Incluir activos en borrador"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid ""
"Inconsistent reporting structure.\n"
"Please correct Asset Group '{group}' (id {id})"
msgstr ""
"Estructura de informes inconsistente.\n"
"Corrija el grupo de activos '{group}' (id {id})"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__prorata
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__prorata
msgid ""
"Indicates that the first depreciation entry for this asset has to be done "
"from the depreciation start date instead of the first day of the fiscal year."
msgstr ""
"Indica que el primer asiento de amortización para este activo tiene que ser "
"realizado desde la fecha de inicio de la amortización en lugar del primer "
"día del ejercicio fiscal."

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Init"
msgstr "Inicio"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__init_entry
msgid "Initial Balance Entry"
msgstr "Asiento de saldo inicial"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "Es seguidor"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__journal_id
msgid "Journal"
msgstr "Diario"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
#, python-format
msgid "Journal Entries"
msgstr "Asientos contables"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#: model:ir.model,name:account_asset_management.model_account_move
#, python-format
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.act_entries_open
msgid "Journal Items"
msgstr "Apuntes contables"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Linear"
msgstr "Lineal"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Linear up to Salvage Value"
msgstr "Lineal hasta Valor de rescate"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_min_value_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_min_value_id
msgid "Min-Value Account"
msgstr "Cuenta para pérdida de valor"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Missing depreciation table"
msgstr "Falta la tabla de amortización"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Month"
msgstr "Mes"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__name
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "New Acquisitions"
msgstr "Adquisiciones nuevas"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__remaining_value
msgid "Next Period Depreciation"
msgstr "Amortización del siguiente período"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "No"
msgstr "No"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__note
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__note
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__note
msgid "Note"
msgstr "Nota"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__note
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Notes"
msgstr "Notas"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Number of Depreciations"
msgstr "Número de depreciaciones"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_number
#, python-format
msgid "Number of Years"
msgstr "Número de años"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Number of Years or end date"
msgstr "Número de años o fecha de finalización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de entrega"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_recompute_trigger__state__open
msgid "Open"
msgstr "Abierto"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Other Information"
msgstr "Otra información"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__parent_id
msgid "Parent Asset Group"
msgstr "Grupo de activos padre"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__parent_path
msgid "Parent Path"
msgstr "Ruta padre"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__partner_id
msgid "Partner"
msgstr "Empresa"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_profile__salvage_type__percent
msgid "Percentage of Price"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Period Depreciation"
msgstr "Periodo de amortización"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Period End Value"
msgstr "Valor al final del periodo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_period
msgid "Period Length"
msgstr "Longitud del periodo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Period Start Value"
msgstr "Valor al comienzo del periodo"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method_period
msgid "Period length for the depreciation accounting entries"
msgstr "Longitud del periodo para los asientos de amortización"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_plus_value_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_plus_value_id
msgid "Plus-Value Account"
msgstr "Cuenta para ganancia de valor"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__move_check
msgid "Posted"
msgstr "Asentado"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__previous_id
msgid "Previous Depreciation Line"
msgstr "Línea de amortización previa"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Profile"
msgstr "Perfil"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__prorata
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__prorata
#, python-format
msgid "Prorata Temporis"
msgstr "Tiempo prorrateado"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__purchase_value
#, python-format
msgid "Purchase Value"
msgstr "Valor de compra"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Quarter"
msgstr "Trimestre"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__reason
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__reason
#, python-format
msgid "Reason"
msgstr "Razón"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__code
#, python-format
msgid "Reference"
msgstr "Referencia"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__posting_regime
msgid "Removal Entry Policy"
msgstr "Política para el asiento de eliminación"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_remove__posting_regime
msgid ""
"Removal Entry Policy \n"
"  * Residual Value: The non-depreciated value will be posted on the "
"'Residual Value Account' \n"
"  * Gain/Loss on Sale: The Gain or Loss will be posted on the 'Plus-Value "
"Account' or 'Min-Value Account' "
msgstr ""
"Política para el asiento de eliminación\n"
"  * Valor residual: el valor no amortizado será llevado a la \"Cuenta de "
"valor residual\"\n"
"  * Ganancia/Pérdida en la venta: la ganancia o pérdida será llevada a la "
"\"Cuenta para ganancia de valor\" o a la \"Cuenta para pérdida de valor\" "

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_remove__date_remove
msgid ""
"Removal date must be after the last posted entry in case of early removal"
msgstr ""
"La fecha de eliminación debe ser posterior a la del último asiento asentado "
"en caso de eliminación temprana"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Remove"
msgstr "Eliminar"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_remove
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
msgid "Remove Asset"
msgstr "Eliminar activo"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__removed
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Removed"
msgstr "Eliminado"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Removed Assets"
msgstr "Activos eliminados"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__value_residual
#, python-format
msgid "Residual Value"
msgstr "Valor residual"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_residual_value_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_residual_value_id
msgid "Residual Value Account"
msgstr "Cuenta de valor residual"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
msgid "Results :"
msgstr "Resultados:"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__date_reversal
msgid "Reversal date"
msgstr "Fecha de reversión"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_asset_move_reverse_view_form
msgid "Reverse Journal Entry"
msgstr "Revertir asiento"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid "Reverse Move"
msgstr "Revertir movimiento"

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_wiz_asset_move_reverse
msgid "Reverse posted journal entry on depreciation line"
msgstr "Revertir asiento publicado en la línea de amortización"

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__open
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Running"
msgstr "En ejecución"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__sale_value
msgid "Sale Value"
msgstr "Valor de venta"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__salvage_type
msgid "Salvage Type"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__salvage_value
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__salvage_value
#, python-format
msgid "Salvage Value"
msgstr "Valor de rescate"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_group_view_search
msgid "Search Asset Group"
msgstr "Buscar grupo de activo"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
msgid "Search Asset Profile"
msgstr "Buscar la categoría del activo"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_line__init_entry
msgid ""
"Set this flag for entries of previous fiscal years for which Odoo has not "
"generated accounting entries."
msgstr ""
"Marque esta casilla para las entradas de ejercicios fiscales anteriores para "
"los que no se ha generado asientos contables."

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Set to Draft"
msgstr "Cambiar a borrador"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__open_asset
msgid "Skip Draft State"
msgstr "Omitir estado borrador"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__date_from
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__state
msgid "State"
msgstr "Estado"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__parent_state
msgid "State of Asset"
msgstr "Estado del activo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__state
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
#, python-format
msgid "Status"
msgstr "Estado"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: la fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"The '_compute_year_amount' method is only intended for Time Method 'Number "
"of Years'."
msgstr ""
"El método '_compute_year_amount' solo está destinado al Método de tiempo "
"'Número de años'."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "The 'account' field is a mandatory entry of the '_xls_%s_fields' list !"
msgstr ""
"¡El campo 'cuenta' es una entrada obligatoria en la lista '_xls_%s_fields'!"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_account.py:0
#, python-format
msgid ""
"The Asset Account defined in the Asset Profile must be equal to the account."
msgstr ""
"La cuenta del activo definida en la categoría de activo debe ser igual a la "
"cuenta."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "The Sale Value must be positive!"
msgstr "¡El valor de venta debe ser positivo!"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#: code:addons/account_asset_management/wizard/wiz_account_asset_report.py:0
#, python-format
msgid "The Start Date must precede the Ending Date."
msgstr "La fecha de inicio debe ser anterior a la fecha de finalización."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"The duration of the asset conflicts with the posted depreciation table entry "
"dates."
msgstr ""
"La duración del activo entra en conflicto con la fechas de asiento asentadas "
"de la tabla de amortización."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__salvage_value
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__salvage_value
msgid ""
"The estimated value that an asset will realize upon its sale at the end of "
"its useful life.\n"
"This value is used to determine the depreciation amounts."
msgstr ""
"El valor estimado que un activo tendrá hasta su venta al final de su vida "
"útil.\n"
"Este valor se usará para determinar los importes de amortización."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method_number
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method_number
msgid "The number of years needed to depreciate your asset"
msgstr "El número de años necesario para amortizar su activo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "The removal date must be after the last depreciation date."
msgstr ""
"La fecha de eliminación debe ser después de la última fecha de amortización."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__depreciation_base
#: model:ir.model.fields,help:account_asset_management.field_account_asset_line__depreciation_base
msgid ""
"This amount represent the depreciation base of the asset (Purchase Value - "
"Salvage Value)."
msgstr ""
"Este importe representa la base de amortización del activo (Valor de compra "
"- Valor de rescate)."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__purchase_value
msgid ""
"This amount represent the initial value of the asset.\n"
"The Depreciation Base is calculated as follows:\n"
"Purchase Value - Salvage Value."
msgstr ""
"Esta cantidad representa el valor inicial del activo.\n"
"El valor del activo se calcula de la siguiente forma:\n"
"Valor de compra - valor de rescate."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid "This invoice created the asset(s): %s"
msgstr "Esta factura ha creado el/los activo(s): %s"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_time
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_time
msgid "Time Method"
msgstr "Método de tiempo"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Tot. Depreciation"
msgstr "Amortización total"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Total Days"
msgstr "Días totales"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Totals"
msgstr "Amortización total"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__date_trigger
msgid "Trigger Date"
msgstr "Fecha de disparo"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__type
msgid "Type"
msgstr "Tipo"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Undetermined error"
msgstr "Error indeterminado"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__use_leap_years
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__use_leap_years
msgid "Use Leap Years"
msgstr "Usar años bisiestos"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__journal_id
msgid "Use Specific Journal"
msgstr "Utilizar diario específico"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__days_calc
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__days_calc
msgid "Use number of days to calculate depreciation amount"
msgstr "Usar número de días para calcular el importe de la amortización"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
msgid "View Asset Moves"
msgstr "Ver asientos de activos"

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "View Move"
msgstr "Ver asiento"

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__state
#: model:ir.model.fields,help:account_asset_management.field_account_asset_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation "
"lines can be posted to the accounting.\n"
"If the last depreciation line is posted, the asset goes into the 'Close' "
"status.\n"
"When the removal entries are generated, the asset goes into the 'Removed' "
"status."
msgstr ""
"Cuando se crea un activo, su estado es 'Borrador'.\n"
"Si el activo se confirma, su estado pasa a 'En ejecución' y las líneas de "
"amortización pueden ser publicadas en la contabilidad.\n"
"Si la última línea de amortización se publica, el activo pasa a estado "
"'Cerrado'.\n"
"Cuando se generan los asientos de eliminación, el activo pasa a estado "
"'Eliminado'."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Year"
msgstr "Año"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You are not allowed to link an accounting entry to an asset.\n"
"You should generate such entries from the asset."
msgstr ""
"No está autorizado a enlazar un asiento contable a un activo.\n"
"Debe generar los asientos desde el activo."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You are not allowed to remove an accounting entry linked to an asset.\n"
"You should remove such entries from the asset."
msgstr ""
"No está autorizado a eliminar un asiento contable enlazado a un activo.\n"
"Debe eliminarlo desde el activo."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "You can only delete assets in draft state."
msgstr "Sólo puede eliminar activos en estado borrador."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid ""
"You can't make an early removal if all the depreciation lines for previous "
"periods are not posted."
msgstr ""
"No puede realizar una eliminación temprana si todos los asientos de las "
"líneas de amortización para los periodos previos no están generados."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot change a depreciation line with an associated accounting entry."
msgstr ""
"No puede cambiar una línea de amortización con un asiento contable asociado."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You cannot change an accounting entry linked to an asset depreciation line."
msgstr ""
"No puede cambiar un asiento contable enlazado a una línea de amortización de "
"un activo."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You cannot change an accounting item linked to an asset depreciation line."
msgstr ""
"No puede cambiar un apunte contable enlazado a una línea de amortización de "
"activo."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "You cannot change the profile of an asset with accounting entries."
msgstr "No puede cambiar la categoría de un activo con asientos contables."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot delete a depreciation line with an associated accounting entry."
msgstr "No puede eliminar una línea de amortización con un asiento enlazado."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "You cannot delete an asset that contains posted depreciation lines."
msgstr ""
"No puede eliminar un activo que contenga líneas de amortización asentadas."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid "You cannot remove an asset line of type 'Depreciation Base'."
msgstr ""
"No puede eliminar una linea de amortización de tipo 'Base Amortización'."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot set the 'Initial Balance Entry' flag on a depreciation line with "
"prior posted entries."
msgstr ""
"No puede establecer la casilla 'Asiento de saldo inicial' en una línea de "
"amortización con asientos anteriores ya generados."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid "You cannot set the Asset Start Date after already posted entries."
msgstr ""
"No puede establecer la fecha de inicio posterior a amortizaciones ya "
"contabilizadas."

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot set the date on a depreciation line prior to already posted "
"entries."
msgstr ""
"No puede establecer la fecha de la línea de amortización anterior a los "
"asientos ya generados."

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__date_start
msgid ""
"You should manually add depreciation lines with the depreciations of "
"previous fiscal years if the Depreciation Start Date is different from the "
"date for which accounting entries need to be generated."
msgstr ""
"Debe añadir manualmente las líneas de amortización con las amortizaciones de "
"ejercicios fiscales anteriores si la fecha de inicio de amortización es "
"diferente de la fecha para la cual se deben generar asientos contables."

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
msgid "or"
msgstr "o"

#~ msgid "SMS Delivery error"
#~ msgstr "Error en la entrega del SMS"

#~ msgid "Analytic"
#~ msgstr "Analítica"

#~ msgid "Last Modified on"
#~ msgstr "Última modificación el"

#~ msgid "Main Attachment"
#~ msgstr "Adjunto principal"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#, python-format
#~ msgid ""
#~ "\n"
#~ "Error while processing asset '%s': %s"
#~ msgstr ""
#~ "\n"
#~ "Error al procesar el activo '%s': %s"

#~ msgid "Analytic account"
#~ msgstr "Cuenta analítica"

#~ msgid "Analytic tags"
#~ msgstr "Etiquetas analíticas"

#~ msgid "Asset Lines"
#~ msgstr "Líneas de activo"

#, python-format
#~ msgid ""
#~ "Error while processing asset '%s': \n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Error mientras se procesaba activo '%s': \n"
#~ "\n"
#~ "%s"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#, python-format
#~ msgid ""
#~ "Inconsistent reporting structure.\n"
#~ "Please correct Asset Group '%s' (id %s)"
#~ msgstr ""
#~ "Estructura inconsistente del informe.\n"
#~ "Por favor, corrija el grupo del activo '%s' (id %s)"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes sin leer"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes no leídos"

#~ msgid "Unread Messages Counter"
#~ msgstr "Número de mensajes no leídos"

#~ msgid "Use leap years"
#~ msgstr "Use años bisiestos"

#~ msgid "account_analytic_id"
#~ msgstr "account_analytic_id"

#~ msgid "Delete Move"
#~ msgstr "Borrar asiento"

#~ msgid "Assets in Close State"
#~ msgstr "Activos cerrados"

#~ msgid "Assets in Running State"
#~ msgstr "Activos en ejecución"

#~ msgid "Assets which have been removed"
#~ msgstr "Activos que han sido eliminados"

#~ msgid "Draft Assets"
#~ msgstr "Activos borrador"

#~ msgid "Group By..."
#~ msgstr "Agrupado por..."

#~ msgid "Total Credit"
#~ msgstr "Total haber"

#~ msgid "Total Debit"
#~ msgstr "Total debe"

#~ msgid ""
#~ "Choose the method to use to compute the dates and number of depreciation "
#~ "lines.\n"
#~ "  * Number of Years: Specify the number of years for the depreciation.\n"
#~ msgstr ""
#~ "Escoja el método a usar para calcular las fechas y número de líneas de "
#~ "amortización.\n"
#~ "* Número de años: Especifica el número de años para la amortización.\n"

#~ msgid "Fiscal Year"
#~ msgstr "Ejercicio Fiscal"
