#!/usr/bin/env python3
"""
Quick check script - run this in Odoo shell to diagnose IFRS module
Usage: py -3.13 odoo-bin shell --config=odoo.conf -d my_odoo_erp
Then run: exec(open('quick_ifrs_check.py').read())
"""

print("🔍 IFRS Module Quick Diagnostic")
print("=" * 40)

try:
    # Check module installation
    module = env['ir.module.module'].search([('name', '=', 'ifrs_financial_statements')])
    if module:
        print(f"✅ Module installed: {module.state}")
    else:
        print("❌ Module not found")
        exit()

    # Check models
    try:
        stmt_model = env['ifrs.financial.statement']
        print(f"✅ Main model accessible")
        
        # Count records
        count = stmt_model.search_count([])
        print(f"✅ Existing statements: {count}")
        
    except Exception as e:
        print(f"❌ Model error: {e}")

    # Check menus
    menus = env['ir.ui.menu'].search([('name', 'ilike', 'IFRS')])
    print(f"✅ Menu items found: {len(menus)}")
    for menu in menus:
        print(f"   - {menu.name} (ID: {menu.id})")

    # Check user groups
    admin = env['res.users'].browse(1)  # Admin user
    ifrs_groups = admin.groups_id.filtered(lambda g: 'ifrs' in g.name.lower())
    print(f"✅ IFRS groups for admin: {len(ifrs_groups)}")
    for group in ifrs_groups:
        print(f"   - {group.name}")

    # Create test statement
    print("\n🧪 Creating test statement...")
    test_stmt = stmt_model.create({
        'name': 'Diagnostic Test Statement',
        'statement_type': 'balance_sheet',
        'reporting_period': 'annual',
        'date_from': '2024-01-01',
        'date_to': '2024-12-31',
    })
    print(f"✅ Test statement created: ID {test_stmt.id}")
    
    # Test generation
    test_stmt.action_generate_statement()
    line_count = len(test_stmt.statement_line_ids)
    print(f"✅ Statement lines generated: {line_count}")
    
    print("\n🎯 Module is working correctly!")
    print("Access URLs:")
    print("- Main: http://localhost:8069/web#menu_id=menu_ifrs_main")
    print(f"- Test Statement: http://localhost:8069/web#id={test_stmt.id}&model=ifrs.financial.statement&view_type=form")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
