# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date
import logging

_logger = logging.getLogger(__name__)


class Country(models.Model):
    _name = 'financial.planning.country'
    _description = 'Country for Financial Planning'
    _order = 'name'

    name = fields.Char(string='Country Name', required=True, translate=True)
    code = fields.Char(string='ISO Code', size=3, required=True, help='ISO 3166-1 alpha-3 country code')
    continent_id = fields.Many2one('financial.planning.continent', string='Continent', required=True)
    
    # Population and demographic data
    population = fields.Float(string='Population', help='Population in millions')
    population_density = fields.Float(string='Population Density', help='People per km²')
    area_km2 = fields.Float(string='Area (km²)')
    
    # Economic indicators
    gdp = fields.Monetary(string='GDP (USD)', currency_field='usd_currency_id', help='Gross Domestic Product in USD billions')
    gdp_per_capita = fields.Monetary(string='GDP per Capita (USD)', currency_field='usd_currency_id')
    inflation_rate = fields.Float(string='Inflation Rate (%)', digits=(5, 2))
    unemployment_rate = fields.Float(string='Unemployment Rate (%)', digits=(5, 2))
    
    # Growth rates
    annual_population_growth_rate = fields.Float(string='Population Growth Rate (%)', digits=(5, 2), required=True, default=1.5)
    annual_gdp_growth_rate = fields.Float(string='GDP Growth Rate (%)', digits=(5, 2), required=True, default=3.0)
    
    # Currency and financial
    currency_id = fields.Many2one('res.currency', string='Local Currency')
    usd_currency_id = fields.Many2one('res.currency', string='USD Currency', default=lambda self: self.env.ref('base.USD'))
    
    # Relationships
    city_ids = fields.One2many('financial.planning.city', 'country_id', string='Cities')
    growth_rate_ids = fields.One2many('financial.planning.growth.rate', 'country_id', string='Historical Growth Rates')
    
    # Computed fields
    city_count = fields.Integer(string='Number of Cities', compute='_compute_city_count', store=True)
    total_city_population = fields.Float(string='Total City Population', compute='_compute_total_city_population')
    
    # Market and business indicators
    ease_of_doing_business_rank = fields.Integer(string='Ease of Doing Business Rank')
    corruption_perception_index = fields.Float(string='Corruption Perception Index', digits=(3, 1))
    human_development_index = fields.Float(string='Human Development Index', digits=(3, 3))
    
    # Financial planning specific
    market_potential = fields.Selection([
        ('very_low', 'Very Low'),
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('very_high', 'Very High')
    ], string='Market Potential', default='medium')
    
    risk_level = fields.Selection([
        ('very_low', 'Very Low Risk'),
        ('low', 'Low Risk'),
        ('medium', 'Medium Risk'),
        ('high', 'High Risk'),
        ('very_high', 'Very High Risk')
    ], string='Investment Risk Level', default='medium')
    
    # Additional info
    capital_city = fields.Char(string='Capital City')
    official_language = fields.Char(string='Official Language')
    time_zone = fields.Char(string='Primary Time Zone')
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)

    @api.depends('city_ids')
    def _compute_city_count(self):
        for country in self:
            country.city_count = len(country.city_ids)

    @api.depends('city_ids.population')
    def _compute_total_city_population(self):
        for country in self:
            country.total_city_population = sum(country.city_ids.mapped('population'))

    @api.onchange('population', 'area_km2')
    def _onchange_population_density(self):
        if self.population and self.area_km2:
            # Convert population from millions to actual number
            self.population_density = (self.population * 1000000) / self.area_km2

    @api.onchange('gdp', 'population')
    def _onchange_gdp_per_capita(self):
        if self.gdp and self.population:
            # GDP is in billions, population in millions
            self.gdp_per_capita = (self.gdp * 1000) / self.population

    def name_get(self):
        result = []
        for country in self:
            name = f"{country.name} ({country.code})"
            result.append((country.id, name))
        return result

    def action_view_cities(self):
        """Action to view cities in this country"""
        self.ensure_one()
        return {
            'name': _('Cities in %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.city',
            'view_mode': 'tree,form',
            'domain': [('country_id', '=', self.id)],
            'context': {'default_country_id': self.id},
        }

    def action_view_growth_rates(self):
        """Action to view historical growth rates"""
        self.ensure_one()
        return {
            'name': _('Growth Rates for %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.growth.rate',
            'view_mode': 'tree,form,graph',
            'domain': [('country_id', '=', self.id)],
            'context': {'default_country_id': self.id},
        }

    def calculate_projected_population(self, target_date):
        """Calculate projected population for a target date"""
        self.ensure_one()
        if not self.population or not self.annual_population_growth_rate:
            return self.population
        
        current_year = date.today().year
        target_year = target_date.year if isinstance(target_date, date) else target_date
        years_diff = target_year - current_year
        
        # Compound growth formula: P(t) = P(0) * (1 + r)^t
        growth_factor = (1 + self.annual_population_growth_rate / 100) ** years_diff
        projected_population = self.population * growth_factor
        
        return projected_population

    def calculate_projected_gdp(self, target_date):
        """Calculate projected GDP for a target date"""
        self.ensure_one()
        if not self.gdp or not self.annual_gdp_growth_rate:
            return self.gdp
        
        current_year = date.today().year
        target_year = target_date.year if isinstance(target_date, date) else target_date
        years_diff = target_year - current_year
        
        # Compound growth formula
        growth_factor = (1 + self.annual_gdp_growth_rate / 100) ** years_diff
        projected_gdp = self.gdp * growth_factor
        
        return projected_gdp

    def get_demographic_summary(self):
        """Get comprehensive demographic summary"""
        self.ensure_one()
        return {
            'country_name': self.name,
            'continent': self.continent_id.name,
            'population': self.population,
            'population_density': self.population_density,
            'gdp': self.gdp,
            'gdp_per_capita': self.gdp_per_capita,
            'population_growth_rate': self.annual_population_growth_rate,
            'gdp_growth_rate': self.annual_gdp_growth_rate,
            'city_count': self.city_count,
            'market_potential': self.market_potential,
            'risk_level': self.risk_level,
        }

    @api.model
    def get_top_countries_by_population(self, limit=10):
        """Get top countries by population"""
        return self.search([('population', '>', 0)], order='population desc', limit=limit)

    @api.model
    def get_top_countries_by_gdp(self, limit=10):
        """Get top countries by GDP"""
        return self.search([('gdp', '>', 0)], order='gdp desc', limit=limit)

    @api.model
    def get_fastest_growing_countries(self, limit=10):
        """Get countries with highest growth rates"""
        return self.search([('annual_gdp_growth_rate', '>', 0)], order='annual_gdp_growth_rate desc', limit=limit)
