# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime, date


class BudgetVsActualReport(models.TransientModel):
    _name = 'budget.vs.actual.report'
    _description = 'Budget vs Actual Report Data'

    name = fields.Char(string='Report Name', required=True)
    date_from = fields.Date(string='Start Date', required=True)
    date_to = fields.Date(string='End Date', required=True)
    budget_id = fields.Many2one('budget.budget', string='Budget')
    account_ids = fields.Many2many('account.account', string='Accounts')
    analytic_account_ids = fields.Many2many('account.analytic.account', string='Analytic Accounts')
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    
    # Report data fields
    line_ids = fields.One2many('budget.vs.actual.report.line', 'report_id', string='Report Lines')
    total_budget_amount = fields.Monetary(string='Total Budget', currency_field='currency_id', compute='_compute_totals')
    total_actual_amount = fields.Monetary(string='Total Actual', currency_field='currency_id', compute='_compute_totals')
    total_variance = fields.Monetary(string='Total Variance', currency_field='currency_id', compute='_compute_totals')
    total_variance_percent = fields.Float(string='Total Variance %', compute='_compute_totals')
    currency_id = fields.Many2one(related='company_id.currency_id')

    @api.depends('line_ids.budget_amount', 'line_ids.actual_amount', 'line_ids.variance')
    def _compute_totals(self):
        for report in self:
            report.total_budget_amount = sum(report.line_ids.mapped('budget_amount'))
            report.total_actual_amount = sum(report.line_ids.mapped('actual_amount'))
            report.total_variance = sum(report.line_ids.mapped('variance'))
            if report.total_budget_amount != 0:
                report.total_variance_percent = (report.total_variance / report.total_budget_amount) * 100
            else:
                report.total_variance_percent = 0.0

    def generate_report_data(self):
        """Generate the budget vs actual report data"""
        self.ensure_one()
        
        # Clear existing lines
        self.line_ids.unlink()
        
        # Get accounts to analyze
        if self.account_ids:
            accounts = self.account_ids
        else:
            # Get all accounts that have budget lines or actual transactions
            budget_accounts = self.env['account.account']
            if self.budget_id:
                budget_accounts = self.budget_id.budget_line.mapped('general_budget_id.account_ids')
            
            actual_accounts = self.env['account.move.line'].search([
                ('date', '>=', self.date_from),
                ('date', '<=', self.date_to),
                ('move_id.state', '=', 'posted')
            ]).mapped('account_id')
            
            accounts = (budget_accounts | actual_accounts).filtered(lambda a: a.company_id == self.company_id)
        
        # Generate report lines
        report_lines = []
        for account in accounts:
            budget_data = account.get_budget_vs_actual_data(
                self.date_from, 
                self.date_to, 
                self.budget_id.id if self.budget_id else None
            )
            
            # Filter by analytic accounts if specified
            if self.analytic_account_ids:
                # Get actual amount filtered by analytic accounts
                move_lines = self.env['account.move.line'].search([
                    ('account_id', '=', account.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to),
                    ('move_id.state', '=', 'posted'),
                    ('analytic_distribution', '!=', False)
                ])
                
                filtered_actual = 0.0
                for line in move_lines:
                    if line.analytic_distribution:
                        for analytic_id in self.analytic_account_ids.ids:
                            if str(analytic_id) in line.analytic_distribution:
                                filtered_actual += line.balance * (line.analytic_distribution[str(analytic_id)] / 100)
                
                budget_data['actual_amount'] = filtered_actual
                budget_data['variance'] = filtered_actual - budget_data['budget_amount']
                budget_data['variance_percent'] = (budget_data['variance'] / budget_data['budget_amount'] * 100) if budget_data['budget_amount'] != 0 else 0.0
            
            report_lines.append((0, 0, {
                'account_id': account.id,
                'account_code': account.code,
                'account_name': account.name,
                'budget_amount': budget_data['budget_amount'],
                'actual_amount': budget_data['actual_amount'],
                'variance': budget_data['variance'],
                'variance_percent': budget_data['variance_percent'],
            }))
        
        self.line_ids = report_lines
        return True


class BudgetVsActualReportLine(models.TransientModel):
    _name = 'budget.vs.actual.report.line'
    _description = 'Budget vs Actual Report Line'

    report_id = fields.Many2one('budget.vs.actual.report', string='Report', ondelete='cascade')
    account_id = fields.Many2one('account.account', string='Account')
    account_code = fields.Char(string='Account Code')
    account_name = fields.Char(string='Account Name')
    budget_amount = fields.Monetary(string='Budget Amount', currency_field='currency_id')
    actual_amount = fields.Monetary(string='Actual Amount', currency_field='currency_id')
    variance = fields.Monetary(string='Variance', currency_field='currency_id')
    variance_percent = fields.Float(string='Variance %')
    currency_id = fields.Many2one(related='report_id.currency_id')
    
    def get_variance_color(self):
        """Return color class based on variance percentage"""
        if self.variance_percent > 10:
            return 'text-success'  # Green for positive variance > 10%
        elif self.variance_percent < -10:
            return 'text-danger'   # Red for negative variance > 10%
        else:
            return 'text-warning'  # Yellow for variance within 10%
