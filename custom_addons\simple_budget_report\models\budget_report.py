# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date


class SimpleBudgetReport(models.TransientModel):
    _name = 'simple.budget.report'
    _description = 'Simple Budget vs Actual Report'

    name = fields.Char(string='Report Name', default='Budget vs Actual Report')
    date_from = fields.Date(string='Start Date', required=True, default=lambda self: date.today().replace(day=1))
    date_to = fields.Date(string='End Date', required=True, default=lambda self: date.today())
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    
    # Report results
    report_html = fields.Html(string='Report', readonly=True)

    def generate_report(self):
        """Generate the budget vs actual report"""
        self.ensure_one()
        
        # Get all accounts with transactions in the period
        move_lines = self.env['account.move.line'].search([
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
            ('move_id.state', '=', 'posted'),
            ('company_id', '=', self.company_id.id)
        ])
        
        # Group by account
        account_data = {}
        for line in move_lines:
            account = line.account_id
            if account.id not in account_data:
                account_data[account.id] = {
                    'account': account,
                    'actual_amount': 0.0,
                    'budget_amount': 0.0
                }
            account_data[account.id]['actual_amount'] += line.balance
        
        # Try to get budget data if available
        try:
            if 'budget.lines' in self.env:
                budget_lines = self.env['budget.lines'].search([
                    ('date_from', '<=', self.date_to),
                    ('date_to', '>=', self.date_from)
                ])
                
                for budget_line in budget_lines:
                    if budget_line.general_budget_id and budget_line.general_budget_id.account_ids:
                        for account in budget_line.general_budget_id.account_ids:
                            if account.id not in account_data:
                                account_data[account.id] = {
                                    'account': account,
                                    'actual_amount': 0.0,
                                    'budget_amount': 0.0
                                }
                            account_data[account.id]['budget_amount'] += budget_line.planned_amount
        except:
            pass  # Budget module not available
        
        # Generate HTML report
        html_content = self._generate_html_report(account_data)
        self.report_html = html_content
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'simple.budget.report',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'show_report': True}
        }

    def _generate_html_report(self, account_data):
        """Generate HTML content for the report"""
        html = """
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-center mb-4">Budget vs Actual Report</h2>
                    <div class="row mb-3">
                        <div class="col-6">
                            <strong>Period:</strong> %s to %s
                        </div>
                        <div class="col-6">
                            <strong>Company:</strong> %s
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <table class="table table-striped table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>Account Code</th>
                                <th>Account Name</th>
                                <th class="text-right">Budget Amount</th>
                                <th class="text-right">Actual Amount</th>
                                <th class="text-right">Variance</th>
                                <th class="text-right">Variance %%</th>
                            </tr>
                        </thead>
                        <tbody>
        """ % (self.date_from, self.date_to, self.company_id.name)
        
        total_budget = 0.0
        total_actual = 0.0
        
        for account_id, data in account_data.items():
            account = data['account']
            budget_amount = data['budget_amount']
            actual_amount = data['actual_amount']
            variance = actual_amount - budget_amount
            variance_percent = (variance / budget_amount * 100) if budget_amount != 0 else 0.0
            
            total_budget += budget_amount
            total_actual += actual_amount
            
            # Color coding for variance
            if variance_percent > 10:
                row_class = 'table-success'
            elif variance_percent < -10:
                row_class = 'table-danger'
            else:
                row_class = 'table-warning'
            
            html += """
                            <tr class="%s">
                                <td>%s</td>
                                <td>%s</td>
                                <td class="text-right">%.2f</td>
                                <td class="text-right">%.2f</td>
                                <td class="text-right">%.2f</td>
                                <td class="text-right">%.2f%%</td>
                            </tr>
            """ % (row_class, account.code, account.name, budget_amount, actual_amount, variance, variance_percent)
        
        total_variance = total_actual - total_budget
        total_variance_percent = (total_variance / total_budget * 100) if total_budget != 0 else 0.0
        
        html += """
                        </tbody>
                        <tfoot class="thead-dark">
                            <tr>
                                <th colspan="2">TOTAL</th>
                                <th class="text-right">%.2f</th>
                                <th class="text-right">%.2f</th>
                                <th class="text-right">%.2f</th>
                                <th class="text-right">%.2f%%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <h4>Variance Analysis</h4>
                    <div class="row">
                        <div class="col-4">
                            <div class="alert alert-success">
                                <h6>Favorable Variance (> 10%%)</h6>
                                <p>Accounts performing better than budget</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="alert alert-warning">
                                <h6>Within Range (±10%%)</h6>
                                <p>Accounts within acceptable variance</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="alert alert-danger">
                                <h6>Unfavorable Variance (< -10%%)</h6>
                                <p>Accounts exceeding budget</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """ % (total_budget, total_actual, total_variance, total_variance_percent)
        
        return html
