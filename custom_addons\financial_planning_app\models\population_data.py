# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date


class PopulationData(models.Model):
    _name = 'financial.planning.population.data'
    _description = 'Population Data for Financial Planning'
    _order = 'year desc, country_id, city_id'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    
    # Geographic reference
    country_id = fields.Many2one('financial.planning.country', string='Country')
    city_id = fields.Many2one('financial.planning.city', string='City')
    continent_id = fields.Many2one('financial.planning.continent', string='Continent')
    
    # Time period
    year = fields.Integer(string='Year', required=True, default=lambda self: date.today().year)
    quarter = fields.Selection([
        ('Q1', 'Q1'),
        ('Q2', 'Q2'),
        ('Q3', 'Q3'),
        ('Q4', 'Q4')
    ], string='Quarter')
    
    # Population metrics
    total_population = fields.Float(string='Total Population (M)', help='Total population in millions')
    urban_population = fields.Float(string='Urban Population (M)', help='Urban population in millions')
    rural_population = fields.Float(string='Rural Population (M)', help='Rural population in millions')
    population_density = fields.Float(string='Population Density', help='People per km²')
    
    # Demographic breakdown
    population_0_14 = fields.Float(string='Population 0-14 years (%)', digits=(5, 2))
    population_15_64 = fields.Float(string='Population 15-64 years (%)', digits=(5, 2))
    population_65_plus = fields.Float(string='Population 65+ years (%)', digits=(5, 2))
    
    # Economic demographics
    working_age_population = fields.Float(string='Working Age Population (M)', help='Population aged 15-64 in millions')
    labor_force_participation = fields.Float(string='Labor Force Participation (%)', digits=(5, 2))
    employment_rate = fields.Float(string='Employment Rate (%)', digits=(5, 2))
    
    # Income and spending
    median_income = fields.Monetary(string='Median Income', currency_field='currency_id')
    average_household_size = fields.Float(string='Average Household Size', digits=(3, 2))
    consumer_spending_per_capita = fields.Monetary(string='Consumer Spending per Capita', currency_field='currency_id')
    
    # Technology and connectivity
    internet_penetration = fields.Float(string='Internet Penetration (%)', digits=(5, 2))
    mobile_penetration = fields.Float(string='Mobile Penetration (%)', digits=(5, 2))
    smartphone_penetration = fields.Float(string='Smartphone Penetration (%)', digits=(5, 2))
    
    # Education and literacy
    literacy_rate = fields.Float(string='Literacy Rate (%)', digits=(5, 2))
    tertiary_education_rate = fields.Float(string='Tertiary Education Rate (%)', digits=(5, 2))
    
    # Data source and quality
    data_source = fields.Selection([
        ('census', 'National Census'),
        ('survey', 'Statistical Survey'),
        ('world_bank', 'World Bank'),
        ('un', 'United Nations'),
        ('oecd', 'OECD'),
        ('estimated', 'Estimated'),
        ('projected', 'Projected')
    ], string='Data Source', default='census')
    
    data_quality = fields.Selection([
        ('high', 'High Quality'),
        ('medium', 'Medium Quality'),
        ('low', 'Low Quality'),
        ('estimated', 'Estimated')
    ], string='Data Quality', default='high')
    
    # Currency and company
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                 default=lambda self: self.env.company.currency_id)
    company_id = fields.Many2one('res.company', string='Company', 
                                default=lambda self: self.env.company)
    
    # Additional fields
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)

    @api.depends('country_id', 'city_id', 'year', 'quarter')
    def _compute_name(self):
        for record in self:
            parts = []
            if record.city_id:
                parts.append(record.city_id.name)
            elif record.country_id:
                parts.append(record.country_id.name)
            elif record.continent_id:
                parts.append(record.continent_id.name)
            
            parts.append(str(record.year))
            if record.quarter:
                parts.append(record.quarter)
            
            record.name = ' - '.join(parts) if parts else 'New Population Data'

    @api.onchange('total_population', 'population_15_64')
    def _onchange_working_age_population(self):
        if self.total_population and self.population_15_64:
            self.working_age_population = self.total_population * (self.population_15_64 / 100)

    @api.onchange('urban_population', 'rural_population')
    def _onchange_total_population(self):
        if self.urban_population and self.rural_population:
            self.total_population = self.urban_population + self.rural_population

    @api.constrains('population_0_14', 'population_15_64', 'population_65_plus')
    def _check_population_percentages(self):
        for record in self:
            if record.population_0_14 and record.population_15_64 and record.population_65_plus:
                total = record.population_0_14 + record.population_15_64 + record.population_65_plus
                if abs(total - 100) > 1:  # Allow 1% tolerance
                    raise models.ValidationError(_('Population age group percentages must sum to 100%'))

    def name_get(self):
        result = []
        for record in self:
            name = record.name
            result.append((record.id, name))
        return result

    @api.model
    def get_latest_population_data(self, country_id=None, city_id=None):
        """Get the most recent population data for a country or city"""
        domain = [('year', '>', 0)]
        if country_id:
            domain.append(('country_id', '=', country_id))
        if city_id:
            domain.append(('city_id', '=', city_id))
        
        return self.search(domain, order='year desc, quarter desc', limit=1)

    @api.model
    def get_population_trend(self, country_id=None, city_id=None, years=5):
        """Get population trend data for analysis"""
        domain = [('year', '>', 0)]
        if country_id:
            domain.append(('country_id', '=', country_id))
        if city_id:
            domain.append(('city_id', '=', city_id))
        
        current_year = date.today().year
        domain.append(('year', '>=', current_year - years))
        
        return self.search(domain, order='year asc')

    def calculate_growth_rate(self, previous_record):
        """Calculate population growth rate compared to previous record"""
        self.ensure_one()
        if not previous_record or not previous_record.total_population or not self.total_population:
            return 0.0
        
        years_diff = self.year - previous_record.year
        if years_diff <= 0:
            return 0.0
        
        # Calculate compound annual growth rate (CAGR)
        growth_rate = ((self.total_population / previous_record.total_population) ** (1/years_diff) - 1) * 100
        return growth_rate

    def get_demographic_profile(self):
        """Get comprehensive demographic profile"""
        self.ensure_one()
        return {
            'location': self.name,
            'year': self.year,
            'total_population': self.total_population,
            'urban_population': self.urban_population,
            'rural_population': self.rural_population,
            'population_density': self.population_density,
            'working_age_population': self.working_age_population,
            'labor_force_participation': self.labor_force_participation,
            'employment_rate': self.employment_rate,
            'median_income': self.median_income,
            'consumer_spending_per_capita': self.consumer_spending_per_capita,
            'internet_penetration': self.internet_penetration,
            'mobile_penetration': self.mobile_penetration,
            'smartphone_penetration': self.smartphone_penetration,
            'literacy_rate': self.literacy_rate,
            'data_source': self.data_source,
            'data_quality': self.data_quality,
        }

    @api.model
    def import_population_data(self, data_list):
        """Import population data from external sources"""
        created_records = self.env['financial.planning.population.data']
        
        for data in data_list:
            # Validate required fields
            if not data.get('year') or not data.get('total_population'):
                continue
            
            # Check if record already exists
            domain = [('year', '=', data['year'])]
            if data.get('country_id'):
                domain.append(('country_id', '=', data['country_id']))
            if data.get('city_id'):
                domain.append(('city_id', '=', data['city_id']))
            
            existing = self.search(domain, limit=1)
            if existing:
                # Update existing record
                existing.write(data)
                created_records |= existing
            else:
                # Create new record
                new_record = self.create(data)
                created_records |= new_record
        
        return created_records
