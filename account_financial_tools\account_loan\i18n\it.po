# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_loan
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-03-30 02:39+0000\n"
"PO-Revision-Date: 2025-06-30 13:25+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.10.4\n"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid ""
"<span class=\"o_stat_text\">Deduct</span>\n"
"                                <span class=\"o_stat_text\">Debt</span>"
msgstr ""
"<span class=\"o_stat_text\">Deduzione</span>\n"
"                                <span class=\"o_stat_text\">Debito</span>"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid ""
"<span class=\"o_stat_text\">Increase</span>\n"
"                                <span class=\"o_stat_text\">Debt</span>"
msgstr ""
"<span class=\"o_stat_text\">Incremento</span>\n"
"                                <span class=\"o_stat_text\">Debito</span>"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.res_partner_form_view
msgid "<span class=\"o_stat_text\">Loans</span>"
msgstr "<span class=\"o_stat_text\">Prestiti</span>"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__account_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__account_id
msgid "Account"
msgstr "Conto"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__date
msgid "Account Date"
msgstr "Data addebito"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__long_term_loan_account_id
#: model:ir.model.fields,help:account_loan.field_account_loan_line__long_term_loan_account_id
msgid "Account that will contain the pending amount on Long term"
msgstr "Conto che conterrà l'importo sospeso a lungo termine"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__short_term_loan_account_id
#: model:ir.model.fields,help:account_loan.field_account_loan_line__short_term_loan_account_id
msgid "Account that will contain the pending amount on short term"
msgstr "Conto che conterrà l'importo sospeso a corto termine"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__interest_expenses_account_id
#: model:ir.model.fields,help:account_loan.field_account_loan_line__interest_expenses_account_id
msgid "Account where the interests will be assigned to"
msgstr "Conto a cui verrano assegnati gli interessi"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Accounts"
msgstr "Conti"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo attività"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_pay_amount.py:0
#, python-format
msgid "Amount cannot be bigger than debt"
msgstr "L'importo non può essere maggiore del debito"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_increase_amount.py:0
#: code:addons/account_loan/wizards/account_loan_pay_amount.py:0
#, python-format
msgid "Amount cannot be less than zero"
msgstr "L'importo non può essere meno di zero"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/models/account_loan_line.py:0
#, python-format
msgid "Amount cannot be recomputed if moves or invoices exists already"
msgstr ""
"L'importo non può essere ricalcolato se movimenti o fatture già esistono"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__interests_amount
msgid "Amount of the payment that will be assigned to interests"
msgstr "L'importo del pagamento che verrà assegnato agli interessi"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__principal_amount
msgid "Amount of the payment that will reduce the pending loan amount"
msgstr "L'importo del pagamento che ridurrà l'importo del prestito in sospeso"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__long_term_principal_amount
msgid "Amount that will reduce the pending loan amount on long term"
msgstr ""
"L'importo che ridurrà l'importo del prestito a lungo termine in sospeso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__amount
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__amount
msgid "Amount to reduce from Principal"
msgstr "Importo da ridurre dal capitale"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_loan_line
msgid "Annuity"
msgstr "Tasso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__fees
msgid "Bank fees"
msgstr "Tasse bancarie"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_generate_wizard_form
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_increase_amount_form_view
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_pay_amount_form
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_post_form
msgid "Cancel"
msgstr "Annulla"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__cancel_loan
msgid "Cancel Loan"
msgstr "Annulla Prestito"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__state__cancelled
msgid "Cancelled"
msgstr "Annullato"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_generate_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"Scegli il periodo per il quale si desidera registrare automaticamente le "
"righe di ammortamento dei cespiti in esecuzione"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__state__closed
msgid "Closed"
msgstr "Chiuso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__company_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__company_id
msgid "Company"
msgstr "Azienda"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__partner_id
#: model:ir.model.fields,help:account_loan.field_account_loan_line__partner_id
msgid "Company or individual that lends the money at an interest rate."
msgstr "Azienda o Persona che presta i soldi con un tasso di interesse."

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Compute items"
msgstr "Calcola prestito"

#. module: account_loan
#: model:ir.model,name:account_loan.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__create_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__create_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__create_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__create_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__create_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__create_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__create_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__create_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__create_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__create_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__create_date
msgid "Created on"
msgstr "Creato il"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__currency_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__currency_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__currency_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__rate
msgid "Currently applied rate"
msgstr "Tasso interesse attualmente applicato"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__date
msgid "Date"
msgstr "Data"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__date
msgid "Date when the payment will be accounted"
msgstr "Data in cui verrà contabilizzato il pagamento"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__display_name
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__display_name
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__display_name
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__display_name
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__display_name
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__state__draft
msgid "Draft"
msgstr "Bozza"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__rate_type__ear
msgid "EAR"
msgstr "TAN"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_lines_view
msgid "Edit"
msgstr "Modifica"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__final_pending_principal_amount
msgid "Final Pending Principal Amount"
msgstr "Capitale residuo finale"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__fixed_amount
msgid "Fixed Amount"
msgstr "Importo fisso"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__loan_type__fixed-annuity
msgid "Fixed Annuity"
msgstr "Tasso fisso"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__loan_type__fixed-annuity-begin
msgid "Fixed Annuity Begin"
msgstr "Inizio tasso fisso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__fixed_loan_amount
msgid "Fixed Loan Amount"
msgstr "Importo prestito fisso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__fixed_periods
msgid "Fixed Periods"
msgstr "Periodi fissi"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__loan_type__fixed-principal
msgid "Fixed Principal"
msgstr "Capitale fisso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: account_loan
#: model:ir.ui.menu,name:account_loan.account_loan_generate_wizard_menu
msgid "Generate Loan Entries"
msgstr "Genera movimenti prestiti"

#. module: account_loan
#: model:ir.actions.act_window,name:account_loan.account_loan_generate_wizard_action
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_generate_wizard_form
msgid "Generate moves"
msgstr "Genera movimenti"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.view_account_loan_lines_search
msgid "Group by..."
msgstr "Raggruppa per..."

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__has_invoices
msgid "Has Invoices"
msgstr "Ha fatture"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__has_message
msgid "Has Message"
msgstr "Ha un messaggio"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__has_moves
msgid "Has Moves"
msgstr "Ha movimenti"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_res_partner__lended_loan_count
#: model:ir.model.fields,help:account_loan.field_res_users__lended_loan_count
msgid "How many Loans this partner lended to us ?"
msgstr "Quanti prestiti ci ha concesso questo partner?"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__id
msgid "ID"
msgstr "ID"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi hanno un errore di consegna."

#. module: account_loan
#: model:ir.actions.act_window,name:account_loan.account_loan_increase_amount_act_window
msgid "Increase Amount"
msgstr "Aumenta valore"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_loan_increase_amount
msgid "Increase the debt of a loan"
msgstr "Aumenta il debito di un prestito"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__interests_product_id
msgid "Interest product"
msgstr "Prodotto interesse"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__interests_amount
msgid "Interests Amount"
msgstr "Importo interessi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__interest_expenses_account_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__interest_expenses_account_id
msgid "Interests account"
msgstr "Conto Interessi"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Invoices"
msgstr "Fatture"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__post_invoice
msgid "Invoices will be posted automatically"
msgstr "La fatture verranno registrate automaticamente"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_is_follower
msgid "Is Follower"
msgstr "Segue"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__is_leasing
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__is_leasing
msgid "Is Leasing"
msgstr "Leasing"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/models/account_loan.py:0
#, python-format
msgid ""
"It is only possible to change to draft if the status is cancelled or posted "
"and there are no account moves."
msgstr ""
"È possibile mettere a bozza solo se lo stato è annullato o inserito e non ci "
"sono movimenti contabili."

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Items"
msgstr "Piano"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__journal_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__journal_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__journal_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__journal_id
msgid "Journal"
msgstr "Registro"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__journal_type
msgid "Journal Type"
msgstr "Tipo registro"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__write_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__write_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__write_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__write_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__write_uid
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__write_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__write_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__write_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__write_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__write_date
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__leased_asset_account_id
msgid "Leased Asset Account"
msgstr "Conto Leasing"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Leasing"
msgstr "Leasing"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan_generate_wizard__loan_type__leasing
msgid "Leasings"
msgstr "Leasing"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_res_partner__lended_loan_ids
#: model:ir.model.fields,field_description:account_loan.field_res_users__lended_loan_ids
msgid "Lended Loan"
msgstr "Prestito concesso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_res_partner__lended_loan_count
#: model:ir.model.fields,field_description:account_loan.field_res_users__lended_loan_count
msgid "Lended Loan Count"
msgstr "Conteggio prestiti concessi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__partner_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__partner_id
msgid "Lender"
msgstr "Ente erogante"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__line_ids
msgid "Line"
msgstr "Riga"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_bank_statement_line__loan_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_increase_amount__loan_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__loan_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_pay_amount__loan_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_post__loan_id
#: model:ir.model.fields,field_description:account_loan.field_account_move__loan_id
#: model:ir.model.fields,field_description:account_loan.field_account_payment__loan_id
#: model_terms:ir.ui.view,arch_db:account_loan.view_account_loan_lines_search
msgid "Loan"
msgstr "Prestito"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__loan_amount
msgid "Loan Amount"
msgstr "Importo prestito"

#. module: account_loan
#: model:ir.actions.act_window,name:account_loan.account_loan_lines_action
#: model:ir.ui.menu,name:account_loan.account_loan_lines_menu
msgid "Loan Items"
msgstr "Righe prestito"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_bank_statement_line__loan_line_id
#: model:ir.model.fields,field_description:account_loan.field_account_move__loan_line_id
#: model:ir.model.fields,field_description:account_loan.field_account_payment__loan_line_id
msgid "Loan Line"
msgstr "Riga prestito"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__loan_type
#: model:ir.model.fields,field_description:account_loan.field_account_loan_generate_wizard__loan_type
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__loan_type
msgid "Loan Type"
msgstr "Tipo di Prestito"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_loan_generate_wizard
msgid "Loan generate wizard"
msgstr "Procedura guidata per la generazione del prestito"

#. module: account_loan
#: model:ir.model.constraint,message:account_loan.constraint_account_loan_name_uniq
msgid "Loan name must be unique"
msgstr "Il Nome del prestito deve essere univoco"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_loan_pay_amount
msgid "Loan pay amount"
msgstr "Importo del prestito"

#. module: account_loan
#: model:ir.model,name:account_loan.model_account_loan_post
msgid "Loan post"
msgstr "Registra prestito"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__product_id
msgid "Loan product"
msgstr "Prodotto prestito"

#. module: account_loan
#: model:ir.actions.act_window,name:account_loan.account_loan_action
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan_generate_wizard__loan_type__loan
#: model:ir.ui.menu,name:account_loan.account_loan_menu
#: model:ir.ui.menu,name:account_loan.loan_menu
msgid "Loans"
msgstr "Prestiti"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__long_term_pending_principal_amount
msgid "Long Term Pending Principal Amount"
msgstr "Importo sospeso entro 12 mesi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__long_term_principal_amount
msgid "Long Term Principal Amount"
msgstr "Importo sospeso oltre 12 mesi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__long_term_loan_account_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__long_term_loan_account_id
msgid "Long term account"
msgstr "Conto a lungo termine"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__rate_type
msgid "Method of computation of the applied rate"
msgstr "Metodo di calcolo dei tassi applicati"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__loan_type
#: model:ir.model.fields,help:account_loan.field_account_loan_line__loan_type
msgid "Method of computation of the period annuity"
msgstr "Metodo di calcolo del periodo di rendita"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__move_ids
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__move_ids
msgid "Move"
msgstr "Movimento"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__move_count
msgid "Move Count"
msgstr "Conteggio movimenti"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Moves"
msgstr "Movimenti"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mia attività"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__name
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__name
msgid "Name"
msgstr "Nome"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo prossima attività"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__rate_type__napr
msgid "Nominal APR"
msgstr "TAEG"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__periods
msgid "Number of periods that the loan will last"
msgstr "Numero in rate della durata del prestito"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__loan_type__interest
msgid "Only interest"
msgstr "Solo interessi"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_post.py:0
#, python-format
msgid "Only loans in draft state can be posted"
msgstr "Solo i prestiti in stato bozza possono essere registrati"

#. module: account_loan
#: model:ir.actions.act_window,name:account_loan.account_loan_pay_amount_action
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_pay_amount_form
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_post_form
msgid "Pay amount"
msgstr "Paga importo"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__payment_amount
msgid "Payment Amount"
msgstr "Importo pagamento"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__payment_on_first_period
msgid "Payment On First Period"
msgstr "Pagamento su prima rata"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__pending_principal_amount
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__pending_principal_amount
msgid "Pending Principal Amount"
msgstr "Capitale residuo"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__final_pending_principal_amount
msgid "Pending amount of the loan after the payment"
msgstr "L'importo in sospeso del prestito dopo il pagamento"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__pending_principal_amount
msgid "Pending amount of the loan before the payment"
msgstr "L'importo in sospeso del prestito prima del pagamento"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__long_term_pending_principal_amount
msgid ""
"Pending amount of the loan before the payment that will not be payed in, at "
"least, 12 months"
msgstr ""
"L'importo in sospeso del prestito prima del pagamento che non sarà pagato "
"entro, in almeno, 12 mesi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__method_period
msgid "Period Length"
msgstr "Durata periodo"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__periods
msgid "Periods"
msgstr "Numero rate"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Post"
msgstr "Registra"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__post_invoice
msgid "Post Invoice"
msgstr "Registra Fattura"

#. module: account_loan
#: model:ir.actions.act_window,name:account_loan.account_loan_post_action
msgid "Post loan"
msgstr "Registra Prestito"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__state__posted
msgid "Posted"
msgstr "Registrato"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__principal_amount
msgid "Principal Amount"
msgstr "Quota capitale"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_line_tree
msgid "Process"
msgstr "Registra"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__interests_product_id
msgid ""
"Product where the amount of interests will be assigned when the invoice is "
"created"
msgstr ""
"Prodotto a cui verrà assegnato l'importo degli interessi al momento della "
"creazione della fattura"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__product_id
msgid ""
"Product where the amount of the loan will be assigned when the invoice is "
"created"
msgstr ""
"Prodotto a cui verrà assegnato l'importo del prestito al momento della "
"creazione della fattura"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__rate
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__rate
msgid "Rate"
msgstr "Tasso"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/models/account_loan.py:0
#, python-format
msgid "Rate Change"
msgstr "Modifica tasso"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__rate_period
msgid "Rate Period"
msgstr "Tasso periodo"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__rate_type
msgid "Rate Type"
msgstr "Tipo Tasso"

#. module: account_loan
#: model:ir.model.fields.selection,name:account_loan.selection__account_loan__rate_type__real
msgid "Real rate"
msgstr "Tasso reale"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__rate_period
msgid "Real rate that will be applied on each period"
msgstr "Tasso reale che sarà applicato ogni rata"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_form
msgid "Reset to draft"
msgstr "Reimposta a bozza"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__residual_amount
msgid "Residual Amount"
msgstr "Importo residuo"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__residual_amount
msgid ""
"Residual amount of the lease that must be payed on the end in order to "
"acquire the asset"
msgstr "Costo del riscatto"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__round_on_end
msgid "Round On End"
msgstr "Arrotondare alla fine"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_generate_wizard_form
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_increase_amount_form_view
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_pay_amount_form
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_post_form
msgid "Run"
msgstr "Esecuzione"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__short_term_loan_account_id
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__short_term_loan_account_id
msgid "Short term account"
msgstr "Conto a corto termine"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_increase_amount.py:0
#: code:addons/account_loan/wizards/account_loan_pay_amount.py:0
#, python-format
msgid "Some future invoices already exists"
msgstr "Alcune fatture future già esistono"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_increase_amount.py:0
#: code:addons/account_loan/wizards/account_loan_pay_amount.py:0
#, python-format
msgid "Some future moves already exists"
msgstr "Alcuni Movimenti futuri già esistono"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_increase_amount.py:0
#: code:addons/account_loan/wizards/account_loan_pay_amount.py:0
#, python-format
msgid "Some invoices are not created"
msgstr "Alcune fatture non vengono create"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/models/account_loan_line.py:0
#, python-format
msgid "Some invoices must be created first"
msgstr "Alcune fatture devono essere create prima"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_increase_amount.py:0
#: code:addons/account_loan/wizards/account_loan_pay_amount.py:0
#, python-format
msgid "Some moves are not created"
msgstr "Alcuni movimenti non vengono creati"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/models/account_loan_line.py:0
#, python-format
msgid "Some moves must be created first"
msgstr "Alcuni movimenti devono essere creati prima"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__start_date
msgid "Start Date"
msgstr "Data inizio"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__start_date
msgid "Start of the moves"
msgstr "inizio dei movimenti"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__state
#: model:ir.model.fields,field_description:account_loan.field_account_loan_line__loan_state
msgid "State"
msgstr "Stato"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Calcolo periodo tra 2 rate, in mesi"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato in base alle attività\n"
"Scaduto: la data richiesta è trascorsa\n"
"Oggi: la data attività è oggi\n"
"Pianificato: attività future."

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/wizards/account_loan_post.py:0
#, python-format
msgid "The total principal amount does not match the loan amount."
msgstr "Il valore totale del capitale non corrisponde al valore del prestito."

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan_line__payment_amount
msgid "Total amount that will be payed (Annuity)"
msgstr "Importo totale che verrà pagato (Tasso)"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_line_tree
msgid "Total interests"
msgstr "Totale Interessi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__interests_amount
msgid "Total interests payed"
msgstr "Totale pagato interessi"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__payment_amount
msgid "Total payed amount"
msgstr "Totale capitale pagato"

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_line_tree
msgid "Total payments"
msgstr "Totale Pagamenti"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_line_tree
msgid "Values"
msgstr "Valori"

#. module: account_loan
#: model:ir.model.fields,field_description:account_loan.field_account_loan__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__website_message_ids
msgid "Website communication history"
msgstr "Storico comunicazioni sito web"

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__round_on_end
msgid ""
"When checked, the differences will be applied on the last period, if it is "
"unchecked, the annuity will be recalculated on each period."
msgstr ""
"Quando spuntato, le differenze saranno applicate nell'ultima rata, se non è "
"spuntato, il tasso verrà ricalcolato su ogni rata."

#. module: account_loan
#: model:ir.model.fields,help:account_loan.field_account_loan__payment_on_first_period
msgid "When checked, the first payment will be on start date"
msgstr "Quando spuntato, il primo pagamento sarà alla data inizio"

#. module: account_loan
#. odoo-python
#: code:addons/account_loan/models/account_loan.py:0
#, python-format
msgid ""
"You have modified the interest rate. Click the Compute items button to "
"update the lines. Please note that if you have manually edited these lines, "
"those changes will be lost upon computation."
msgstr ""
"È stato modificato il tasso di interesse. Fare clic sul pulsante calcola "
"articoli per aggiornare le righe. Tenere presente che se si è modificato "
"manualmente queste righe, le modifiche andranno perse durante il calcolo."

#. module: account_loan
#: model_terms:ir.ui.view,arch_db:account_loan.account_loan_line_tree
msgid "principal_amount"
msgstr "principal_amount"

#~ msgid "Sequence must be unique in a loan"
#~ msgstr "La sequenza deve essere univoco in un prestito"

#~ msgid "Last Modified on"
#~ msgstr "Ultima modifica il"

#~ msgid "Main Attachment"
#~ msgstr "Allegato principale"

#~ msgid "or"
#~ msgstr "o"
