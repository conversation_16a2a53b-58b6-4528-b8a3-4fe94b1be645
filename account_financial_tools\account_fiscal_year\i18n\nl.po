# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fiscal_year
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-04-14 21:46+0000\n"
"Last-Translator: Bosd <<EMAIL>>\n"
"Language-Team: none\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_fiscal_year
#: model_terms:ir.actions.act_window,help:account_fiscal_year.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Klik hier om een nieuw fiscaal jaar aan te maken."

#. module: account_fiscal_year
#: model:ir.model,name:account_fiscal_year.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__create_date
msgid "Created on"
msgstr "aangemaakt op"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__display_name
msgid "Display Name"
msgstr "Weergavenaam"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "Einddatum"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_res_company__fiscal_year_date_to
msgid "End Date of the Fiscal Year"
msgstr ""

#. module: account_fiscal_year
#: model:ir.model.fields,help:account_fiscal_year.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Einddatum, opnemen in het fiscale jaar."

#. module: account_fiscal_year
#: model:ir.model,name:account_fiscal_year.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "Fiscaal jaar"

#. module: account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_fiscal_year.account_fiscal_year_form_view
msgid "Fiscal Year 2020"
msgstr "Fiscaal jaar 2020"

#. module: account_fiscal_year
#: model:ir.actions.act_window,name:account_fiscal_year.actions_account_fiscal_year
#: model:ir.ui.menu,name:account_fiscal_year.menu_actions_account_fiscal_year
msgid "Fiscal Years"
msgstr "Fiscale jaren"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__id
msgid "ID"
msgstr "ID"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__name
msgid "Name"
msgstr "Naam"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "Start Datum"

#. module: account_fiscal_year
#: model:ir.model.fields,field_description:account_fiscal_year.field_res_company__fiscal_year_date_from
msgid "Start Date of the Fiscal Year"
msgstr ""

#. module: account_fiscal_year
#: model:ir.model.fields,help:account_fiscal_year.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Begindatum, opgenomen in het fiscale jaar."

#. module: account_fiscal_year
#. odoo-python
#: code:addons/account_fiscal_year/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "De einddatum kan niet eerder zijn dan de start datum."

#. module: account_fiscal_year
#. odoo-python
#: code:addons/account_fiscal_year/models/account_fiscal_year.py:0
#, python-format
msgid ""
"This fiscal year '{fy}' overlaps with '{overlapping_fy}'.\n"
"Please correct the start and/or end dates of your fiscal years."
msgstr ""
"Dit fiscale jaar '{fy}' overlap met '{overlapping_fy}'.\n"
"Corrigeer de begin en/of eind data van de fiscale jaren."

#~ msgid "Last Modified on"
#~ msgstr "Laatst bijgewerkt op"
