# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_daily_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 06:44+0000\n"
"PO-Revision-Date: 2022-04-15 06:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>End Date:</strong>"
msgstr "<strong><PERSON><PERSON><PERSON>:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Journals:</strong>"
msgstr "<strong>Defterler:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Sıralama:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Başlangıç Tarihi:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Hedef Hareketler:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
msgid "Account Bank Book"
msgstr "Hesap Banka Defteri"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "Account Cash Book"
msgstr "Hesap Nakit Defteri"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Account Day Book"
msgstr "Hesap Yevmiye Defteri"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__account_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__account_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__account_ids
msgid "Accounts"
msgstr "Hesaplar"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__all
msgid "All"
msgstr "Tümü"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__target_move__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__target_move__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_daybook_report__target_move__all
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "All Entries"
msgstr "Tüm Kayıtlar"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Balance"
msgstr "Bakiye"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_bankbook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_bank_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_bankbook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_bankbook
msgid "Bank Book"
msgstr "Banka Defteri"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_bankbook_report
msgid "Bank Book Report"
msgstr "Banka Defteri Raporu"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Cancel"
msgstr "İptal"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_cashbook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_cash_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_cashbook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_cashbook
msgid "Cash Book"
msgstr "Nakit Defteri"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_cashbook_report
msgid "Cash Book Report"
msgstr "Nakit Defteri Raporu"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__create_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__create_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__create_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__create_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__create_date
msgid "Created on"
msgstr "Oluşturulma Tarihi"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Credit"
msgstr "Alacak"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Currency"
msgstr "Para Birimi"

#. module: om_account_daily_reports
#: model:ir.ui.menu,name:om_account_daily_reports.menu_finance_daily_reports
msgid "Daily Reports"
msgstr "Günlük Raporlar"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__sortby__sort_date
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__sortby__sort_date
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Date"
msgstr "Tarih"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_daybook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_day_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_daybook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_daybook
msgid "Day Book"
msgstr "Yevmiye Defteri"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_daybook_report
msgid "Day Book Report"
msgstr "Yevmiye Defteri Raporu"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Debit"
msgstr "Borç"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__display_account
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__display_account
msgid "Display Accounts"
msgstr "Görüntülenen Hesaplar"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__display_name
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__display_name
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__display_name
msgid "Display Name"
msgstr "Görüntülenen Ad"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__date_to
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__date_to
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__date_to
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Entry Label"
msgstr "Giriş Etiketi"

#. module: om_account_daily_reports
#: code:addons/om_account_daily_reports/report/report_bankbook.py:0
#: code:addons/om_account_daily_reports/report/report_cashbook.py:0
#: code:addons/om_account_daily_reports/report/report_daybook.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__id
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__id
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__id
msgid "ID"
msgstr ""

#. module: om_account_daily_reports
#: model:ir.model.fields,help:om_account_daily_reports.field_account_bankbook_report__initial_balance
#: model:ir.model.fields,help:om_account_daily_reports.field_account_cashbook_report__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Şayet tarih seçtiyseniz, bu alan size ayarladığınız filtreden önce "
"borç/alacak/bakiye miktarını görüntülemek için bir satır eklemenize izin "
"verir."

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__initial_balance
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__initial_balance
msgid "Include Initial Balances"
msgstr "Açılış Bakiyelerini Dahil Et"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "JRNL"
msgstr "DFTR"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr "Defter & İş Ortağı"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "Journal and Partner"
msgstr "Defter ve İş Ortağı"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__journal_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__journal_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__journal_ids
msgid "Journals"
msgstr "Defterler"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report____last_update
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report____last_update
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report____last_update
msgid "Last Modified on"
msgstr "Son Değişiklik Tarihi"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__write_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__write_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__write_uid
msgid "Last Updated by"
msgstr "Son Güncellemeyi Yapan"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__write_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__write_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme Tarihi"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Move"
msgstr "Taşı"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Partner"
msgstr "İş Ortağı"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__target_move__posted
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__target_move__posted
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_daybook_report__target_move__posted
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Posted Entries"
msgstr "Gönderilen Kayıtlar"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Print"
msgstr "Yazdır"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Ref"
msgstr ""

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Report Options"
msgstr "Rapor Seçenekleri"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__sortby
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__sortby
msgid "Sort by"
msgstr "Sıralama"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__date_from
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__date_from
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__date_from
msgid "Start Date"
msgstr "Başlangıç Tarihi"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__target_move
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__target_move
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__target_move
msgid "Target Moves"
msgstr "Hedef Hareketler"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__not_zero
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "Sıfırdan farklı bakiyelerle"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__movement
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__movement
msgid "With movements"
msgstr "Taşımalarla Birlikte"
