# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date
import logging

_logger = logging.getLogger(__name__)


class IFRSFinancialStatement(models.Model):
    _name = 'ifrs.financial.statement'
    _description = 'IFRS Financial Statement'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_from desc'
    _rec_name = 'name'

    name = fields.Char(
        string='Statement Name',
        required=True,
        default=lambda self: _('IFRS Financial Statement %s') % fields.Date.today()
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        required=True,
        default=lambda self: self.env.company
    )
    
    statement_type = fields.Selection([
        ('balance_sheet', 'Statement of Financial Position (Balance Sheet)'),
        ('income_statement', 'Statement of Comprehensive Income'),
        ('cash_flow', 'Statement of Cash Flows'),
        ('equity_changes', 'Statement of Changes in Equity'),
        ('notes', 'Notes to Financial Statements'),
        ('complete_set', 'Complete Set of Financial Statements')
    ], string='Statement Type', required=True, default='complete_set', tracking=True)
    
    reporting_period = fields.Selection([
        ('quarterly', 'Quarterly'),
        ('half_yearly', 'Half-Yearly'),
        ('annual', 'Annual')
    ], string='Reporting Period', required=True, default='annual')
    
    date_from = fields.Date(
        string='Period From',
        required=True,
        default=lambda self: fields.Date.today().replace(month=1, day=1)
    )
    
    date_to = fields.Date(
        string='Period To',
        required=True,
        default=lambda self: fields.Date.today().replace(month=12, day=31)
    )
    
    comparative_period = fields.Boolean(
        string='Include Comparative Period',
        default=True,
        help="Include previous period figures for comparison"
    )
    
    comparative_date_from = fields.Date(
        string='Comparative Period From',
        compute='_compute_comparative_dates',
        store=True
    )
    
    comparative_date_to = fields.Date(
        string='Comparative Period To',
        compute='_compute_comparative_dates',
        store=True
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Reporting Currency',
        required=True,
        default=lambda self: self.env.company.currency_id
    )
    
    functional_currency_id = fields.Many2one(
        'res.currency',
        string='Functional Currency',
        required=True,
        default=lambda self: self.env.company.currency_id
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('review', 'Under Review'),
        ('approved', 'Approved'),
        ('published', 'Published')
    ], string='Status', default='draft', tracking=True)
    
    ifrs_compliance_level = fields.Selection([
        ('full', 'Full IFRS Compliance'),
        ('ifrs_sme', 'IFRS for SMEs'),
        ('local_gaap', 'Local GAAP with IFRS Adjustments')
    ], string='IFRS Compliance Level', required=True, default='full')
    
    # Statement Lines
    statement_line_ids = fields.One2many(
        'ifrs.statement.line',
        'statement_id',
        string='Statement Lines'
    )
    
    # Compliance and Audit
    compliance_check_ids = fields.One2many(
        'ifrs.compliance.check',
        'statement_id',
        string='Compliance Checks'
    )
    
    auditor_id = fields.Many2one(
        'res.partner',
        string='External Auditor',
        domain=[('is_company', '=', True)]
    )
    
    audit_opinion = fields.Selection([
        ('unqualified', 'Unqualified Opinion'),
        ('qualified', 'Qualified Opinion'),
        ('adverse', 'Adverse Opinion'),
        ('disclaimer', 'Disclaimer of Opinion')
    ], string='Audit Opinion')
    
    # Notes and Documentation
    notes = fields.Html(string='Notes to Financial Statements')
    internal_notes = fields.Text(string='Internal Notes')
    
    # Approval Workflow
    prepared_by = fields.Many2one('res.users', string='Prepared By', default=lambda self: self.env.user)
    reviewed_by = fields.Many2one('res.users', string='Reviewed By')
    approved_by = fields.Many2one('res.users', string='Approved By')
    
    preparation_date = fields.Datetime(string='Preparation Date', default=fields.Datetime.now)
    review_date = fields.Datetime(string='Review Date')
    approval_date = fields.Datetime(string='Approval Date')
    
    @api.depends('date_from', 'date_to', 'reporting_period')
    def _compute_comparative_dates(self):
        for record in self:
            if record.date_from and record.date_to:
                # Calculate comparative period (previous year)
                from_date = record.date_from
                to_date = record.date_to
                
                record.comparative_date_from = from_date.replace(year=from_date.year - 1)
                record.comparative_date_to = to_date.replace(year=to_date.year - 1)
    
    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for record in self:
            if record.date_from >= record.date_to:
                raise ValidationError(_('Period From date must be before Period To date.'))
    
    def action_generate_statement(self):
        """Generate IFRS Financial Statement"""
        self.ensure_one()
        self.state = 'in_progress'

        # Clear existing lines
        self.statement_line_ids.unlink()

        # Generate statement lines based on type
        if self.statement_type == 'balance_sheet':
            self._generate_balance_sheet()
        elif self.statement_type == 'income_statement':
            self._generate_income_statement()
        elif self.statement_type == 'cash_flow':
            self._generate_cash_flow()
        elif self.statement_type == 'equity_changes':
            self._generate_equity_changes()
        elif self.statement_type == 'complete_set':
            self._generate_complete_set()

        # Auto-assign accounts to statement lines
        self._auto_assign_accounts()

        # Check if we have any financial data, if not, suggest creating some
        has_data = self._check_for_financial_data()

        # Run compliance checks
        self._run_compliance_checks()

        message = 'IFRS Financial Statement generated successfully with actual financial data.'
        if not has_data:
            message = 'IFRS Financial Statement generated. Note: No accounting transactions found. Create some invoices/bills to see financial figures.'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _(message),
                'type': 'success' if has_data else 'warning',
            }
        }
    
    def _generate_balance_sheet(self):
        """Generate Statement of Financial Position (Balance Sheet)"""
        self.ensure_one()

        # Create balance sheet structure using the template
        self.env['ifrs.statement.line'].create_balance_sheet_structure(self.id)

        # Now populate with actual data
        self._populate_balance_sheet_data()
    
    def _generate_income_statement(self):
        """Generate Statement of Comprehensive Income"""
        self.ensure_one()

        # Create income statement structure
        self._create_income_statement_structure()

        # Auto-assign accounts to income statement lines
        self._auto_assign_income_accounts()

        # Populate with actual data
        self._populate_income_statement_data()

    def _create_income_statement_structure(self):
        """Create standard income statement structure"""
        lines = [
            # Revenue
            {'name': 'Revenue', 'line_type': 'line', 'sequence': 10, 'statement_section': 'revenue', 'ifrs_reference': 'IFRS 15', 'bold': True},
            {'name': 'Cost of Sales', 'line_type': 'line', 'sequence': 20, 'statement_section': 'cost_of_sales', 'ifrs_reference': 'IAS 2'},
            {'name': 'Gross Profit', 'line_type': 'subtotal', 'sequence': 30, 'bold': True},

            # Operating Expenses
            {'name': 'Operating Expenses', 'line_type': 'header', 'sequence': 40, 'statement_section': 'operating_expenses', 'bold': True},
            {'name': 'Administrative Expenses', 'line_type': 'line', 'sequence': 50, 'statement_section': 'operating_expenses', 'indent_level': 1},
            {'name': 'Selling Expenses', 'line_type': 'line', 'sequence': 60, 'statement_section': 'operating_expenses', 'indent_level': 1},
            {'name': 'Total Operating Expenses', 'line_type': 'subtotal', 'sequence': 70, 'statement_section': 'operating_expenses', 'bold': True},

            # Operating Profit
            {'name': 'Operating Profit', 'line_type': 'subtotal', 'sequence': 80, 'bold': True},

            # Finance
            {'name': 'Finance Income', 'line_type': 'line', 'sequence': 90, 'statement_section': 'other_income'},
            {'name': 'Finance Costs', 'line_type': 'line', 'sequence': 100, 'statement_section': 'finance_costs', 'ifrs_reference': 'IAS 23'},

            # Profit Before Tax
            {'name': 'Profit Before Tax', 'line_type': 'subtotal', 'sequence': 110, 'bold': True},

            # Tax
            {'name': 'Income Tax Expense', 'line_type': 'line', 'sequence': 120, 'statement_section': 'tax_expense', 'ifrs_reference': 'IAS 12'},

            # Net Profit
            {'name': 'Net Profit for the Year', 'line_type': 'total', 'sequence': 130, 'bold': True, 'underline': True},
        ]

        for line_data in lines:
            line_data['statement_id'] = self.id
            self.env['ifrs.statement.line'].create(line_data)

    def _populate_income_statement_data(self):
        """Populate income statement with actual data"""
        self.ensure_one()

        # Get account data for current and comparative periods
        current_data = self._get_income_statement_balances(self.date_from, self.date_to)
        comparative_data = {}
        if self.comparative_period:
            comparative_data = self._get_income_statement_balances(self.comparative_date_from, self.comparative_date_to)

        # Update statement lines with actual data
        for line in self.statement_line_ids:
            if line.line_type == 'line':
                if line.account_ids:
                    # Calculate current amount from assigned accounts
                    current_amount = sum(current_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                    line.current_amount = current_amount

                    # Calculate comparative amount
                    if self.comparative_period:
                        comparative_amount = sum(comparative_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                        line.comparative_amount = comparative_amount
                else:
                    # Try to auto-assign accounts based on line name
                    self._auto_assign_income_line(line, current_data)

            elif line.line_type in ['total', 'subtotal']:
                # Calculate totals based on related lines
                self._calculate_income_totals(line)

    def _get_income_statement_balances(self, date_from, date_to):
        """Get income statement account balances for the specified period"""
        self.ensure_one()

        # For income statement, we need period balances (not cumulative)
        query = """
            SELECT aml.account_id,
                   aa.code,
                   aa.name,
                   aa.account_type,
                   SUM(aml.credit - aml.debit) as balance
            FROM account_move_line aml
            JOIN account_move am ON aml.move_id = am.id
            JOIN account_account aa ON aml.account_id = aa.id
            WHERE am.state = 'posted'
            AND am.date >= %s
            AND am.date <= %s
            AND aml.company_id = %s
            AND aa.account_type IN ('income', 'expense')
            GROUP BY aml.account_id, aa.code, aa.name, aa.account_type
            HAVING SUM(aml.credit - aml.debit) != 0
        """

        try:
            self.env.cr.execute(query, (date_from, date_to, self.company_id.id))
            results = self.env.cr.fetchall()

            account_data = {}
            for account_id, code, name, account_type, balance in results:
                account_data[account_id] = {
                    'balance': balance or 0.0,
                    'code': code or '',
                    'name': name or '',
                    'account_type': account_type or ''
                }

            return account_data
        except Exception as e:
            _logger.error(f"Error retrieving income statement balances: {e}")
            return {}

    def _calculate_income_totals(self, total_line):
        """Calculate totals for income statement"""
        if total_line.name == 'Gross Profit':
            revenue_line = self.statement_line_ids.filtered(lambda l: l.name == 'Revenue')
            cost_line = self.statement_line_ids.filtered(lambda l: l.name == 'Cost of Sales')
            total_line.current_amount = (revenue_line.current_amount or 0) - abs(cost_line.current_amount or 0)
            total_line.comparative_amount = (revenue_line.comparative_amount or 0) - abs(cost_line.comparative_amount or 0)

        elif total_line.name == 'Total Operating Expenses':
            expense_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == 'operating_expenses' and l.line_type == 'line'
            )
            total_line.current_amount = sum(abs(line.current_amount or 0) for line in expense_lines)
            total_line.comparative_amount = sum(abs(line.comparative_amount or 0) for line in expense_lines)

        elif total_line.name == 'Operating Profit':
            gross_profit = self.statement_line_ids.filtered(lambda l: l.name == 'Gross Profit')
            total_expenses = self.statement_line_ids.filtered(lambda l: l.name == 'Total Operating Expenses')
            total_line.current_amount = (gross_profit.current_amount or 0) - (total_expenses.current_amount or 0)
            total_line.comparative_amount = (gross_profit.comparative_amount or 0) - (total_expenses.comparative_amount or 0)

        elif total_line.name == 'Profit Before Tax':
            operating_profit = self.statement_line_ids.filtered(lambda l: l.name == 'Operating Profit')
            finance_income = self.statement_line_ids.filtered(lambda l: l.name == 'Finance Income')
            finance_costs = self.statement_line_ids.filtered(lambda l: l.name == 'Finance Costs')
            total_line.current_amount = (operating_profit.current_amount or 0) + (finance_income.current_amount or 0) - abs(finance_costs.current_amount or 0)
            total_line.comparative_amount = (operating_profit.comparative_amount or 0) + (finance_income.comparative_amount or 0) - abs(finance_costs.comparative_amount or 0)

        elif total_line.name == 'Net Profit for the Year':
            profit_before_tax = self.statement_line_ids.filtered(lambda l: l.name == 'Profit Before Tax')
            tax_expense = self.statement_line_ids.filtered(lambda l: l.name == 'Income Tax Expense')
            total_line.current_amount = (profit_before_tax.current_amount or 0) - abs(tax_expense.current_amount or 0)
            total_line.comparative_amount = (profit_before_tax.comparative_amount or 0) - abs(tax_expense.comparative_amount or 0)
    
    def _generate_cash_flow(self):
        """Generate Statement of Cash Flows"""
        self.ensure_one()

        # Create cash flow statement structure
        self._create_cash_flow_structure()

        # Auto-assign accounts to cash flow lines
        self._auto_assign_cash_flow_accounts()

        # Populate with actual data
        self._populate_cash_flow_data()
    
    def _generate_equity_changes(self):
        """Generate Statement of Changes in Equity"""
        self.ensure_one()

        # Create equity changes structure
        self._create_equity_changes_structure()

        # Auto-assign accounts to equity lines
        self._auto_assign_equity_accounts()

        # Populate with actual data
        self._populate_equity_changes_data()
    
    def _generate_complete_set(self):
        """Generate Complete Set of Financial Statements"""
        # Generate balance sheet first
        self._generate_balance_sheet()

        # Then generate income statement
        self._generate_income_statement()

        # Generate cash flow statement
        self._generate_cash_flow()

        # Generate equity changes statement
        self._generate_equity_changes()

        # Generate notes to financial statements
        self._generate_notes()
    
    def _run_compliance_checks(self):
        """Run IFRS compliance checks"""
        # Clear existing checks
        self.compliance_check_ids.unlink()
        
        # Create compliance check records
        checks = [
            {'name': 'IAS 1 - Presentation of Financial Statements', 'status': 'passed'},
            {'name': 'IAS 7 - Statement of Cash Flows', 'status': 'passed'},
            {'name': 'IFRS 15 - Revenue Recognition', 'status': 'warning'},
            {'name': 'IFRS 16 - Leases', 'status': 'passed'},
        ]
        
        for check in checks:
            self.env['ifrs.compliance.check'].create({
                'statement_id': self.id,
                'name': check['name'],
                'status': check['status'],
                'check_date': fields.Datetime.now(),
            })
    
    def action_submit_for_review(self):
        """Submit statement for review"""
        self.state = 'review'
        self.review_date = fields.Datetime.now()
    
    def action_approve(self):
        """Approve the financial statement"""
        self.state = 'approved'
        self.approved_by = self.env.user
        self.approval_date = fields.Datetime.now()
    
    def action_publish(self):
        """Publish the financial statement"""
        self.state = 'published'
    
    def action_reset_to_draft(self):
        """Reset to draft state"""
        self.state = 'draft'

    def action_export_pdf(self):
        """Export financial statement to PDF"""
        return self.env.ref('ifrs_financial_statements.action_report_ifrs_statement').report_action(self)

    def action_export_excel(self):
        """Export financial statement to Excel"""
        # This will be implemented for Excel export
        pass

    def _populate_balance_sheet_data(self):
        """Populate balance sheet with actual accounting data"""
        self.ensure_one()

        # Get account data for current and comparative periods
        current_data = self._get_account_balances(self.date_from, self.date_to)
        comparative_data = {}
        if self.comparative_period:
            comparative_data = self._get_account_balances(self.comparative_date_from, self.comparative_date_to)

        # First, auto-assign accounts if not already assigned
        self._auto_assign_accounts_smart(current_data)

        # Update statement lines with actual data
        for line in self.statement_line_ids:
            if line.line_type == 'line':
                if line.account_ids:
                    # Calculate current amount from assigned accounts
                    current_amount = sum(current_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                    line.current_amount = current_amount

                    # Calculate comparative amount
                    if self.comparative_period:
                        comparative_amount = sum(comparative_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                        line.comparative_amount = comparative_amount
                else:
                    # Try to find accounts automatically based on line name
                    self._auto_assign_single_line(line, current_data)

            elif line.line_type in ['total', 'subtotal']:
                # Calculate totals based on related lines
                self._calculate_totals(line)

    def _get_account_balances(self, date_from, date_to):
        """Get account balances for the specified period"""
        self.ensure_one()

        # For balance sheet, we need cumulative balances up to the end date
        query = """
            SELECT aml.account_id,
                   aa.code,
                   aa.name,
                   aa.account_type,
                   SUM(aml.debit - aml.credit) as balance
            FROM account_move_line aml
            JOIN account_move am ON aml.move_id = am.id
            JOIN account_account aa ON aml.account_id = aa.id
            WHERE am.state = 'posted'
            AND am.date <= %s
            AND aml.company_id = %s
            GROUP BY aml.account_id, aa.code, aa.name, aa.account_type
            HAVING SUM(aml.debit - aml.credit) != 0
        """

        try:
            self.env.cr.execute(query, (date_to, self.company_id.id))
            results = self.env.cr.fetchall()

            account_data = {}
            for account_id, code, name, account_type, balance in results:
                account_data[account_id] = {
                    'balance': balance or 0.0,
                    'code': code or '',
                    'name': name or '',
                    'account_type': account_type or ''
                }

            return account_data
        except Exception as e:
            # Log error and return empty dict
            _logger.error(f"Error retrieving account balances: {e}")
            return {}

    def _calculate_totals(self, total_line):
        """Calculate totals for subtotal and total lines"""
        # This is a simplified calculation - in practice, you'd define
        # which lines contribute to each total
        if total_line.name == 'Total Current Assets':
            # Sum all current asset lines
            asset_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == 'assets_current' and l.line_type == 'line'
            )
            total_line.current_amount = sum(asset_lines.mapped('current_amount'))
            total_line.comparative_amount = sum(asset_lines.mapped('comparative_amount'))

        elif total_line.name == 'Total Non-Current Assets':
            # Sum all non-current asset lines
            asset_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == 'assets_non_current' and l.line_type == 'line'
            )
            total_line.current_amount = sum(asset_lines.mapped('current_amount'))
            total_line.comparative_amount = sum(asset_lines.mapped('comparative_amount'))

        elif total_line.name == 'TOTAL ASSETS':
            # Sum all asset totals
            asset_totals = self.statement_line_ids.filtered(
                lambda l: l.name in ['Total Current Assets', 'Total Non-Current Assets']
            )
            total_line.current_amount = sum(asset_totals.mapped('current_amount'))
            total_line.comparative_amount = sum(asset_totals.mapped('comparative_amount'))

    def _auto_assign_accounts(self):
        """Auto-assign accounts to statement lines based on account types"""
        self.ensure_one()

        # Get all accounts for the company
        accounts = self.env['account.account'].search([('company_id', '=', self.company_id.id)])

        # Define account type mappings
        account_mappings = {
            # Balance Sheet Items
            'Cash and Cash Equivalents': ['asset_cash', 'asset_current'],
            'Trade and Other Receivables': ['asset_receivable', 'asset_current'],
            'Inventories': ['asset_current'],
            'Property, Plant and Equipment': ['asset_fixed'],
            'Intangible Assets': ['asset_non_current'],
            'Trade and Other Payables': ['liability_payable', 'liability_current'],
            'Short-term Borrowings': ['liability_current'],
            'Long-term Borrowings': ['liability_non_current'],

            # Income Statement Items
            'Revenue': ['income'],
            'Cost of Sales': ['expense'],
            'Administrative Expenses': ['expense'],
            'Selling Expenses': ['expense'],
            'Finance Income': ['income'],
            'Finance Costs': ['expense'],
            'Income Tax Expense': ['expense'],
        }

        # Assign accounts to lines
        for line in self.statement_line_ids.filtered(lambda l: l.line_type == 'line'):
            if line.name in account_mappings:
                # Find matching accounts
                matching_accounts = accounts.filtered(
                    lambda a: any(acc_type in str(a.account_type) for acc_type in account_mappings[line.name])
                )
                if matching_accounts:
                    line.account_ids = [(6, 0, matching_accounts.ids)]

    def _auto_assign_accounts_smart(self, account_data):
        """Smart auto-assignment based on actual account data"""
        self.ensure_one()

        # Group accounts by type for easier assignment
        accounts_by_type = {}
        for acc_id, data in account_data.items():
            acc_type = data['account_type']
            if acc_type not in accounts_by_type:
                accounts_by_type[acc_type] = []
            accounts_by_type[acc_type].append(acc_id)

        # Define smart mappings based on account types and names
        smart_mappings = {
            'Cash and Cash Equivalents': {
                'types': ['asset_cash', 'asset_current'],
                'keywords': ['cash', 'bank', 'petty']
            },
            'Trade and Other Receivables': {
                'types': ['asset_receivable'],
                'keywords': ['receivable', 'debtors', 'accounts receivable']
            },
            'Inventories': {
                'types': ['asset_current'],
                'keywords': ['inventory', 'stock', 'goods']
            },
            'Property, Plant and Equipment': {
                'types': ['asset_fixed'],
                'keywords': ['equipment', 'building', 'machinery', 'furniture']
            },
            'Trade and Other Payables': {
                'types': ['liability_payable'],
                'keywords': ['payable', 'creditors', 'accounts payable']
            },
        }

        # Assign accounts to lines
        for line in self.statement_line_ids.filtered(lambda l: l.line_type == 'line' and not l.account_ids):
            if line.name in smart_mappings:
                mapping = smart_mappings[line.name]
                matching_account_ids = []

                # Find accounts by type
                for acc_type in mapping['types']:
                    if acc_type in accounts_by_type:
                        matching_account_ids.extend(accounts_by_type[acc_type])

                # Filter by keywords if available
                if mapping.get('keywords') and matching_account_ids:
                    filtered_ids = []
                    for acc_id in matching_account_ids:
                        if acc_id in account_data and 'name' in account_data[acc_id]:
                            acc_name = str(account_data[acc_id]['name']).lower()
                            if any(keyword in acc_name for keyword in mapping['keywords']):
                                filtered_ids.append(acc_id)
                    if filtered_ids:
                        matching_account_ids = filtered_ids

                if matching_account_ids:
                    line.account_ids = [(6, 0, matching_account_ids)]

    def _auto_assign_single_line(self, line, account_data):
        """Auto-assign accounts to a single line based on available data"""
        # Simple fallback assignment based on line name
        line_name_lower = line.name.lower()

        matching_accounts = []
        for acc_id, data in account_data.items():
            if isinstance(data, dict) and 'name' in data:
                acc_name_lower = str(data['name']).lower()

                # Simple keyword matching
                if ('cash' in line_name_lower and 'cash' in acc_name_lower) or \
                   ('receivable' in line_name_lower and 'receivable' in acc_name_lower) or \
                   ('payable' in line_name_lower and 'payable' in acc_name_lower) or \
                   ('inventory' in line_name_lower and ('inventory' in acc_name_lower or 'stock' in acc_name_lower)):
                    matching_accounts.append(acc_id)

        if matching_accounts:
            line.account_ids = [(6, 0, matching_accounts)]
            # Recalculate amounts
            current_amount = sum(account_data.get(acc_id, {}).get('balance', 0.0) for acc_id in matching_accounts)
            line.current_amount = current_amount

    def _auto_assign_income_accounts(self):
        """Auto-assign accounts to income statement lines"""
        self.ensure_one()

        # Get all accounts for the company
        accounts = self.env['account.account'].search([('company_id', '=', self.company_id.id)])

        # Define income statement account mappings
        income_mappings = {
            'Revenue': ['income'],
            'Cost of Sales': ['expense'],
            'Administrative Expenses': ['expense'],
            'Selling Expenses': ['expense'],
            'Finance Income': ['income'],
            'Finance Costs': ['expense'],
            'Income Tax Expense': ['expense'],
        }

        # Assign accounts to lines
        for line in self.statement_line_ids.filtered(lambda l: l.line_type == 'line' and not l.account_ids):
            if line.name in income_mappings:
                account_types = income_mappings[line.name]

                # Find matching accounts by type
                matching_accounts = accounts.filtered(
                    lambda a: any(acc_type in str(a.account_type) for acc_type in account_types)
                )

                # Further filter by keywords for specific lines
                if line.name == 'Cost of Sales':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['cost', 'cogs', 'purchase'])
                    )
                elif line.name == 'Administrative Expenses':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['admin', 'office', 'general'])
                    )
                elif line.name == 'Selling Expenses':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['sales', 'marketing', 'commission'])
                    )
                elif line.name == 'Finance Income':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['interest income', 'finance income'])
                    )
                elif line.name == 'Finance Costs':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['interest', 'finance cost', 'bank charge'])
                    )
                elif line.name == 'Income Tax Expense':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['tax', 'income tax'])
                    )

                if matching_accounts:
                    line.account_ids = [(6, 0, matching_accounts.ids)]

    def _auto_assign_income_line(self, line, account_data):
        """Auto-assign accounts to a single income statement line"""
        line_name_lower = line.name.lower()

        matching_accounts = []
        for acc_id, data in account_data.items():
            if isinstance(data, dict) and 'name' in data and 'account_type' in data:
                acc_name_lower = str(data['name']).lower()
                acc_type = data['account_type']

                # Match based on line name and account type
                if ('revenue' in line_name_lower and 'income' in acc_type) or \
                   ('cost' in line_name_lower and 'expense' in acc_type and any(keyword in acc_name_lower for keyword in ['cost', 'cogs', 'purchase'])) or \
                   ('admin' in line_name_lower and 'expense' in acc_type and any(keyword in acc_name_lower for keyword in ['admin', 'office'])) or \
                   ('selling' in line_name_lower and 'expense' in acc_type and any(keyword in acc_name_lower for keyword in ['sales', 'marketing'])) or \
                   ('finance income' in line_name_lower and 'income' in acc_type and 'interest' in acc_name_lower) or \
                   ('finance cost' in line_name_lower and 'expense' in acc_type and any(keyword in acc_name_lower for keyword in ['interest', 'finance'])) or \
                   ('tax' in line_name_lower and 'expense' in acc_type and 'tax' in acc_name_lower):
                    matching_accounts.append(acc_id)

        if matching_accounts:
            line.account_ids = [(6, 0, matching_accounts)]
            # Recalculate amounts
            current_amount = sum(account_data.get(acc_id, {}).get('balance', 0.0) for acc_id in matching_accounts)
            line.current_amount = current_amount

    def _check_for_financial_data(self):
        """Check if there's any financial data in the system"""
        self.ensure_one()

        # Check for posted account moves in the period
        moves = self.env['account.move'].search([
            ('state', '=', 'posted'),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
            ('company_id', '=', self.company_id.id)
        ], limit=1)

        return bool(moves)

    def action_create_sample_data(self):
        """Create sample accounting data for testing"""
        self.ensure_one()

        # Create a sample customer
        customer = self.env['res.partner'].create({
            'name': 'Sample Customer',
            'is_company': True,
            'customer_rank': 1,
        })

        # Create a sample vendor
        vendor = self.env['res.partner'].create({
            'name': 'Sample Vendor',
            'is_company': True,
            'supplier_rank': 1,
        })

        # Create a sample product
        product = self.env['product.product'].create({
            'name': 'Sample Service',
            'type': 'service',
            'list_price': 1000.0,
        })

        # Create a sample customer invoice
        invoice = self.env['account.move'].create({
            'move_type': 'out_invoice',
            'partner_id': customer.id,
            'invoice_date': self.date_from,
            'invoice_line_ids': [(0, 0, {
                'product_id': product.id,
                'quantity': 1,
                'price_unit': 1000.0,
            })],
        })
        invoice.action_post()

        # Create a sample vendor bill
        bill = self.env['account.move'].create({
            'move_type': 'in_invoice',
            'partner_id': vendor.id,
            'invoice_date': self.date_from,
            'invoice_line_ids': [(0, 0, {
                'product_id': product.id,
                'quantity': 1,
                'price_unit': 500.0,
            })],
        })
        bill.action_post()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sample Data Created'),
                'message': _('Sample customer invoice and vendor bill created. Now regenerate your financial statement.'),
                'type': 'success',
            }
        }

    def _create_cash_flow_structure(self):
        """Create standard cash flow statement structure"""
        lines = [
            # Operating Activities
            {'name': 'Cash Flows from Operating Activities', 'line_type': 'header', 'sequence': 10, 'statement_section': 'operating_activities', 'ifrs_reference': 'IAS 7', 'bold': True},
            {'name': 'Net Profit for the Year', 'line_type': 'line', 'sequence': 20, 'statement_section': 'operating_activities', 'indent_level': 1},
            {'name': 'Adjustments for:', 'line_type': 'subtotal', 'sequence': 30, 'statement_section': 'operating_activities', 'indent_level': 1},
            {'name': 'Depreciation and Amortization', 'line_type': 'line', 'sequence': 40, 'statement_section': 'operating_activities', 'indent_level': 2, 'ifrs_reference': 'IAS 16'},
            {'name': 'Interest Expense', 'line_type': 'line', 'sequence': 50, 'statement_section': 'operating_activities', 'indent_level': 2},
            {'name': 'Tax Expense', 'line_type': 'line', 'sequence': 60, 'statement_section': 'operating_activities', 'indent_level': 2},
            {'name': 'Changes in Working Capital:', 'line_type': 'subtotal', 'sequence': 70, 'statement_section': 'operating_activities', 'indent_level': 1},
            {'name': 'Increase in Trade Receivables', 'line_type': 'line', 'sequence': 80, 'statement_section': 'operating_activities', 'indent_level': 2},
            {'name': 'Increase in Inventories', 'line_type': 'line', 'sequence': 90, 'statement_section': 'operating_activities', 'indent_level': 2},
            {'name': 'Increase in Trade Payables', 'line_type': 'line', 'sequence': 100, 'statement_section': 'operating_activities', 'indent_level': 2},
            {'name': 'Interest Paid', 'line_type': 'line', 'sequence': 110, 'statement_section': 'operating_activities', 'indent_level': 1},
            {'name': 'Income Tax Paid', 'line_type': 'line', 'sequence': 120, 'statement_section': 'operating_activities', 'indent_level': 1},
            {'name': 'Net Cash from Operating Activities', 'line_type': 'total', 'sequence': 130, 'statement_section': 'operating_activities', 'bold': True, 'underline': True},

            # Investing Activities
            {'name': 'Cash Flows from Investing Activities', 'line_type': 'header', 'sequence': 140, 'statement_section': 'investing_activities', 'ifrs_reference': 'IAS 7', 'bold': True},
            {'name': 'Purchase of Property, Plant and Equipment', 'line_type': 'line', 'sequence': 150, 'statement_section': 'investing_activities', 'indent_level': 1, 'ifrs_reference': 'IAS 16'},
            {'name': 'Sale of Property, Plant and Equipment', 'line_type': 'line', 'sequence': 160, 'statement_section': 'investing_activities', 'indent_level': 1},
            {'name': 'Purchase of Investments', 'line_type': 'line', 'sequence': 170, 'statement_section': 'investing_activities', 'indent_level': 1},
            {'name': 'Sale of Investments', 'line_type': 'line', 'sequence': 180, 'statement_section': 'investing_activities', 'indent_level': 1},
            {'name': 'Interest Received', 'line_type': 'line', 'sequence': 190, 'statement_section': 'investing_activities', 'indent_level': 1},
            {'name': 'Net Cash from Investing Activities', 'line_type': 'total', 'sequence': 200, 'statement_section': 'investing_activities', 'bold': True, 'underline': True},

            # Financing Activities
            {'name': 'Cash Flows from Financing Activities', 'line_type': 'header', 'sequence': 210, 'statement_section': 'financing_activities', 'ifrs_reference': 'IAS 7', 'bold': True},
            {'name': 'Proceeds from Borrowings', 'line_type': 'line', 'sequence': 220, 'statement_section': 'financing_activities', 'indent_level': 1},
            {'name': 'Repayment of Borrowings', 'line_type': 'line', 'sequence': 230, 'statement_section': 'financing_activities', 'indent_level': 1},
            {'name': 'Proceeds from Share Issue', 'line_type': 'line', 'sequence': 240, 'statement_section': 'financing_activities', 'indent_level': 1, 'ifrs_reference': 'IAS 32'},
            {'name': 'Dividends Paid', 'line_type': 'line', 'sequence': 250, 'statement_section': 'financing_activities', 'indent_level': 1},
            {'name': 'Net Cash from Financing Activities', 'line_type': 'total', 'sequence': 260, 'statement_section': 'financing_activities', 'bold': True, 'underline': True},

            # Net Change in Cash
            {'name': 'Net Increase in Cash and Cash Equivalents', 'line_type': 'total', 'sequence': 270, 'bold': True, 'underline': True},
            {'name': 'Cash and Cash Equivalents at Beginning of Year', 'line_type': 'line', 'sequence': 280},
            {'name': 'Cash and Cash Equivalents at End of Year', 'line_type': 'total', 'sequence': 290, 'bold': True, 'underline': True},
        ]

        for line_data in lines:
            line_data['statement_id'] = self.id
            self.env['ifrs.statement.line'].create(line_data)

    def _auto_assign_cash_flow_accounts(self):
        """Auto-assign accounts to cash flow lines"""
        self.ensure_one()

        # Get all accounts for the company
        accounts = self.env['account.account'].search([('company_id', '=', self.company_id.id)])

        # Define cash flow account mappings
        cash_flow_mappings = {
            'Net Profit for the Year': ['income', 'expense'],  # Will be calculated from P&L
            'Depreciation and Amortization': ['expense'],
            'Interest Expense': ['expense'],
            'Tax Expense': ['expense'],
            'Interest Received': ['income'],
            'Cash and Cash Equivalents at Beginning of Year': ['asset_cash'],
            'Cash and Cash Equivalents at End of Year': ['asset_cash'],
        }

        # Assign accounts to lines
        for line in self.statement_line_ids.filtered(lambda l: l.line_type == 'line' and not l.account_ids):
            if line.name in cash_flow_mappings:
                account_types = cash_flow_mappings[line.name]

                # Find matching accounts by type
                matching_accounts = accounts.filtered(
                    lambda a: any(acc_type in str(a.account_type) for acc_type in account_types)
                )

                # Further filter by keywords for specific lines
                if line.name == 'Depreciation and Amortization':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['depreciation', 'amortization'])
                    )
                elif line.name == 'Interest Expense':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['interest expense', 'finance cost'])
                    )
                elif line.name == 'Tax Expense':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['tax', 'income tax'])
                    )
                elif line.name == 'Interest Received':
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['interest income', 'interest received'])
                    )
                elif 'Cash and Cash Equivalents' in line.name:
                    matching_accounts = matching_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in ['cash', 'bank'])
                    )

                if matching_accounts:
                    line.account_ids = [(6, 0, matching_accounts.ids)]

    def _populate_cash_flow_data(self):
        """Populate cash flow statement with actual data"""
        self.ensure_one()

        # Get cash flow data for current and comparative periods
        current_data = self._get_cash_flow_balances(self.date_from, self.date_to)
        comparative_data = {}
        if self.comparative_period:
            comparative_data = self._get_cash_flow_balances(self.comparative_date_from, self.comparative_date_to)

        # Update statement lines with actual data
        for line in self.statement_line_ids:
            if line.line_type == 'line':
                if line.account_ids:
                    # Calculate current amount from assigned accounts
                    current_amount = sum(current_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                    line.current_amount = current_amount

                    # Calculate comparative amount
                    if self.comparative_period:
                        comparative_amount = sum(comparative_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                        line.comparative_amount = comparative_amount
                elif line.name == 'Net Profit for the Year':
                    # Get net profit from income statement
                    line.current_amount = self._get_net_profit_amount()
                    if self.comparative_period:
                        line.comparative_amount = self._get_net_profit_amount(comparative=True)

            elif line.line_type in ['total', 'subtotal']:
                # Calculate totals based on related lines
                self._calculate_cash_flow_totals(line)

    def _get_cash_flow_balances(self, date_from, date_to):
        """Get cash flow related account balances"""
        self.ensure_one()

        # For cash flow, we need period movements
        query = """
            SELECT aml.account_id,
                   aa.code,
                   aa.name,
                   aa.account_type,
                   SUM(aml.debit - aml.credit) as balance
            FROM account_move_line aml
            JOIN account_move am ON aml.move_id = am.id
            JOIN account_account aa ON aml.account_id = aa.id
            WHERE am.state = 'posted'
            AND am.date >= %s
            AND am.date <= %s
            AND aml.company_id = %s
            GROUP BY aml.account_id, aa.code, aa.name, aa.account_type
            HAVING SUM(aml.debit - aml.credit) != 0
        """

        try:
            self.env.cr.execute(query, (date_from, date_to, self.company_id.id))
            results = self.env.cr.fetchall()

            account_data = {}
            for account_id, code, name, account_type, balance in results:
                account_data[account_id] = {
                    'balance': balance or 0.0,
                    'code': code or '',
                    'name': name or '',
                    'account_type': account_type or ''
                }

            return account_data
        except Exception as e:
            _logger.error(f"Error retrieving cash flow balances: {e}")
            return {}

    def _get_net_profit_amount(self, comparative=False):
        """Get net profit amount from income statement or calculate it"""
        # Try to get from existing income statement lines
        net_profit_line = self.statement_line_ids.filtered(lambda l: l.name == 'Net Profit for the Year' and l.statement_section != 'operating_activities')
        if net_profit_line:
            return net_profit_line.comparative_amount if comparative else net_profit_line.current_amount

        # Calculate from income and expense accounts
        if comparative:
            income_data = self._get_income_statement_balances(self.comparative_date_from, self.comparative_date_to)
        else:
            income_data = self._get_income_statement_balances(self.date_from, self.date_to)

        total_income = sum(data.get('balance', 0.0) for data in income_data.values() if 'income' in data.get('account_type', ''))
        total_expense = sum(abs(data.get('balance', 0.0)) for data in income_data.values() if 'expense' in data.get('account_type', ''))

        return total_income - total_expense

    def _calculate_cash_flow_totals(self, total_line):
        """Calculate totals for cash flow statement"""
        if total_line.name == 'Net Cash from Operating Activities':
            operating_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == 'operating_activities' and l.line_type == 'line'
            )
            total_line.current_amount = sum(operating_lines.mapped('current_amount'))
            total_line.comparative_amount = sum(operating_lines.mapped('comparative_amount'))

        elif total_line.name == 'Net Cash from Investing Activities':
            investing_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == 'investing_activities' and l.line_type == 'line'
            )
            total_line.current_amount = sum(investing_lines.mapped('current_amount'))
            total_line.comparative_amount = sum(investing_lines.mapped('comparative_amount'))

        elif total_line.name == 'Net Cash from Financing Activities':
            financing_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == 'financing_activities' and l.line_type == 'line'
            )
            total_line.current_amount = sum(financing_lines.mapped('current_amount'))
            total_line.comparative_amount = sum(financing_lines.mapped('comparative_amount'))

        elif total_line.name == 'Net Increase in Cash and Cash Equivalents':
            operating_total = self.statement_line_ids.filtered(lambda l: l.name == 'Net Cash from Operating Activities')
            investing_total = self.statement_line_ids.filtered(lambda l: l.name == 'Net Cash from Investing Activities')
            financing_total = self.statement_line_ids.filtered(lambda l: l.name == 'Net Cash from Financing Activities')

            total_line.current_amount = (operating_total.current_amount or 0) + (investing_total.current_amount or 0) + (financing_total.current_amount or 0)
            total_line.comparative_amount = (operating_total.comparative_amount or 0) + (investing_total.comparative_amount or 0) + (financing_total.comparative_amount or 0)

        elif total_line.name == 'Cash and Cash Equivalents at End of Year':
            beginning_cash = self.statement_line_ids.filtered(lambda l: l.name == 'Cash and Cash Equivalents at Beginning of Year')
            net_increase = self.statement_line_ids.filtered(lambda l: l.name == 'Net Increase in Cash and Cash Equivalents')

            total_line.current_amount = (beginning_cash.current_amount or 0) + (net_increase.current_amount or 0)
            total_line.comparative_amount = (beginning_cash.comparative_amount or 0) + (net_increase.comparative_amount or 0)

    def _create_equity_changes_structure(self):
        """Create standard statement of changes in equity structure"""
        lines = [
            # Headers
            {'name': 'Share Capital', 'line_type': 'header', 'sequence': 10, 'statement_section': 'share_capital', 'bold': True, 'ifrs_reference': 'IAS 32'},
            {'name': 'Balance at Beginning of Year', 'line_type': 'line', 'sequence': 20, 'statement_section': 'share_capital', 'indent_level': 1},
            {'name': 'Issue of Share Capital', 'line_type': 'line', 'sequence': 30, 'statement_section': 'share_capital', 'indent_level': 1},
            {'name': 'Balance at End of Year', 'line_type': 'total', 'sequence': 40, 'statement_section': 'share_capital', 'bold': True, 'indent_level': 1},

            {'name': 'Retained Earnings', 'line_type': 'header', 'sequence': 50, 'statement_section': 'retained_earnings', 'bold': True},
            {'name': 'Balance at Beginning of Year', 'line_type': 'line', 'sequence': 60, 'statement_section': 'retained_earnings', 'indent_level': 1},
            {'name': 'Net Profit for the Year', 'line_type': 'line', 'sequence': 70, 'statement_section': 'retained_earnings', 'indent_level': 1},
            {'name': 'Dividends Paid', 'line_type': 'line', 'sequence': 80, 'statement_section': 'retained_earnings', 'indent_level': 1},
            {'name': 'Balance at End of Year', 'line_type': 'total', 'sequence': 90, 'statement_section': 'retained_earnings', 'bold': True, 'indent_level': 1},

            {'name': 'Other Reserves', 'line_type': 'header', 'sequence': 100, 'statement_section': 'other_reserves', 'bold': True},
            {'name': 'Balance at Beginning of Year', 'line_type': 'line', 'sequence': 110, 'statement_section': 'other_reserves', 'indent_level': 1},
            {'name': 'Other Comprehensive Income', 'line_type': 'line', 'sequence': 120, 'statement_section': 'other_reserves', 'indent_level': 1},
            {'name': 'Balance at End of Year', 'line_type': 'total', 'sequence': 130, 'statement_section': 'other_reserves', 'bold': True, 'indent_level': 1},

            {'name': 'Total Equity', 'line_type': 'total', 'sequence': 140, 'bold': True, 'underline': True},
        ]

        for line_data in lines:
            line_data['statement_id'] = self.id
            self.env['ifrs.statement.line'].create(line_data)

    def _auto_assign_equity_accounts(self):
        """Auto-assign accounts to equity lines"""
        self.ensure_one()

        # Get all equity accounts
        equity_accounts = self.env['account.account'].search([
            ('company_id', '=', self.company_id.id),
            ('account_type', 'like', 'equity')
        ])

        # Define equity mappings
        equity_mappings = {
            'Share Capital': ['share', 'capital'],
            'Retained Earnings': ['retained', 'earnings', 'profit'],
            'Other Reserves': ['reserve', 'revaluation'],
        }

        # Assign accounts to lines
        for line in self.statement_line_ids.filtered(lambda l: l.line_type in ['line', 'total'] and not l.account_ids):
            for equity_type, keywords in equity_mappings.items():
                if equity_type in line.name or any(keyword in line.name.lower() for keyword in keywords):
                    matching_accounts = equity_accounts.filtered(
                        lambda a: any(keyword in a.name.lower() for keyword in keywords)
                    )
                    if matching_accounts:
                        line.account_ids = [(6, 0, matching_accounts.ids)]
                        break

    def _populate_equity_changes_data(self):
        """Populate equity changes with actual data"""
        self.ensure_one()

        # Get equity data for current and comparative periods
        current_data = self._get_equity_balances(self.date_from, self.date_to)
        comparative_data = {}
        if self.comparative_period:
            comparative_data = self._get_equity_balances(self.comparative_date_from, self.comparative_date_to)

        # Update statement lines with actual data
        for line in self.statement_line_ids:
            if line.line_type == 'line' and line.account_ids:
                # Calculate current amount from assigned accounts
                current_amount = sum(current_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                line.current_amount = current_amount

                # Calculate comparative amount
                if self.comparative_period:
                    comparative_amount = sum(comparative_data.get(acc.id, {}).get('balance', 0.0) for acc in line.account_ids)
                    line.comparative_amount = comparative_amount

            elif line.line_type == 'total':
                # Calculate totals based on related lines
                self._calculate_equity_totals(line)

    def _get_equity_balances(self, date_from, date_to):
        """Get equity account balances"""
        self.ensure_one()

        # For equity, we need cumulative balances
        query = """
            SELECT aml.account_id,
                   aa.code,
                   aa.name,
                   aa.account_type,
                   SUM(aml.credit - aml.debit) as balance
            FROM account_move_line aml
            JOIN account_move am ON aml.move_id = am.id
            JOIN account_account aa ON aml.account_id = aa.id
            WHERE am.state = 'posted'
            AND am.date <= %s
            AND aml.company_id = %s
            AND aa.account_type LIKE '%%equity%%'
            GROUP BY aml.account_id, aa.code, aa.name, aa.account_type
            HAVING SUM(aml.credit - aml.debit) != 0
        """

        try:
            self.env.cr.execute(query, (date_to, self.company_id.id))
            results = self.env.cr.fetchall()

            account_data = {}
            for account_id, code, name, account_type, balance in results:
                account_data[account_id] = {
                    'balance': balance or 0.0,
                    'code': code or '',
                    'name': name or '',
                    'account_type': account_type or ''
                }

            return account_data
        except Exception as e:
            _logger.error(f"Error retrieving equity balances: {e}")
            return {}

    def _calculate_equity_totals(self, total_line):
        """Calculate totals for equity statement"""
        if 'Balance at End of Year' in total_line.name:
            section = total_line.statement_section
            section_lines = self.statement_line_ids.filtered(
                lambda l: l.statement_section == section and l.line_type == 'line'
            )
            total_line.current_amount = sum(section_lines.mapped('current_amount'))
            total_line.comparative_amount = sum(section_lines.mapped('comparative_amount'))

        elif total_line.name == 'Total Equity':
            all_section_totals = self.statement_line_ids.filtered(
                lambda l: 'Balance at End of Year' in l.name and l.line_type == 'total'
            )
            total_line.current_amount = sum(all_section_totals.mapped('current_amount'))
            total_line.comparative_amount = sum(all_section_totals.mapped('comparative_amount'))

    def _generate_notes(self):
        """Generate Notes to Financial Statements"""
        self.ensure_one()

        # Create notes structure
        self._create_notes_structure()

        # Populate notes with actual data
        self._populate_notes_data()

    def _create_notes_structure(self):
        """Create standard notes to financial statements structure"""
        lines = [
            # Note 1: Accounting Policies
            {'name': 'Note 1: Significant Accounting Policies', 'line_type': 'header', 'sequence': 10, 'statement_section': 'accounting_policies', 'bold': True, 'ifrs_reference': 'IAS 1'},
            {'name': 'Basis of Preparation', 'line_type': 'line', 'sequence': 20, 'statement_section': 'accounting_policies', 'indent_level': 1},
            {'name': 'Revenue Recognition', 'line_type': 'line', 'sequence': 30, 'statement_section': 'accounting_policies', 'indent_level': 1, 'ifrs_reference': 'IFRS 15'},
            {'name': 'Property, Plant and Equipment', 'line_type': 'line', 'sequence': 40, 'statement_section': 'accounting_policies', 'indent_level': 1, 'ifrs_reference': 'IAS 16'},
            {'name': 'Depreciation Methods', 'line_type': 'line', 'sequence': 50, 'statement_section': 'accounting_policies', 'indent_level': 1, 'ifrs_reference': 'IAS 16'},
            {'name': 'Financial Instruments', 'line_type': 'line', 'sequence': 60, 'statement_section': 'accounting_policies', 'indent_level': 1, 'ifrs_reference': 'IFRS 9'},

            # Note 2: Property, Plant and Equipment
            {'name': 'Note 2: Property, Plant and Equipment', 'line_type': 'header', 'sequence': 70, 'statement_section': 'ppe_details', 'bold': True, 'ifrs_reference': 'IAS 16'},
            {'name': 'Cost at Beginning of Year', 'line_type': 'line', 'sequence': 80, 'statement_section': 'ppe_details', 'indent_level': 1},
            {'name': 'Additions during the Year', 'line_type': 'line', 'sequence': 90, 'statement_section': 'ppe_details', 'indent_level': 1},
            {'name': 'Disposals during the Year', 'line_type': 'line', 'sequence': 100, 'statement_section': 'ppe_details', 'indent_level': 1},
            {'name': 'Cost at End of Year', 'line_type': 'total', 'sequence': 110, 'statement_section': 'ppe_details', 'indent_level': 1, 'bold': True},
            {'name': 'Accumulated Depreciation at Beginning', 'line_type': 'line', 'sequence': 120, 'statement_section': 'ppe_details', 'indent_level': 1},
            {'name': 'Depreciation for the Year', 'line_type': 'line', 'sequence': 130, 'statement_section': 'ppe_details', 'indent_level': 1},
            {'name': 'Accumulated Depreciation at End', 'line_type': 'total', 'sequence': 140, 'statement_section': 'ppe_details', 'indent_level': 1, 'bold': True},
            {'name': 'Net Book Value', 'line_type': 'total', 'sequence': 150, 'statement_section': 'ppe_details', 'bold': True, 'underline': True},

            # Note 3: Trade and Other Receivables
            {'name': 'Note 3: Trade and Other Receivables', 'line_type': 'header', 'sequence': 160, 'statement_section': 'receivables_details', 'bold': True, 'ifrs_reference': 'IFRS 9'},
            {'name': 'Trade Receivables', 'line_type': 'line', 'sequence': 170, 'statement_section': 'receivables_details', 'indent_level': 1},
            {'name': 'Less: Allowance for Expected Credit Losses', 'line_type': 'line', 'sequence': 180, 'statement_section': 'receivables_details', 'indent_level': 1, 'ifrs_reference': 'IFRS 9'},
            {'name': 'Other Receivables', 'line_type': 'line', 'sequence': 190, 'statement_section': 'receivables_details', 'indent_level': 1},
            {'name': 'Prepayments', 'line_type': 'line', 'sequence': 200, 'statement_section': 'receivables_details', 'indent_level': 1},
            {'name': 'Total Trade and Other Receivables', 'line_type': 'total', 'sequence': 210, 'statement_section': 'receivables_details', 'bold': True, 'underline': True},

            # Note 4: Share Capital
            {'name': 'Note 4: Share Capital', 'line_type': 'header', 'sequence': 220, 'statement_section': 'share_capital_details', 'bold': True, 'ifrs_reference': 'IAS 32'},
            {'name': 'Authorized Share Capital', 'line_type': 'line', 'sequence': 230, 'statement_section': 'share_capital_details', 'indent_level': 1},
            {'name': 'Issued and Fully Paid', 'line_type': 'line', 'sequence': 240, 'statement_section': 'share_capital_details', 'indent_level': 1},
            {'name': 'Number of Shares Outstanding', 'line_type': 'line', 'sequence': 250, 'statement_section': 'share_capital_details', 'indent_level': 1},

            # Note 5: Revenue
            {'name': 'Note 5: Revenue', 'line_type': 'header', 'sequence': 260, 'statement_section': 'revenue_details', 'bold': True, 'ifrs_reference': 'IFRS 15'},
            {'name': 'Sale of Goods', 'line_type': 'line', 'sequence': 270, 'statement_section': 'revenue_details', 'indent_level': 1},
            {'name': 'Rendering of Services', 'line_type': 'line', 'sequence': 280, 'statement_section': 'revenue_details', 'indent_level': 1},
            {'name': 'Other Revenue', 'line_type': 'line', 'sequence': 290, 'statement_section': 'revenue_details', 'indent_level': 1},
            {'name': 'Total Revenue', 'line_type': 'total', 'sequence': 300, 'statement_section': 'revenue_details', 'bold': True, 'underline': True},

            # Note 6: Subsequent Events
            {'name': 'Note 6: Subsequent Events', 'line_type': 'header', 'sequence': 310, 'statement_section': 'subsequent_events', 'bold': True, 'ifrs_reference': 'IAS 10'},
            {'name': 'Events after Reporting Period', 'line_type': 'line', 'sequence': 320, 'statement_section': 'subsequent_events', 'indent_level': 1},
        ]

        for line_data in lines:
            line_data['statement_id'] = self.id
            self.env['ifrs.statement.line'].create(line_data)

    def _populate_notes_data(self):
        """Populate notes with actual data from accounting records"""
        self.ensure_one()

        # Get detailed account data
        account_data = self._get_detailed_account_data()

        # Populate specific notes with actual figures
        for line in self.statement_line_ids:
            if line.statement_section == 'ppe_details':
                self._populate_ppe_note(line, account_data)
            elif line.statement_section == 'receivables_details':
                self._populate_receivables_note(line, account_data)
            elif line.statement_section == 'revenue_details':
                self._populate_revenue_note(line, account_data)
            elif line.statement_section == 'share_capital_details':
                self._populate_share_capital_note(line, account_data)

    def _get_detailed_account_data(self):
        """Get detailed account data for notes"""
        self.ensure_one()

        # Get all account movements for the period
        query = """
            SELECT aa.id, aa.code, aa.name, aa.account_type,
                   SUM(CASE WHEN am.date < %s THEN aml.debit - aml.credit ELSE 0 END) as opening_balance,
                   SUM(CASE WHEN am.date >= %s AND am.date <= %s THEN aml.debit ELSE 0 END) as period_debit,
                   SUM(CASE WHEN am.date >= %s AND am.date <= %s THEN aml.credit ELSE 0 END) as period_credit,
                   SUM(aml.debit - aml.credit) as closing_balance
            FROM account_account aa
            LEFT JOIN account_move_line aml ON aa.id = aml.account_id
            LEFT JOIN account_move am ON aml.move_id = am.id AND am.state = 'posted'
            WHERE aa.company_id = %s
            GROUP BY aa.id, aa.code, aa.name, aa.account_type
        """

        try:
            self.env.cr.execute(query, (
                self.date_from, self.date_from, self.date_to,
                self.date_from, self.date_to, self.company_id.id
            ))
            results = self.env.cr.fetchall()

            account_details = {}
            for acc_id, code, name, acc_type, opening, debit, credit, closing in results:
                account_details[acc_id] = {
                    'code': code or '',
                    'name': name or '',
                    'account_type': acc_type or '',
                    'opening_balance': opening or 0.0,
                    'period_debit': debit or 0.0,
                    'period_credit': credit or 0.0,
                    'closing_balance': closing or 0.0,
                }

            return account_details
        except Exception as e:
            _logger.error(f"Error retrieving detailed account data: {e}")
            return {}

    def _populate_ppe_note(self, line, account_data):
        """Populate Property, Plant and Equipment note"""
        # Find PPE accounts
        ppe_accounts = {acc_id: data for acc_id, data in account_data.items()
                       if isinstance(data, dict) and 'asset_fixed' in str(data.get('account_type', ''))}

        if line.name == 'Cost at Beginning of Year':
            line.current_amount = sum(data.get('opening_balance', 0.0) for data in ppe_accounts.values())
        elif line.name == 'Additions during the Year':
            line.current_amount = sum(data.get('period_debit', 0.0) for data in ppe_accounts.values())
        elif line.name == 'Cost at End of Year':
            line.current_amount = sum(data.get('closing_balance', 0.0) for data in ppe_accounts.values() if data.get('closing_balance', 0.0) > 0)

    def _populate_receivables_note(self, line, account_data):
        """Populate Trade and Other Receivables note"""
        # Find receivable accounts
        receivable_accounts = {acc_id: data for acc_id, data in account_data.items()
                              if isinstance(data, dict) and 'receivable' in str(data.get('account_type', ''))}

        if line.name == 'Trade Receivables':
            line.current_amount = sum(data.get('closing_balance', 0.0) for data in receivable_accounts.values())
        elif line.name == 'Total Trade and Other Receivables':
            line.current_amount = sum(data.get('closing_balance', 0.0) for data in receivable_accounts.values())

    def _populate_revenue_note(self, line, account_data):
        """Populate Revenue note"""
        # Find income accounts
        income_accounts = {acc_id: data for acc_id, data in account_data.items()
                          if isinstance(data, dict) and 'income' in str(data.get('account_type', ''))}

        if line.name == 'Sale of Goods':
            goods_accounts = {acc_id: data for acc_id, data in income_accounts.items()
                             if isinstance(data, dict) and 'name' in data and
                             any(keyword in str(data['name']).lower() for keyword in ['sales', 'goods', 'product'])}
            line.current_amount = sum(data.get('period_credit', 0.0) for data in goods_accounts.values())
        elif line.name == 'Rendering of Services':
            service_accounts = {acc_id: data for acc_id, data in income_accounts.items()
                               if isinstance(data, dict) and 'name' in data and
                               any(keyword in str(data['name']).lower() for keyword in ['service', 'consulting'])}
            line.current_amount = sum(data.get('period_credit', 0.0) for data in service_accounts.values())
        elif line.name == 'Total Revenue':
            line.current_amount = sum(data.get('period_credit', 0.0) for data in income_accounts.values())

    def _populate_share_capital_note(self, line, account_data):
        """Populate Share Capital note"""
        # Find equity accounts
        equity_accounts = {acc_id: data for acc_id, data in account_data.items()
                          if isinstance(data, dict) and 'equity' in str(data.get('account_type', ''))}

        if line.name == 'Issued and Fully Paid':
            share_accounts = {acc_id: data for acc_id, data in equity_accounts.items()
                             if isinstance(data, dict) and 'name' in data and
                             any(keyword in str(data['name']).lower() for keyword in ['share', 'capital'])}
            line.current_amount = sum(data.get('closing_balance', 0.0) for data in share_accounts.values())
