# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class IFRSReportTemplate(models.Model):
    _name = 'ifrs.report.template'
    _description = 'IFRS Report Template'
    _order = 'name'

    name = fields.Char(string='Template Name', required=True)
    
    template_type = fields.Selection([
        ('balance_sheet', 'Statement of Financial Position'),
        ('income_statement', 'Statement of Comprehensive Income'),
        ('cash_flow', 'Statement of Cash Flows'),
        ('equity_changes', 'Statement of Changes in Equity'),
        ('notes', 'Notes Template'),
        ('complete_set', 'Complete Set Template')
    ], string='Template Type', required=True)
    
    country_id = fields.Many2one(
        'res.country',
        string='Country',
        help='Country-specific template variations'
    )
    
    industry_id = fields.Many2one(
        'res.partner.industry',
        string='Industry',
        help='Industry-specific template'
    )
    
    active = fields.Boolean(string='Active', default=True)
    
    description = fields.Text(string='Description')
    
    template_content = fields.Html(string='Template Content')
    
    # Template Lines
    template_line_ids = fields.One2many(
        'ifrs.report.template.line',
        'template_id',
        string='Template Lines'
    )
    
    def action_apply_template(self, statement_id):
        """Apply template to financial statement"""
        statement = self.env['ifrs.financial.statement'].browse(statement_id)
        
        # Clear existing lines
        statement.statement_line_ids.unlink()
        
        # Create lines from template
        for template_line in self.template_line_ids:
            self.env['ifrs.statement.line'].create({
                'statement_id': statement_id,
                'name': template_line.name,
                'line_type': template_line.line_type,
                'statement_section': template_line.statement_section,
                'sequence': template_line.sequence,
                'ifrs_reference': template_line.ifrs_reference,
                'bold': template_line.bold,
                'italic': template_line.italic,
                'underline': template_line.underline,
                'indent_level': template_line.indent_level,
            })


class IFRSReportTemplateLine(models.Model):
    _name = 'ifrs.report.template.line'
    _description = 'IFRS Report Template Line'
    _order = 'sequence, id'

    template_id = fields.Many2one(
        'ifrs.report.template',
        string='Template',
        required=True,
        ondelete='cascade'
    )
    
    sequence = fields.Integer(string='Sequence', default=10)
    
    name = fields.Char(string='Line Description', required=True)
    
    line_type = fields.Selection([
        ('header', 'Header'),
        ('line', 'Line Item'),
        ('subtotal', 'Subtotal'),
        ('total', 'Total'),
        ('note', 'Note Reference')
    ], string='Line Type', required=True, default='line')
    
    statement_section = fields.Selection([
        # Balance Sheet Sections
        ('assets_current', 'Current Assets'),
        ('assets_non_current', 'Non-Current Assets'),
        ('liabilities_current', 'Current Liabilities'),
        ('liabilities_non_current', 'Non-Current Liabilities'),
        ('equity', 'Equity'),
        
        # Income Statement Sections
        ('revenue', 'Revenue'),
        ('cost_of_sales', 'Cost of Sales'),
        ('operating_expenses', 'Operating Expenses'),
        ('finance_costs', 'Finance Costs'),
        ('other_income', 'Other Income'),
        ('tax_expense', 'Tax Expense'),
        
        # Cash Flow Sections
        ('operating_activities', 'Operating Activities'),
        ('investing_activities', 'Investing Activities'),
        ('financing_activities', 'Financing Activities'),
        
        # Equity Changes
        ('share_capital', 'Share Capital'),
        ('retained_earnings', 'Retained Earnings'),
        ('other_reserves', 'Other Reserves'),
    ], string='Statement Section')
    
    ifrs_reference = fields.Char(
        string='IFRS Reference',
        help='Reference to specific IFRS standard'
    )
    
    # Presentation
    bold = fields.Boolean(string='Bold', default=False)
    italic = fields.Boolean(string='Italic', default=False)
    underline = fields.Boolean(string='Underline', default=False)
    indent_level = fields.Integer(string='Indent Level', default=0)
