# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_recurring_payments
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0-20220319\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-12 03:16+0000\n"
"PO-Revision-Date: 2022-06-11 22:18-0500\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_MX\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"X-Generator: Poedit 3.1\n"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__amount
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__amount
msgid "Amount"
msgstr "Monto"

#. module: om_recurring_payments
#: code:addons/om_recurring_payments/models/recurring_payment.py:0
#, python-format
msgid "Amount Must Be Non-Zero Positive Number"
msgstr "El monto debe ser un número positivo distinto de cero"

#. module: om_recurring_payments
#: code:addons/om_recurring_payments/models/recurring_payment.py:0
#, python-format
msgid "Cannot delete done records !"
msgstr "¡No se pueden eliminar los registros realizados!"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__company_id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__company_id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__company_id
msgid "Company"
msgstr "Compañia"

#. module: om_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_template_form
msgid "Confirm"
msgstr "Confirmar"

#. module: om_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
msgid "Create Payment"
msgstr "Crear pago"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__create_uid
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__create_uid
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__create_date
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__create_date
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__currency_id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__date
msgid "Date"
msgstr "Fecha"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__recurring_period__days
msgid "Days"
msgstr "Días"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__description
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__description
msgid "Description"
msgstr "Descripción"

#. module: om_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_template_form
msgid "Description..."
msgstr "Descripción..."

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__display_name
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__display_name
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__state__done
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__recurring_payment__state__done
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__recurring_payment_line__state__done
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
msgid "Done"
msgstr "Hecho"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__state__draft
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__recurring_payment__state__draft
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__recurring_payment_line__state__draft
msgid "Draft"
msgstr "Borrador"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__date_end
msgid "End Date"
msgstr "Fecha final"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__journal_state
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__journal_state
msgid "Generate Journal As"
msgstr "Generar diario como"

#. module: om_recurring_payments
#: model:ir.actions.server,name:om_recurring_payments.action_generate_recurring_payment_ir_actions_server
#: model:ir.cron,cron_name:om_recurring_payments.action_generate_recurring_payment
#: model:ir.cron,name:om_recurring_payments.action_generate_recurring_payment
msgid "Generate Recurring Payments"
msgstr "Generar pagos recurrentes"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__id
msgid "ID"
msgstr "ID"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__journal_id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__journal_id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__journal_id
msgid "Journal"
msgstr "Diario"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template____last_update
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment____last_update
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__write_uid
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__write_uid
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__write_date
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__write_date
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__recurring_period__months
msgid "Months"
msgstr "Meses"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__name
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__name
msgid "Name"
msgstr "Nombre"

#. module: om_recurring_payments
#: code:addons/om_recurring_payments/models/recurring_payment.py:0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__partner_id
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__partner_id
msgid "Partner"
msgstr "Cliente"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__payment_id
msgid "Payment"
msgstr "Pago"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__payment_type
msgid "Payment Type"
msgstr "Tipo de Pago"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__journal_state__posted
msgid "Posted"
msgstr "Publicado"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__recurring_payment__payment_type__inbound
msgid "Receive Money"
msgstr "Recibir dinero"

#. module: om_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
msgid "Recurring Entries"
msgstr "Entradas recurrentes"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__recurring_interval
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__recurring_interval
msgid "Recurring Interval"
msgstr "Intervalo recurrente"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__line_ids
msgid "Recurring Lines"
msgstr "Líneas recurrentes"

#. module: om_recurring_payments
#: model:ir.actions.act_window,name:om_recurring_payments.action_account_recurring_payment
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__recurring_payment_id
#: model:ir.ui.menu,name:om_recurring_payments.menu_recurring_payment
#: model:ir.ui.menu,name:om_recurring_payments.menu_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
msgid "Recurring Payment"
msgstr "Pago recurrente"

#. module: om_recurring_payments
#: model:ir.model,name:om_recurring_payments.model_recurring_payment_line
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
msgid "Recurring Payment Line"
msgstr "Línea de pago recurrente"

#. module: om_recurring_payments
#: model:ir.model,name:om_recurring_payments.model_recurring_payment
msgid "Recurring Payment("
msgstr "Pago recurrente("

#. module: om_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_account_recurring_payment_tree
msgid "Recurring Payments"
msgstr "Pagos recurrentes"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__recurring_period
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__recurring_period
msgid "Recurring Period"
msgstr "Período recurrente"

#. module: om_recurring_payments
#: model:ir.actions.act_window,name:om_recurring_payments.action_account_recurring_template
#: model:ir.model,name:om_recurring_payments.model_account_recurring_template
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__template_id
#: model:ir.ui.menu,name:om_recurring_payments.menu_recurring_template
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_account_recurring_template_tree
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_template_form
msgid "Recurring Template"
msgstr "Plantilla recurrente"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__recurring_payment__payment_type__outbound
msgid "Send Money"
msgstr "Enviar dinero"

#. module: om_recurring_payments
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_payment_form
#: model_terms:ir.ui.view,arch_db:om_recurring_payments.view_recurring_template_form
msgid "Set To Draft"
msgstr "Establecer a borrador"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__date_begin
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: om_recurring_payments
#: model:ir.model.fields,field_description:om_recurring_payments.field_account_recurring_template__state
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment__state
#: model:ir.model.fields,field_description:om_recurring_payments.field_recurring_payment_line__state
msgid "Status"
msgstr "Estatus"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__journal_state__draft
msgid "Un Posted"
msgstr "Sin publicar"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__recurring_period__weeks
msgid "Weeks"
msgstr "Semanas"

#. module: om_recurring_payments
#: model:ir.model.fields.selection,name:om_recurring_payments.selection__account_recurring_template__recurring_period__years
msgid "Years"
msgstr "Años"

#. module: om_recurring_payments
#: code:addons/om_recurring_payments/models/recurring_payment.py:0
#, python-format
msgid "You cannot Set to Draft as one of the line is already in done state"
msgstr ""
"No puede establecer como borrador porque una de las líneas ya está en "
"estado hecho"
