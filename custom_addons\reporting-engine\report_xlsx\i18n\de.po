# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_xlsx
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-12-23 03:49+0000\n"
"PO-Revision-Date: 2019-07-12 12:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German (https://www.transifex.com/oca/teams/23907/de/)\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 3.7.1\n"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_abstract
msgid "Abstract XLSX Report"
msgstr "Abstrakter XLSX-Bericht"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_partner_xlsx
#, fuzzy
msgid "Partner XLSX Report"
msgstr "Abstrakter XLSX-Bericht"

#. module: report_xlsx
#: model:ir.actions.report,name:report_xlsx.partner_xlsx
msgid "Print to XLSX"
msgstr "Drucke nach XLSX"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_ir_actions_report
msgid "Report Action"
msgstr "Berichtsaktion"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_ir_actions_report__report_type
msgid "Report Type"
msgstr "Berichtsart"

#. module: report_xlsx
#: model:ir.model.fields,help:report_xlsx.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""
"Die Art des Berichts, der erstellt wird, jeder mit eigener Darstellungsform. "
"HTML bedeutet, dass der Bericht unmittelbar in Ihrem Browser dargestellt "
"wird, PDF bedeutet, dass der Bericht mittels Wkhtmltopdf gewandelt wird und "
"vom Anwender heruntergeladen wird."

#. module: report_xlsx
#: model:ir.model.fields.selection,name:report_xlsx.selection__ir_actions_report__report_type__xlsx
msgid "XLSX"
msgstr "XLSX"

#, python-format
#~ msgid "%s model was not found"
#~ msgstr "%s Modell wurde nicht gefunden"

#, python-format
#~ msgid ""
#~ "A popup window with your report was blocked. You may need to change your "
#~ "browser settings to allow popup windows for this page."
#~ msgstr ""
#~ "Ein Popup-Fenster wurde abgewiesen. Sie werden vermutlich Ihre Browser-"
#~ "Einstellungen ändern müssen, damit die Anzeige dieser Seite möglich wird."

#~ msgid "Display Name"
#~ msgstr "Name anzeigen"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Zuletzt geändert am"

#, python-format
#~ msgid "Warning"
#~ msgstr "Hinweis"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "PDF"
#~ msgstr "PDF"

#~ msgid "Py3o"
#~ msgstr "Py3o"

#~ msgid "Text"
#~ msgstr "Text"

#~ msgid "XML"
#~ msgstr "XML"

#~ msgid "report.report_xlsx.partner_xlsx"
#~ msgstr "report.report_xlsx.partner_xlsx"
