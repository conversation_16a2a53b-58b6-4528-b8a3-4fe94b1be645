# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_followup
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-23 21:31+0000\n"
"PO-Revision-Date: 2023-11-24 06:22+0800\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 3.4.1\n"

#. module: om_account_followup
#: model:followup.line,description:om_account_followup.demo_followup_line3
msgid ""
"\n"
"                Dear %(partner_name)s,\n"
"\n"
"                Despite several reminders, your account is still not "
"settled.\n"
"\n"
"                Unless full payment is made in next 8 days, then legal "
"action\n"
"                for the recovery of the debt will be taken without further\n"
"                notice.\n"
"\n"
"                I trust that this action will prove unnecessary and details "
"of\n"
"                due payments is printed below.\n"
"\n"
"                In case of any queries concerning this matter, do not "
"hesitate\n"
"                to contact our accounting department.\n"
"\n"
"                Best Regards,\n"
"            "
msgstr ""
"\n"
"                親愛的%(partner_name)s，\n"
"\n"
"                儘管多次提醒，您的帳戶仍未結清。\n"
"\n"
"                除非在接下來的8天內進行全額付款，\n"
"                否則將會在不另行通知的情況下採取法律行動來追討欠款。\n"
"\n"
"                我相信這一舉措將是不必要的，未付款的詳細資訊如下所示。\n"
"\n"
"                如果對此事有任何疑問，請不要猶豫，隨時聯繫我們的會計部門。\n"
"\n"
"                最好的問候\n"
"            "

#. module: om_account_followup
#: model:followup.line,description:om_account_followup.demo_followup_line1
msgid ""
"\n"
"                Dear %(partner_name)s,\n"
"\n"
"                Exception made if there was a mistake of ours, it seems "
"that\n"
"                the following amount stays unpaid. Please, take appropriate\n"
"                measures in order to carry out this payment in the next 8 "
"days.\n"
"\n"
"                Would your payment have been carried out after this mail "
"was\n"
"                sent, please ignore this message. Do not hesitate to "
"contact\n"
"                our accounting department.\n"
"\n"
"                Best Regards,\n"
"            "
msgstr ""
"\n"
"                親愛的%(partner_name)s，\n"
"\n"
"                除非我們有所錯誤，否則似乎以下金額尚未付清。\n"
"                請採取適當措施，以便在接下來的8天內完成這筆付款。\n"
"\n"
"                如果您在此郵件發送後已經完成付款，\n"
"                請忽略此訊息。如果有任何疑問，\n"
"                請隨時聯繫我們的會計部門。\n"
"\n"
"                此致，\n"
"            "

#. module: om_account_followup
#: model:followup.line,description:om_account_followup.demo_followup_line2
msgid ""
"\n"
"                Dear %(partner_name)s,\n"
"\n"
"                We are disappointed to see that despite sending a reminder,\n"
"                that your account is now seriously overdue.\n"
"\n"
"                It is essential that immediate payment is made, otherwise "
"we\n"
"                will have to consider placing a stop on your account which\n"
"                means that we will no longer be able to supply your company\n"
"                with (goods/services).\n"
"                Please, take appropriate measures in order to carry out "
"this\n"
"                payment in the next 8 days.\n"
"\n"
"                If there is a problem with paying invoice that we are not "
"aware\n"
"                of, do not hesitate to contact our accounting department, "
"so\n"
"                that we can resolve the matter quickly.\n"
"\n"
"                Details of due payments is printed below.\n"
"\n"
"                Best Regards,\n"
"            "
msgstr ""
"\n"
"                親愛的%(partner_name)s，\n"
"\n"
"                 我們很失望地看到，儘管已經發送了催繳通知，\n"
"                您的帳戶目前仍然嚴重逾期。\n"
"\n"
"                迫切需要立即付款，否則我們將不得不考慮停止您的帳戶，\n"
"                這意味著我們將無法再向貴公司提供（商品/服務）。\n"
"\n"
"                請您在接下來的8天內採取適當措施，以便完成這筆付款。\n"
"\n"
"                如果您在支付發票方面遇到我們不知道的問題，請不要猶豫，\n"
"                立即聯繫我們的會計部門，以便我們能夠迅速解決此事。\n"
"\n"
"                未付款的詳細資訊如下所示。\n"
"                \n"
"                此致，\n"
"            "

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_level0
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"\n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    Exception made if there was a mistake of ours, it seems that the "
"following amount stays unpaid. Please, take\n"
"appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please "
"ignore this message. Do not hesitate to\n"
"contact our accounting department.  \n"
"\n"
"    </p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"   <br/>\n"
"${user.name}\n"
"\n"
"<br/>\n"
"<br/>\n"
"\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "
msgstr ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"\n"
"    <p>親愛的 ${object.name},</p>\n"
"    <p>\n"
"    \n"
"    除非我們有所錯誤，否則似乎以下金額尚未付清。請採取適當措施，以便在接下來"
"的8天內完成這筆付款。\n"
"\n"
"如果您在此郵件發送後已經完成付款，請忽略此訊息。如果有任何疑問，請隨時聯繫我"
"們的會計部門。\n"
"    </p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"   <br/>\n"
"${user.name}\n"
"\n"
"<br/>\n"
"<br/>\n"
"\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_level2
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    Despite several reminders, your account is still not settled.\n"
"Unless full payment is made in next 8 days, legal action for the recovery of "
"the debt will be taken without\n"
"further notice.\n"
"I trust that this action will prove unnecessary and details of due payments "
"is printed below.\n"
"In case of any queries concerning this matter, do not hesitate to contact "
"our accounting department.\n"
"</p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"<br/>\n"
"<br/>\n"
"\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "
msgstr ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    儘管多次提醒，您的帳戶仍未結清。\n"
"除非在接下來的8天內進行全額付款，否則將會在不另行通知的情況下採取法律行動來追"
"討欠款。\n"
"我相信這一舉措將是不必要的，未付款的詳細資訊如下所示。\n"
"如果對此事有任何疑問，請不要猶豫，隨時聯繫我們的會計部門。\n"
"</p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"<br/>\n"
"<br/>\n"
"\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_default
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    Exception made if there was a mistake of ours, it seems that the "
"following amount stays unpaid. Please, take\n"
"appropriate measures in order to carry out this payment in the next 8 days.\n"
"Would your payment have been carried out after this mail was sent, please "
"ignore this message. Do not hesitate to\n"
"contact our accounting department.\n"
"    </p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"<br/>\n"
"<br/>\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"<br/>\n"
"</div>\n"
"            "
msgstr ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"    \n"
"    <p>親愛的 ${object.name},</p>\n"
"    <p>\n"
"    除非是我們的錯誤造成的例外情況，否則似乎以下金額尚未支付。請您在接下來的8"
"天內採取適當措施完成此付款。\n"
"\n"
"如果您在本郵件發送後已經完成付款，請忽略此訊息。如有任何疑問，請隨時聯絡我們"
"的會計部門。    </p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"<br/>\n"
"<br/>\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"<br/>\n"
"</div>\n"
"            "

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_level1
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"   <p>\n"
"    We are disappointed to see that despite sending a reminder, that your "
"account is now seriously overdue.\n"
"It is essential that immediate payment is made, otherwise we will have to "
"consider placing a stop on your account\n"
"which means that we will no longer be able to supply your company with "
"(goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the "
"next 8 days.\n"
"If there is a problem with paying invoice that we are not aware of, do not "
"hesitate to contact our accounting\n"
"department. so that we can resolve the matter quickly.\n"
"Details of due payments is printed below.\n"
" </p>\n"
"<br/>\n"
"Best Regards,\n"
"    \n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"    \n"
"<br/>\n"
"<br/>\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "
msgstr ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, "
"255, 255); \">\n"
"    \n"
"    <p>親愛的 ${object.name},</p>\n"
"   <p>\n"
"    我們很失望地看到，儘管已經發送了催繳通知，您的帳戶目前仍然嚴重逾期。\n"
"迫切需要立即付款，否則我們將不得不考慮停止您的帳戶，這意味著我們將無法再向貴"
"公司提供（商品/服務）。\n"
"請您在接下來的8天內採取適當措施，以便完成這筆付款。\n"
"如果您在支付發票方面遇到我們不知道的問題，請不要猶豫，立即聯絡我們的會計部"
"門，以便我們能夠迅速解決此事。\n"
"未付款的詳細資訊如下所示。 </p>\n"
"<br/>\n"
"Best Regards,\n"
"    \n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"    \n"
"<br/>\n"
"<br/>\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " email(s) sent"
msgstr " 已發送電子郵件"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " email(s) should have been sent, but "
msgstr " 電子郵件應該已發送，但是 "

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " had unknown email address(es)"
msgstr " 有未知的電子郵件地址"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " letter(s) in report"
msgstr " 報告中的信函"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " manual action(s) assigned:"
msgstr " 手動操作分配："

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " will be sent"
msgstr " 將被發送"

#. module: om_account_followup
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_default
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_level0
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_level1
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_level2
msgid "${user.company_id.name} Payment Reminder"
msgstr "${user.company_id.name} 付款提醒"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "%s partners have no credits and as such the action is cleared"
msgstr "%s 合作夥伴目前沒有信用額度，因此該措施已被解除"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ", the latest payment follow-up was:"
msgstr "，最新的付款跟催是："

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": Current Date"
msgstr "與欄位值進行比較的日期，預設情況下使用目前日期"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ": 合作夥伴名稱"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": User Name"
msgstr ": 使用者名稱"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr "：用戶的公司名稱"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid ""
"<br/>\n"
"                                Customer ref:"
msgstr ""
"<br/>\n"
"                                客戶編碼:"

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_level1
msgid "A bit urging second payment follow-up reminder email"
msgstr "有點催第二次付款後續提醒郵件"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_followup
msgid "Account Follow-up"
msgstr "帳戶跟催"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Account Move line"
msgstr "帳戶明細"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__manual_action_note
msgid "Action To Do"
msgstr "待辦的行動"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Action to be taken e.g. Give a phonecall, Check if it's paid, ..."
msgstr "應採取的行動，例如打個電話，看看是否已付款，..."

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "After"
msgstr "之後"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
#, python-format
msgid "Amount"
msgstr "金額"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_amount_due
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_amount_due
msgid "Amount Due"
msgstr "到期金額"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_amount_overdue
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_amount_overdue
msgid "Amount Overdue"
msgstr "逾期金額"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Amount due"
msgstr "到期金額"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Anybody"
msgstr "任何人"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr "指派一名負責人"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__balance
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__balance
msgid "Balance"
msgstr "餘額"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.om_account_followup_stat_by_partner_search
msgid "Balance > 0"
msgstr "餘額 > 0"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_account_move_line__result
msgid "Balance Amount"
msgstr "餘額"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ""
"Below is the history of the transactions of this\n"
"                            customer. You can check \"No Follow-up\" in\n"
"                            order to exclude it from the next follow-up\n"
"                            actions."
msgstr ""
"以下是該客戶交易的歷史記錄。\n"
"                            您可以選擇“無催款”\n"
"                            以排除該客戶不進行下一步的跟催動作。"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__blocked
msgid "Blocked"
msgstr "已封鎖"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "Cancel"
msgstr "取消"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_print__test_print
msgid "Check if you want to print follow-ups without changing follow-up level."
msgstr "檢查是否要在不更改後續級別的情況下列印後續內容。"

#. module: om_account_followup
#: model_terms:ir.actions.act_window,help:om_account_followup.action_om_account_followup_definition_form
msgid "Click to define follow-up levels and their related actions."
msgstr "點擊以定義後續跟催級別及其相關操作。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Click to mark the action as done."
msgstr "點擊以標記此操作為已完成。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_sending_results
msgid "Close"
msgstr "關閉"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__company_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__company_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__company_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__company_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Company"
msgstr "公司"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_res_config_settings
msgid "Config Settings"
msgstr "設置"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_res_partner
msgid "Contact"
msgstr "聯繫人"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__create_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__create_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__create_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__create_uid
msgid "Created by"
msgstr "建立者"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__create_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__create_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__create_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__create_date
msgid "Created on"
msgstr "建立於"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__credit
msgid "Credit"
msgstr "貸方"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_tree
msgid "Customer Followup"
msgstr "客戶跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_note
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_note
msgid "Customer Payment Promise"
msgstr "客戶付款承諾"

#. module: om_account_followup
#: model:ir.model.constraint,message:om_account_followup.constraint_followup_line_days_uniq
msgid "Days of the follow-up levels must be different"
msgstr "後續跟催的級別天數必須不同"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__debit
msgid "Debit"
msgstr "借方"

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_default
msgid "Default payment follow-up reminder e-mail"
msgstr "預設付款後續提醒郵件"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__description
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
#, python-format
msgid "Description"
msgstr "描述"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: om_account_followup
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_s
msgid "Do Manual Follow-Ups"
msgstr "執行手動跟催"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_print__partner_lang
msgid ""
"Do not change message text, if you want to send email in partner language, "
"or configure from company"
msgstr ""
"請勿更改訊息文字，如果您想要以合作夥伴的語言發送電子郵件，或者從公司進行配置"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid ""
"Document: Customer account statement\n"
"                                <br/>\n"
"                                Date:"
msgstr ""
"文件：客戶帳結單\n"
"                                <br/>\n"
"                                日期:"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_sending_results
msgid "Download Letters"
msgstr "下載信件"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Due"
msgstr "到期"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Due Date"
msgstr "到期日"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__delay
msgid "Due Days"
msgstr "到期日"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__email_body
msgid "Email Body"
msgstr "郵件內文"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__email_subject
msgid "Email Subject"
msgstr "Email Subject"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__email_template_id
msgid "Email Template"
msgstr "Email模板"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Email not sent because of email address of partner not filled in"
msgstr "由於未填寫合作夥伴的電子郵件地址，電子郵件未發送"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__date_move
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__date_move
msgid "First move"
msgstr "第一步驟"

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_level0
msgid "First polite payment follow-up reminder email"
msgstr "第一封禮貌付款後續提醒電子郵件"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__followup_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__followup_id
msgid "Follow Ups"
msgstr "跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__followup_id
msgid "Follow-Up"
msgstr "跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__name
msgid "Follow-Up Action"
msgstr "跟催行動"

#. module: om_account_followup
#: model:ir.ui.menu,name:om_account_followup.menu_finance_followup
msgid "Follow-Ups"
msgstr "帳款跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__followup_line
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_main_menu
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_form
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_tree
msgid "Follow-up"
msgstr "跟催"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_line
msgid "Follow-up Criteria"
msgstr "跟催標準"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_account_move_line__followup_line_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Follow-up Level"
msgstr "跟催級別"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_om_account_followup_definition_form
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_menu
msgid "Follow-up Levels"
msgstr "跟催級別"

#. module: om_account_followup
#: model:ir.actions.report,name:om_account_followup.action_report_followup
msgid "Follow-up Report"
msgstr "跟催報告"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_responsible_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Follow-up Responsible"
msgstr "跟催負責人"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__date
msgid "Follow-up Sending Date"
msgstr "跟催發送日期"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_stat
msgid "Follow-up Statistics"
msgstr "跟催統計"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_stat_by_partner
msgid "Follow-up Statistics by Partner"
msgstr "合作夥伴跟催統計"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "跟催步驟"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Follow-up letter of "
msgstr "跟催信函 "

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_graph
msgid "Follow-up lines"
msgstr "跟催項目"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_followup_stat
#: model:ir.ui.menu,name:om_account_followup.menu_action_followup_stat_follow
msgid "Follow-ups Analysis"
msgstr "跟催分析"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Follow-ups Sent"
msgstr "已發送跟催信息"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Follow-ups To Do"
msgstr "跟催待辦事項"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Followup Level"
msgstr "跟催級別"

#. module: om_account_followup
#: model_terms:ir.actions.act_window,help:om_account_followup.action_om_account_followup_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in\n"
"                    days. It is\n"
"                    possible to use print and e-mail templates to send "
"specific\n"
"                    messages to\n"
"                    the customer."
msgstr ""
"對於每個步驟，請指定要採取的操作和延遲天數。\n"
"                   可以使用列印和電子郵件範本\n"
"                   向客戶發送特定的訊息。"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__sequence
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_sequence
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_sequence
msgid "Gives the sequence order when displaying a list of follow-up lines."
msgstr "給出顯示後續行列表時的順序。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Group By"
msgstr "分組按"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ""
"He said the problem was temporary and promised to pay 50% before 15th of "
"May, balance before 1st of July."
msgstr "他說問題是暫時的，並承諾在5月15日之前支付50%，在7月1日之前支付餘額。"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__id
msgid "ID"
msgstr "ID"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ""
"If not specified by the latest follow-up level, it will send from the "
"default email template"
msgstr "若最新跟催級別未指定，則從默認郵件模板發送"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Including journal entries marked as a litigation"
msgstr "包括標記為訴訟的日記分錄"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__invoice_partner_id
msgid "Invoice Address"
msgstr "發票地址"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
#, python-format
msgid "Invoice Date"
msgstr "發票日期"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Invoices Reminder"
msgstr "發票提醒"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_account_move_line
msgid "Journal Item"
msgstr "日記帳項目"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.account_manual_reconcile_action
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_move_line_reconcile_tree
msgid "Journal Items to Reconcile"
msgstr "待調節的日記帳明細"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__write_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__write_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__write_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__write_uid
msgid "Last Updated by"
msgstr "最後更新人"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__write_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__write_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__write_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__write_date
msgid "Last Updated on"
msgstr "最後更新時間"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__date_move_last
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__date_move_last
msgid "Last move"
msgstr "最後一步驟"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr "最近跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_date
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_date
msgid "Latest Follow-up Date"
msgstr "最後的跟催日期"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_level_id
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_level_id
msgid "Latest Follow-up Level"
msgstr "最新跟催水平"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_level_id_without_lit
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_level_id_without_lit
msgid "Latest Follow-up Level without litigation"
msgstr "最新跟催級別扣除訴訟"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Latest Follow-up Month"
msgstr "最新的跟催月份"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_date
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_date
msgid "Latest date that the follow-up level of the partner was changed"
msgstr "合作夥伴跟催級別更改的最新日期"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__date_followup
msgid "Latest follow-up"
msgstr "最新跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__date_followup
msgid "Latest followup"
msgstr "最新跟催"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Lit."
msgstr "逾期."

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Litigation"
msgstr "訴訟"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "Manual Action"
msgstr "手動操作"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_customer_followup
msgid "Manual Follow-Ups"
msgstr "手動跟催"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Maturity Date"
msgstr "到期日"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__max_followup_id
msgid "Max Follow Up Level"
msgstr "最大跟催級別"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_customer_my_followup
#: model:ir.ui.menu,name:om_account_followup.menu_sale_followup
msgid "My Follow-Ups"
msgstr "我的後續跟催"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "My Follow-ups"
msgstr "我的後續跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__name
msgid "Name"
msgstr "名稱"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__needprinting
msgid "Needs Printing"
msgstr "需要列印"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_next_action
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_next_action
msgid "Next Action"
msgstr "下個動作"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr "下一行動日期"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "No Responsible"
msgstr "未設置負責人"

#. module: om_account_followup
#: model_terms:ir.actions.act_window,help:om_account_followup.account_manual_reconcile_action
msgid "No journal items found."
msgstr "未找到日記項目。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Not Litigation"
msgstr "非訴訟"

#. module: om_account_followup
#: model:ir.model.constraint,message:om_account_followup.constraint_followup_followup_company_uniq
msgid "Only one follow-up per company is allowed"
msgstr "每家公司只允許進行一次跟催"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr "(可選) 您可以於此欄位設置使用者, 由使用者負責該操作。"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Overdue email sent to %s, "
msgstr "逾期電子郵件已發送至 %s， "

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__partner_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__partner_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Partner"
msgstr "合作夥伴"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.account_move_line_partner_tree
msgid "Partner Entries"
msgstr "合作夥伴分錄"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.om_account_followup_stat_by_partner_search
#: model_terms:ir.ui.view,arch_db:om_account_followup.om_account_followup_stat_by_partner_tree
msgid "Partner to Remind"
msgstr "合作夥伴提醒"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__partner_ids
msgid "Partners"
msgstr "合作夥伴"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Partners with Overdue Credits"
msgstr "逾期信用合作夥伴"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Payment Follow-up"
msgstr "付款催收管理"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_note
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_note
msgid "Payment Note"
msgstr "付款備註"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_print
msgid "Print Follow-up & Send Mail to Customers"
msgstr "列印跟催並向客戶發送郵件"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Print Overdue Payments"
msgstr "列印逾期付款"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Print overdue payments report independent of follow-up line"
msgstr "獨立於後續銘系列印逾期付款報告"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__description
msgid "Printed Message"
msgstr "列印訊息"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Printed overdue payments report"
msgstr "列印逾期付款報告"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Ref"
msgstr "參考編號"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Reference"
msgstr "參考"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_report_om_account_followup_report_followup
msgid "Report Followup"
msgstr "報告跟催"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Responsible of credit collection"
msgstr "信用收款的負責人"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_sending_results
msgid "Results from the sending of the different letters and emails"
msgstr "不同信函和電子郵件發送的結果"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_filter
msgid "Search Follow-up"
msgstr "搜索跟催"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__email_conf
msgid "Send Email Confirmation"
msgstr "發送電子郵件確認"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__partner_lang
msgid "Send Email in Partner Language"
msgstr "以合作夥伴語言發送電子郵件"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_om_account_followup_print
msgid "Send Follow-Ups"
msgstr "發送跟催資訊"

#. module: om_account_followup
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_print_menu
msgid "Send Letters and Emails"
msgstr "發送信件和電子郵件"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Send Letters and Emails: Actions Summary"
msgstr "發送信件和電子郵件：行動摘要"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Send Overdue Email"
msgstr "發送逾期郵件"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__send_letter
msgid "Send a Letter"
msgstr "寄信"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "Send a Letter or Email"
msgstr "發送信件或電子郵件"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__send_email
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "Send an Email"
msgstr "通過信件發送"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "Send emails and generate letters"
msgstr "發送電子郵件並生成信件"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "Send follow-ups"
msgstr "發送跟催資訊"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__sequence
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_sequence
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_sequence
msgid "Sequence"
msgstr "序列"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__summary
msgid "Summary"
msgstr "摘要"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_sending_results
msgid "Summary of actions"
msgstr "行動概要"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__test_print
msgid "Test Print"
msgstr "測試列印"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "The"
msgstr "此"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/report/followup_print.py:0
#, python-format
msgid ""
"The followup plan defined for the current company does not have any followup "
"action."
msgstr "當前公司定義的後續跟催計劃沒有任何後續行動。"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_level_id
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_level_id
msgid "The maximum follow-up level"
msgstr "最大跟催水平"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_level_id_without_lit
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_level_id_without_lit
msgid ""
"The maximum follow-up level without taking into account the account move "
"lines with litigation"
msgstr "在不考慮訴訟帳戶明細的情況下，最大的後續級別"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""
"在發送催繳通知之前，從發票到期日之後等待的天數。如果您想提前發送禮貌的警示，"
"也可以是負數。"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid ""
"The partner does not have any accounting entries to print in the overdue "
"report for the current company."
msgstr "合作夥伴在當前公司的逾期報告中沒有任何可列印的會計分錄。"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "There is no followup plan defined for the current company."
msgstr "目前公司沒有製定後續計劃。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid ""
"This action will send follow-up emails, print the\n"
"                        letters and\n"
"                        set the manual actions per customer, according to "
"the\n"
"                        follow-up levels defined."
msgstr ""
"此操作將根據所定義的後續級別，\n"
"                        向客戶發送後續跟進郵件，\n"
"                        列印信件，並設置手動操作。"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_print__date
msgid "This field allow you to select a forecast date to plan your follow-ups"
msgstr "這個欄位允許您選擇一個預測日期來計劃您的後續跟催"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_next_action
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_next_action
msgid ""
"This is the next action to be taken.  It will automatically be set when the "
"partner gets a follow-up level that requires a manual action. "
msgstr ""
"這是需要採取的下一步操作。當合作夥伴達到需要手動操作的後續跟催級別時，它將自"
"動設置。 "

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_next_action_date
msgid ""
"This is when the manual follow-up is needed. The date will be set to the "
"current date when the partner gets a follow-up level that requires a manual "
"action. Can be practical to set manually e.g. to see if he keeps his "
"promises."
msgstr ""
"這時候就需要人工跟催了。該日期將設置為合作夥伴獲得需要手動操作的後續級別時的"
"當前日期。可以手動設置，例如看看他是否遵守諾言。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_form
msgid ""
"To remind customers of paying their invoices, you can\n"
"                        define different actions depending on how severely\n"
"                        overdue the customer is. These actions are bundled\n"
"                        into follow-up levels that are triggered when the "
"due\n"
"                        date of an invoice has passed a certain\n"
"                        number of days. If there are other overdue invoices "
"for\n"
"                        the\n"
"                        same customer, the actions of the most\n"
"                        overdue invoice will be executed."
msgstr ""
"為了提醒客戶支付他們的發票，\n"
"                        您可以根據客戶逾期的嚴重程度來定義不同的操作。\n"
"                        這些操作被捆綁到不同的後續跟催級別中，\n"
"                        當發票的到期日過了特定的天數時觸發。\n"
"                        如果同一客戶還有其他逾期的發票，\n"
"                        則將執行最逾期發票的操作。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.account_move_line_partner_tree
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_move_line_reconcile_tree
msgid "Total credit"
msgstr "貸方總計"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.account_move_line_partner_tree
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_move_line_reconcile_tree
msgid "Total debit"
msgstr "借方總計"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Total:"
msgstr "總計:"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "未調節的 Aml"

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_level2
msgid "Urging payment follow-up reminder email"
msgstr "催促付款後續提醒郵件"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__send_letter
msgid "When processing, it will print a letter"
msgstr "處理時，會列印信件"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__send_email
msgid "When processing, it will send an email"
msgstr "處理時，會發送一封電子郵件"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr "在處理時，它將設定該客戶需要採取的手動操作。 "

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_earliest_due_date
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_earliest_due_date
msgid "Worst Due Date"
msgstr "最差到期日"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter,\n"
"                            according to the level of the follow-up. You "
"can\n"
"                            use the following keywords in the text. Don't\n"
"                            forget to translate in all languages you "
"installed\n"
"                            using to top right icon."
msgstr ""
"根據跟催的級別，在信函中撰寫以下引言。\n"
"                            您可以在文本中使用以下關鍵詞。\n"
"                            請不要忘記使用右上角的圖標將其翻譯為您安裝的所有"
"語言。"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid ""
"You became responsible to do the next action for the payment follow-up of"
msgstr "您已成為帳款跟催的下一步操作負責人"

#. module: om_account_followup
#. odoo-python
#: code:addons/om_account_followup/models/followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""
"您的描述無效，請使用正確的圖例，或者如果您想使用百分比字符，請使用 %%。"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "逾期天數，請執行以下操作："

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "or"
msgstr "或"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "⇾ Mark as Done"
msgstr "標為已完成"
