# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sql_export_mail
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: sql_export_mail
#: model:mail.template,body_html:sql_export_mail.sql_export_mailer
msgid ""
"<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; \">\n"
"\n"
"    <p>You will find the report <t t-out=\"object.name or ''\"></t> as an attachment of the mail.</p>\n"
"\n"
"</div>\n"
"        "
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields,help:sql_export_mail.field_sql_export__mail_user_ids
msgid ""
"Add the users who want to receive the report by e-mail. You need to link the"
" sql query with a cron to send mail automatically"
msgstr ""

#. module: sql_export_mail
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Create Cron"
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields,field_description:sql_export_mail.field_sql_export__cron_ids
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Crons"
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields.selection,name:sql_export_mail.selection__sql_export__mail_condition__not_empty
msgid "File Not Empty"
msgstr ""

#. module: sql_export_mail
#. odoo-python
#: code:addons/sql_export_mail/models/sql_export.py:0
#, python-format
msgid ""
"It is not possible to execute and send a query automatically by mail if "
"there are parameters to fill"
msgstr ""

#. module: sql_export_mail
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Mail"
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields,field_description:sql_export_mail.field_sql_export__mail_condition
msgid "Mail Condition"
msgstr ""

#. module: sql_export_mail
#: model:mail.template,name:sql_export_mail.sql_export_mailer
msgid "SQL Export"
msgstr ""

#. module: sql_export_mail
#: model:ir.model,name:sql_export_mail.model_sql_export
msgid "SQL export"
msgstr ""

#. module: sql_export_mail
#. odoo-python
#: code:addons/sql_export_mail/models/sql_export.py:0
#, python-format
msgid "The user does not have any e-mail address."
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields,field_description:sql_export_mail.field_sql_export__mail_user_ids
msgid "User to notify"
msgstr ""

#. module: sql_export_mail
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Users Notified by e-mail"
msgstr ""

#. module: sql_export_mail
#: model:mail.template,subject:sql_export_mail.sql_export_mailer
msgid "{{object.name or ''}}"
msgstr ""
