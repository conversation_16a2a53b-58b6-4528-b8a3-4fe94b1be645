#!/usr/bin/env python3
"""
Budget vs Actual Report Script
This script connects to your Odoo database and generates a budget vs actual report
"""

import psycopg2
import pandas as pd
from datetime import datetime, date
import sys

def connect_to_database():
    """Connect to the Odoo database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="erp",
            user="odoo",  # Replace with your database user
            password="odoo"  # Replace with your database password
        )
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None

def get_budget_vs_actual_data(conn, date_from, date_to):
    """Get budget vs actual data from the database"""
    
    # Query to get actual amounts by account
    actual_query = """
    SELECT 
        aa.code as account_code,
        aa.name as account_name,
        SUM(aml.balance) as actual_amount
    FROM account_move_line aml
    JOIN account_account aa ON aml.account_id = aa.id
    JOIN account_move am ON aml.move_id = am.id
    WHERE aml.date >= %s 
        AND aml.date <= %s 
        AND am.state = 'posted'
    GROUP BY aa.code, aa.name, aa.id
    ORDER BY aa.code
    """
    
    # Query to get budget amounts by account (if budget module is available)
    budget_query = """
    SELECT 
        aa.code as account_code,
        aa.name as account_name,
        SUM(bl.planned_amount) as budget_amount
    FROM budget_lines bl
    JOIN account_budget_post abp ON bl.general_budget_id = abp.id
    JOIN account_budget_rel abr ON abp.id = abr.budget_id
    JOIN account_account aa ON abr.account_id = aa.id
    WHERE bl.date_from <= %s 
        AND bl.date_to >= %s
    GROUP BY aa.code, aa.name, aa.id
    ORDER BY aa.code
    """
    
    try:
        # Get actual data
        actual_df = pd.read_sql_query(actual_query, conn, params=[date_from, date_to])
        
        # Try to get budget data
        try:
            budget_df = pd.read_sql_query(budget_query, conn, params=[date_to, date_from])
        except Exception as e:
            print(f"Budget module not available or error: {e}")
            budget_df = pd.DataFrame(columns=['account_code', 'account_name', 'budget_amount'])
        
        # Merge actual and budget data
        report_df = pd.merge(actual_df, budget_df, on=['account_code', 'account_name'], how='outer')
        report_df = report_df.fillna(0)
        
        # Calculate variance
        report_df['variance'] = report_df['actual_amount'] - report_df['budget_amount']
        report_df['variance_percent'] = report_df.apply(
            lambda row: (row['variance'] / row['budget_amount'] * 100) if row['budget_amount'] != 0 else 0, 
            axis=1
        )
        
        return report_df
        
    except Exception as e:
        print(f"Error querying data: {e}")
        return None

def generate_html_report(df, date_from, date_to):
    """Generate HTML report"""
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Budget vs Actual Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ text-align: center; margin-bottom: 30px; }}
            .info {{ margin-bottom: 20px; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .text-right {{ text-align: right; }}
            .favorable {{ background-color: #d4edda; }}
            .unfavorable {{ background-color: #f8d7da; }}
            .neutral {{ background-color: #fff3cd; }}
            .totals {{ font-weight: bold; background-color: #e9ecef; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Budget vs Actual Report</h1>
            <div class="info">
                <strong>Period:</strong> {date_from} to {date_to}<br>
                <strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Account Code</th>
                    <th>Account Name</th>
                    <th class="text-right">Budget Amount</th>
                    <th class="text-right">Actual Amount</th>
                    <th class="text-right">Variance</th>
                    <th class="text-right">Variance %</th>
                </tr>
            </thead>
            <tbody>
    """
    
    total_budget = 0
    total_actual = 0
    
    for _, row in df.iterrows():
        # Determine row class based on variance
        if row['variance_percent'] > 10:
            row_class = 'favorable'
        elif row['variance_percent'] < -10:
            row_class = 'unfavorable'
        else:
            row_class = 'neutral'
        
        total_budget += row['budget_amount']
        total_actual += row['actual_amount']
        
        html += f"""
                <tr class="{row_class}">
                    <td>{row['account_code']}</td>
                    <td>{row['account_name']}</td>
                    <td class="text-right">{row['budget_amount']:,.2f}</td>
                    <td class="text-right">{row['actual_amount']:,.2f}</td>
                    <td class="text-right">{row['variance']:,.2f}</td>
                    <td class="text-right">{row['variance_percent']:.2f}%</td>
                </tr>
        """
    
    total_variance = total_actual - total_budget
    total_variance_percent = (total_variance / total_budget * 100) if total_budget != 0 else 0
    
    html += f"""
            </tbody>
            <tfoot>
                <tr class="totals">
                    <td colspan="2">TOTAL</td>
                    <td class="text-right">{total_budget:,.2f}</td>
                    <td class="text-right">{total_actual:,.2f}</td>
                    <td class="text-right">{total_variance:,.2f}</td>
                    <td class="text-right">{total_variance_percent:.2f}%</td>
                </tr>
            </tfoot>
        </table>
        
        <div style="margin-top: 30px;">
            <h3>Variance Analysis Legend:</h3>
            <div style="margin: 10px 0;">
                <span style="background-color: #d4edda; padding: 5px; margin-right: 10px;">Favorable (> 10%)</span>
                <span style="background-color: #fff3cd; padding: 5px; margin-right: 10px;">Within Range (±10%)</span>
                <span style="background-color: #f8d7da; padding: 5px;">Unfavorable (< -10%)</span>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html

def main():
    """Main function"""
    print("Budget vs Actual Report Generator")
    print("=" * 40)
    
    # Get date range from user
    date_from = input("Enter start date (YYYY-MM-DD) or press Enter for current month start: ").strip()
    if not date_from:
        today = date.today()
        date_from = today.replace(day=1).strftime('%Y-%m-%d')
    
    date_to = input("Enter end date (YYYY-MM-DD) or press Enter for today: ").strip()
    if not date_to:
        date_to = date.today().strftime('%Y-%m-%d')
    
    print(f"Generating report for period: {date_from} to {date_to}")
    
    # Connect to database
    conn = connect_to_database()
    if not conn:
        return
    
    # Get data
    print("Fetching data...")
    df = get_budget_vs_actual_data(conn, date_from, date_to)
    
    if df is None or df.empty:
        print("No data found for the specified period.")
        return
    
    # Generate report
    print("Generating HTML report...")
    html_report = generate_html_report(df, date_from, date_to)
    
    # Save report
    filename = f"budget_vs_actual_report_{date_from}_to_{date_to}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print(f"Report saved as: {filename}")
    print(f"Found {len(df)} accounts with data")
    
    # Display summary
    total_budget = df['budget_amount'].sum()
    total_actual = df['actual_amount'].sum()
    total_variance = total_actual - total_budget
    
    print("\nSummary:")
    print(f"Total Budget: {total_budget:,.2f}")
    print(f"Total Actual: {total_actual:,.2f}")
    print(f"Total Variance: {total_variance:,.2f}")
    
    conn.close()

if __name__ == "__main__":
    main()
