# Generated by Django 4.2.21 on 2025-07-19 20:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_country_phone_code'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountPayment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='/', max_length=255)),
                ('payment_type', models.CharField(choices=[('outbound', 'Send Money'), ('inbound', 'Receive Money'), ('transfer', 'Internal Transfer')], max_length=20)),
                ('partner_type', models.CharField(blank=True, choices=[('customer', 'Customer'), ('supplier', 'Vendor')], max_length=20, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('ref', models.CharField(blank=True, help_text='Payment reference', max_length=255)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('sent', 'Sent'), ('reconciled', 'Reconciled'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('destination_account_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payment_destination_account', to='accounting.accountaccount')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.accountjournal')),
                ('move_id', models.ForeignKey(blank=True, help_text='Generated journal entry', null=True, on_delete=django.db.models.deletion.PROTECT, to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('reconciled_invoice_ids', models.ManyToManyField(blank=True, help_text='Reconciled invoices', related_name='payment_reconciled_invoices', to='accounting.accountmove')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Payments',
            },
        ),
    ]
