# Generated by Django 4.2.21 on 2025-07-20 12:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0004_rename_state_countrystate_countrygroup'),
    ]

    operations = [
        migrations.CreateModel(
            name='CrmTeam',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Team Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('use_leads', models.BooleanField(default=True, help_text='Use Leads')),
                ('use_opportunities', models.<PERSON>olean<PERSON>ield(default=True, help_text='Use Opportunities')),
                ('invoiced_target', models.DecimalField(decimal_places=2, default=0.0, help_text='Invoicing Target', max_digits=20)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('member_ids', models.ManyToManyField(blank=True, help_text='Team Members', related_name='crm_teams', to=settings.AUTH_USER_MODEL)),
                ('user_id', models.ForeignKey(blank=True, help_text='Team Leader', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='crm_team_leader', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CrmTag',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CrmStage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Stage Name', max_length=255)),
                ('sequence', models.IntegerField(default=1)),
                ('is_won', models.BooleanField(default=False, help_text='Is Won Stage')),
                ('fold', models.BooleanField(default=False, help_text='Folded in Kanban')),
                ('probability', models.FloatField(default=0.0, help_text='Probability (%)')),
                ('requirements', models.TextField(blank=True, help_text='Requirements to reach this stage')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('team_ids', models.ManyToManyField(blank=True, help_text='Specific teams for this stage', to='crm.crmteam')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CrmLostReason',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Lost Reason', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CrmLead',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Opportunity/Lead Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('type', models.CharField(choices=[('lead', 'Lead'), ('opportunity', 'Opportunity')], default='lead', max_length=20)),
                ('priority', models.CharField(choices=[('0', 'Low'), ('1', 'Normal'), ('2', 'High'), ('3', 'Very High')], default='1', max_length=1)),
                ('partner_name', models.CharField(blank=True, help_text='Customer Name', max_length=255)),
                ('contact_name', models.CharField(blank=True, help_text='Contact Name', max_length=255)),
                ('title', models.CharField(blank=True, help_text='Title', max_length=255)),
                ('email_from', models.EmailField(blank=True, help_text='Email', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Phone', max_length=50)),
                ('mobile', models.CharField(blank=True, help_text='Mobile', max_length=50)),
                ('website', models.URLField(blank=True, help_text='Website')),
                ('street', models.CharField(blank=True, max_length=255)),
                ('street2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('zip', models.CharField(blank=True, max_length=20)),
                ('expected_revenue', models.DecimalField(decimal_places=2, default=0.0, help_text='Expected Revenue', max_digits=20)),
                ('prorated_revenue', models.DecimalField(decimal_places=2, default=0.0, help_text='Prorated Revenue', max_digits=20)),
                ('recurring_revenue', models.DecimalField(decimal_places=2, default=0.0, help_text='Recurring Revenue', max_digits=20)),
                ('recurring_revenue_monthly', models.DecimalField(decimal_places=2, default=0.0, help_text='Monthly Recurring Revenue', max_digits=20)),
                ('probability', models.FloatField(default=0.0, help_text='Success Probability (%)')),
                ('date_deadline', models.DateField(blank=True, help_text='Expected Closing', null=True)),
                ('date_closed', models.DateTimeField(blank=True, help_text='Closed Date', null=True)),
                ('date_conversion', models.DateTimeField(blank=True, help_text='Conversion Date', null=True)),
                ('date_last_stage_update', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(blank=True, help_text='Notes')),
                ('source_id', models.CharField(blank=True, help_text='Source', max_length=255)),
                ('referred', models.CharField(blank=True, help_text='Referred By', max_length=255)),
                ('day_open', models.FloatField(default=0.0, help_text='Days to Assign')),
                ('day_close', models.FloatField(default=0.0, help_text='Days to Close')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('country_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.country')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('lost_reason_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmlostreason')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.partner')),
                ('stage_id', models.ForeignKey(blank=True, help_text='Stage', null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmstage')),
                ('state_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.countrystate')),
                ('team_id', models.ForeignKey(blank=True, help_text='Sales Team', null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmteam')),
                ('user_id', models.ForeignKey(blank=True, help_text='Salesperson', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CrmActivityType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Activity Type', max_length=255)),
                ('category', models.CharField(default='default', max_length=50)),
                ('sequence', models.IntegerField(default=1)),
                ('delay_count', models.IntegerField(default=0, help_text='Number of days/hours')),
                ('delay_unit', models.CharField(choices=[('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], default='days', max_length=10)),
                ('summary', models.CharField(blank=True, help_text='Default Summary', max_length=255)),
                ('default_note', models.TextField(blank=True, help_text='Default Note')),
                ('chaining_type', models.CharField(choices=[('suggest', 'Suggest Next Activity'), ('trigger', 'Trigger Next Activity')], default='suggest', max_length=20)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CrmActivity',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('summary', models.CharField(help_text='Summary', max_length=255)),
                ('note', models.TextField(blank=True, help_text='Note')),
                ('res_model', models.CharField(default='crm.lead', max_length=255)),
                ('res_id', models.PositiveIntegerField()),
                ('date_deadline', models.DateField(help_text='Due Date')),
                ('date_done', models.DateTimeField(blank=True, null=True)),
                ('state', models.CharField(choices=[('overdue', 'Overdue'), ('today', 'Today'), ('planned', 'Planned'), ('done', 'Done')], default='planned', max_length=20)),
                ('feedback', models.TextField(blank=True, help_text='Feedback')),
                ('activity_type_id', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='crm.crmactivitytype')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('lead_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='crm.crmlead')),
                ('user_id', models.ForeignKey(help_text='Assigned to', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='crmteam',
            index=models.Index(fields=['name'], name='crm_crmteam_name_bbe08d_idx'),
        ),
        migrations.AddIndex(
            model_name='crmteam',
            index=models.Index(fields=['company_id'], name='crm_crmteam_company_cf76d0_idx'),
        ),
        migrations.AddConstraint(
            model_name='crmteam',
            constraint=models.UniqueConstraint(fields=('name', 'company_id'), name='crm_team_name_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='crmstage',
            index=models.Index(fields=['sequence'], name='crm_crmstag_sequenc_eae0b0_idx'),
        ),
        migrations.AddConstraint(
            model_name='crmstage',
            constraint=models.CheckConstraint(check=models.Q(('probability__gte', 0), ('probability__lte', 100)), name='crm_stage_probability_range'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['type'], name='crm_crmlead_type_808501_idx'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['user_id'], name='crm_crmlead_user_id_fe4ca5_idx'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['team_id'], name='crm_crmlead_team_id_d4546b_idx'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['stage_id'], name='crm_crmlead_stage_i_15d40a_idx'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['partner_id'], name='crm_crmlead_partner_ba907d_idx'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['date_deadline'], name='crm_crmlead_date_de_3d64d8_idx'),
        ),
        migrations.AddIndex(
            model_name='crmlead',
            index=models.Index(fields=['probability'], name='crm_crmlead_probabi_1a97da_idx'),
        ),
        migrations.AddConstraint(
            model_name='crmlead',
            constraint=models.CheckConstraint(check=models.Q(('probability__gte', 0), ('probability__lte', 100)), name='crm_lead_probability_range'),
        ),
        migrations.AddConstraint(
            model_name='crmlead',
            constraint=models.CheckConstraint(check=models.Q(('expected_revenue__gte', 0)), name='crm_lead_positive_revenue'),
        ),
        migrations.AddIndex(
            model_name='crmactivity',
            index=models.Index(fields=['user_id'], name='crm_crmacti_user_id_ef3088_idx'),
        ),
        migrations.AddIndex(
            model_name='crmactivity',
            index=models.Index(fields=['date_deadline'], name='crm_crmacti_date_de_95f320_idx'),
        ),
        migrations.AddIndex(
            model_name='crmactivity',
            index=models.Index(fields=['state'], name='crm_crmacti_state_76296e_idx'),
        ),
        migrations.AddIndex(
            model_name='crmactivity',
            index=models.Index(fields=['lead_id'], name='crm_crmacti_lead_id_883820_idx'),
        ),
    ]
