<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- IFRS Compliance Check Views -->
        <record id="view_ifrs_compliance_check_form" model="ir.ui.view">
            <field name="name">ifrs.compliance.check.form</field>
            <field name="model">ifrs.compliance.check</field>
            <field name="arch" type="xml">
                <form string="IFRS Compliance Check">
                    <header>
                        <button name="action_mark_resolved" type="object"
                                string="Mark Resolved" class="btn-success"
                                invisible="resolved == True"/>
                        <button name="action_reopen" type="object"
                                string="Reopen" class="btn-secondary"
                                invisible="resolved == False"/>
                        <field name="status" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="ifrs_standard"/>
                                <field name="priority"/>
                                <field name="resolved"/>
                            </group>
                            <group>
                                <field name="statement_id"/>
                                <field name="responsible_user"/>
                                <field name="check_date"/>
                                <field name="resolution_date" invisible="resolved == False"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Description" name="description">
                                <field name="description"/>
                            </page>
                            <page string="Findings" name="findings">
                                <field name="findings"/>
                            </page>
                            <page string="Recommendations" name="recommendations">
                                <field name="recommendations"/>
                            </page>
                            <page string="Resolution" name="resolution" invisible="resolved == False">
                                <field name="resolution_notes"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_ifrs_compliance_check_tree" model="ir.ui.view">
            <field name="name">ifrs.compliance.check.tree</field>
            <field name="model">ifrs.compliance.check</field>
            <field name="arch" type="xml">
                <tree string="IFRS Compliance Checks">
                    <field name="name"/>
                    <field name="ifrs_standard"/>
                    <field name="status" decoration-success="status=='passed'" 
                           decoration-warning="status=='warning'" decoration-danger="status=='failed'"/>
                    <field name="priority"/>
                    <field name="resolved"/>
                    <field name="responsible_user"/>
                    <field name="check_date"/>
                </tree>
            </field>
        </record>

        <record id="action_ifrs_compliance_check" model="ir.actions.act_window">
            <field name="name">IFRS Compliance Checks</field>
            <field name="res_model">ifrs.compliance.check</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- IFRS Report Template Views -->
        <record id="view_ifrs_report_template_form" model="ir.ui.view">
            <field name="name">ifrs.report.template.form</field>
            <field name="model">ifrs.report.template</field>
            <field name="arch" type="xml">
                <form string="IFRS Report Template">
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="template_type"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="country_id"/>
                                <field name="industry_id"/>
                            </group>
                        </group>
                        <field name="description"/>
                        <notebook>
                            <page string="Template Lines" name="lines">
                                <field name="template_line_ids">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="line_type"/>
                                        <field name="statement_section"/>
                                        <field name="ifrs_reference"/>
                                        <field name="bold"/>
                                        <field name="italic"/>
                                        <field name="underline"/>
                                        <field name="indent_level"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Template Content" name="content">
                                <field name="template_content" widget="html"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_ifrs_report_template_tree" model="ir.ui.view">
            <field name="name">ifrs.report.template.tree</field>
            <field name="model">ifrs.report.template</field>
            <field name="arch" type="xml">
                <tree string="IFRS Report Templates">
                    <field name="name"/>
                    <field name="template_type"/>
                    <field name="country_id"/>
                    <field name="industry_id"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <record id="action_ifrs_report_template" model="ir.actions.act_window">
            <field name="name">IFRS Report Templates</field>
            <field name="res_model">ifrs.report.template</field>
            <field name="view_mode">tree,form</field>
        </record>

    </data>
</odoo>
