from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
import uuid

class BaseModel(models.Model):
    """Base model with common fields for all ERP models"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    create_uid = models.ForeignKey(User, on_delete=models.PROTECT, related_name='%(class)s_created')
    write_uid = models.ForeignKey(User, on_delete=models.PROTECT, related_name='%(class)s_modified')
    active = models.BooleanField(default=True)

    class Meta:
        abstract = True

class Company(BaseModel):
    """Company model - equivalent to res.company in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10, unique=True)
    currency_id = models.ForeignKey('Currency', on_delete=models.PROTECT)
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=50, blank=True)
    website = models.URLField(blank=True)
    vat = models.CharField(max_length=50, blank=True, help_text="Tax ID")

    # Address fields
    street = models.CharField(max_length=255, blank=True)
    street2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state_id = models.ForeignKey('CountryState', on_delete=models.SET_NULL, null=True, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country_id = models.ForeignKey('Country', on_delete=models.PROTECT)

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.name

class Country(BaseModel):
    """Country model - equivalent to res.country in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=2, unique=True, help_text="ISO 2-letter code")
    phone_code = models.IntegerField(default=0, help_text="International dialing code")

    class Meta:
        verbose_name_plural = "Countries"

    def __str__(self):
        return self.name


class CountryGroup(BaseModel):
    """Country Group model - equivalent to res.country.group in Odoo"""
    name = models.CharField(max_length=255)
    country_ids = models.ManyToManyField(Country, blank=True)

    def __str__(self):
        return self.name


class CountryState(BaseModel):
    """State/Province model - equivalent to res.country.state in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10)
    country_id = models.ForeignKey(Country, on_delete=models.CASCADE)

    class Meta:
        unique_together = [['code', 'country_id']]

    def __str__(self):
        return f"{self.name} ({self.country_id.name})"


# Keep State as an alias for backward compatibility
State = CountryState

class Currency(BaseModel):
    """Currency model - equivalent to res.currency in Odoo"""
    name = models.CharField(max_length=3, unique=True, help_text="ISO 4217 currency code")
    symbol = models.CharField(max_length=10)
    decimal_places = models.IntegerField(default=2)
    full_name = models.CharField(max_length=100, blank=True)
    position = models.CharField(max_length=10, choices=[('before', 'Before'), ('after', 'After')], default='before')
    currency_unit_label = models.CharField(max_length=50, blank=True)
    currency_subunit_label = models.CharField(max_length=50, blank=True)
    rounding = models.DecimalField(max_digits=12, decimal_places=6, default=0.01)
    active = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "Currencies"

    def __str__(self):
        return f"{self.name} ({self.symbol})"

class Partner(BaseModel):
    """Partner model - equivalent to res.partner in Odoo"""
    PARTNER_TYPE_CHOICES = [
        ('contact', 'Contact'),
        ('invoice', 'Invoice Address'),
        ('delivery', 'Delivery Address'),
        ('other', 'Other Address'),
    ]

    name = models.CharField(max_length=255)
    display_name = models.CharField(max_length=255, blank=True)
    ref = models.CharField(max_length=50, blank=True, help_text="Internal Reference")

    # Contact information
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=50, blank=True)
    mobile = models.CharField(max_length=50, blank=True)
    website = models.URLField(blank=True)

    # Address fields
    street = models.CharField(max_length=255, blank=True)
    street2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state_id = models.ForeignKey(CountryState, on_delete=models.SET_NULL, null=True, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country_id = models.ForeignKey(Country, on_delete=models.PROTECT, null=True, blank=True)

    # Business fields
    is_company = models.BooleanField(default=False)
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    # Note: child_ids is handled by the reverse relation of parent_id

    # Customer/Vendor flags
    customer_rank = models.IntegerField(default=0)
    supplier_rank = models.IntegerField(default=0)

    # Financial fields
    credit_limit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Tax information
    vat = models.CharField(max_length=50, blank=True, help_text="Tax ID")

    # Type and category
    partner_type = models.CharField(max_length=20, choices=PARTNER_TYPE_CHOICES, default='contact')
    category_id = models.ManyToManyField('PartnerCategory', blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['email']),
            models.Index(fields=['customer_rank']),
            models.Index(fields=['supplier_rank']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_customer(self):
        return self.customer_rank > 0

    @property
    def is_supplier(self):
        return self.supplier_rank > 0

class PartnerCategory(BaseModel):
    """Partner Category model - equivalent to res.partner.category in Odoo"""
    name = models.CharField(max_length=255)
    color = models.IntegerField(default=0)
    parent_id = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Partner Categories"

    def __str__(self):
        return self.name
