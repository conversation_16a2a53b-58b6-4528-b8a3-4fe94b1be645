# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sql_export
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-06-06 02:51+0000\n"
"PO-Revision-Date: 2024-08-05 09:58+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: sql_export
#: model:ir.model,name:sql_export.model_sql_file_wizard
msgid "Allow the user to save the file with sql request's data"
msgstr "Consente all'utente di salvare il file con i dati della richiesta SQL"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__group_ids
msgid "Allowed Groups"
msgstr "Gruppi consentiti"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__user_ids
msgid "Allowed Users"
msgstr "Utenti consentiti"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_attachment_count
msgid "Attachment Count"
msgstr "Conteggio allegati"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__file_format__csv
msgid "CSV"
msgstr "CSV"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_file_wizard_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_export_view_form
msgid "Configure Properties"
msgstr "Configura proprietà"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__copy_options
msgid "Copy Options"
msgstr "Copia opzioni"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__create_uid
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__create_date
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__create_date
msgid "Created on"
msgstr "Creato il"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_file_wizard_view_form
msgid "Csv File"
msgstr "File CSV"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__display_name
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__encoding
msgid "Encoding"
msgstr "Codifica"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_export_view_form
#: model_terms:ir.ui.view,arch_db:sql_export.sql_export_view_tree
msgid "Execute Query"
msgstr "Esegui query"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_file_wizard_view_form
msgid "Export"
msgstr "Esporta"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_file_wizard_view_form
msgid "Export file"
msgstr "Esporta file"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__binary_file
msgid "File"
msgstr "File"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__file_format
msgid "File Format"
msgstr "Formato file"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__file_name
msgid "File Name"
msgstr "Nome file"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__has_group_changed
msgid "Has Group Changed"
msgstr "Ha il gruppo modificato"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__has_message
msgid "Has Message"
msgstr "Ha un messaggio"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__id
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__id
msgid "ID"
msgstr "ID"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi hanno un errore di consegna."

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_export_view_form
msgid ""
"In case of use of properties in the query, use this syntax : %(Property "
"String)s. <br/>\n"
"                                Example : SELECT id FROM sale_order WHERE "
"create_date &gt; %(Start Date)s"
msgstr ""
"Nel caso di utilizzo di roprietà nella query, utilizzare questa sintassi : %("
"Property String)s. <br/>\n"
"                                Esempio : SELECT id FROM sale_order WHERE "
"create_date &gt; %(Start Date)s"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_is_follower
msgid "Is Follower"
msgstr "Segue"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__write_uid
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__write_date
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__name
msgid "Name"
msgstr "Nome"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__note
msgid "Note"
msgstr "Nota"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: sql_export
#. odoo-python
#: code:addons/sql_export/wizard/wizard_file.py:0
#, python-format
msgid "Please enter a values for the following properties : %s"
msgstr "Inserire un valore per le segenti proprietà: %s"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__query_properties
msgid "Properties"
msgstr "Proprietà"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__query
msgid "Query"
msgstr "Query"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__query_properties_definition
msgid "Query Properties"
msgstr "Proprietà query"

#. module: sql_export
#: model:ir.actions.act_window,name:sql_export.sql_export_tree_action
msgid "SQL Exports"
msgstr "Esportazioni SQL"

#. module: sql_export
#: model:ir.model,name:sql_export.model_sql_export
msgid "SQL export"
msgstr "Esporta SQL"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_file_wizard__sql_export_id
#: model:ir.ui.menu,name:sql_export.sql_export_menu_view
msgid "Sql Export"
msgstr "Esporta SQL"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__state
msgid "State"
msgstr "Stato"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__state
msgid ""
"State of the Request:\n"
" * 'Draft': Not tested\n"
" * 'SQL Valid': SQL Request has been checked and is valid"
msgstr ""
"Stato della richiesta:\n"
" * 'Bozza': non restata\n"
" * 'SQL valido': la richiesta SQL è stata testata ed è valida"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__has_group_changed
msgid ""
"Technical fields, used in modules that depends on this one to know if groups "
"has changed, and that according access should be updated."
msgstr ""
"Campo tecnico utilizzato nei moduli che dipendono da questo per sapere se i "
"gruppi sono cambiati e che le atorizzazoni di accesso devono essere "
"aggiornate."

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__use_properties
msgid "Use Properties"
msgstr "Usa proprietà"

#. module: sql_export
#: model:ir.model.fields,field_description:sql_export.field_sql_export__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: sql_export
#: model:ir.model.fields,help:sql_export.field_sql_export__query
msgid ""
"You can't use the following words: DELETE, DROP, CREATE, INSERT, ALTER, "
"TRUNCATE, EXECUTE, UPDATE."
msgstr ""
"Non si possono usare le segenti parole: DELETE, DROP, CREATE, INSERT, ALTER, "
"TRUNCATE, EXECUTE, UPDATE."

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__big5
msgid "big5"
msgstr "big5"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__gb18030
msgid "gb18030"
msgstr "gb18030"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__koir8_r
msgid "koir8_r"
msgstr "koir8_r"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__latin1
msgid "latin1"
msgstr "latin1"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__latin2
msgid "latin2"
msgstr "latin2"

#. module: sql_export
#: model_terms:ir.ui.view,arch_db:sql_export.sql_file_wizard_view_form
msgid "or"
msgstr "o"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__shift_jis
msgid "shift_jis"
msgstr "shift_jis"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__utf-16
msgid "utf-16"
msgstr "utf-16"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__utf-8
msgid "utf-8"
msgstr "utf-8"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__windows-1251
msgid "windows-1251"
msgstr "windows-1251"

#. module: sql_export
#: model:ir.model.fields.selection,name:sql_export.selection__sql_export__encoding__windows-1252
msgid "windows-1252"
msgstr "windows-1251"

#~ msgid "Last Modified on"
#~ msgstr "Ultima modifica il"

#~ msgid "Set to Draft"
#~ msgstr "Imposta a Bozza"

#~ msgid "Draft"
#~ msgstr "Bozza"
