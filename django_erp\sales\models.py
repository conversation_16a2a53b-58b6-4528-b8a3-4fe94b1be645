from django.db import models
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
from core.models import BaseModel, Company, Partner, Currency
from accounting.models import AccountJournal, AccountTax

# Supporting Models for Sales Module (defined first to avoid forward references)

class SalesTeam(BaseModel):
    """Sales Team model - equivalent to crm.team in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10, blank=True)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Team Leader")
    member_ids = models.ManyToManyField('auth.User', related_name='sales_team_members', blank=True)

    def __str__(self):
        return self.name

class ProductPricelist(BaseModel):
    """Product Pricelist model - equivalent to product.pricelist in Odoo"""
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    sequence = models.IntegerField(default=16)

    def __str__(self):
        return self.name

class ProductUomCategory(BaseModel):
    """Unit of Measure Category model - equivalent to uom.category in Odoo"""
    name = models.CharField(max_length=255)

    def __str__(self):
        return self.name

class ProductUom(BaseModel):
    """Unit of Measure model - equivalent to uom.uom in Odoo"""
    name = models.CharField(max_length=255)
    category_id = models.ForeignKey(ProductUomCategory, on_delete=models.PROTECT)
    factor = models.DecimalField(max_digits=16, decimal_places=6, default=1.0)
    factor_inv = models.DecimalField(max_digits=16, decimal_places=6, default=1.0)
    uom_type = models.CharField(max_length=20, choices=[
        ('bigger', 'Bigger than the reference Unit of Measure'),
        ('reference', 'Reference Unit of Measure for this category'),
        ('smaller', 'Smaller than the reference Unit of Measure'),
    ], default='reference')
    rounding = models.DecimalField(max_digits=16, decimal_places=6, default=0.01)
    active = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class DeliveryCarrier(BaseModel):
    """Delivery Carrier model - equivalent to delivery.carrier in Odoo"""
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    delivery_type = models.CharField(max_length=50, choices=[
        ('fixed', 'Fixed Price'),
        ('base_on_rule', 'Based on Rules'),
    ], default='fixed')
    fixed_price = models.DecimalField(max_digits=16, decimal_places=2, default=0.0)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

class StockWarehouse(BaseModel):
    """Warehouse model - equivalent to stock.warehouse in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, help_text="Address")

    class Meta:
        unique_together = [['code', 'company_id']]

    def __str__(self):
        return f"{self.code} - {self.name}"

class ProcurementGroup(BaseModel):
    """Procurement Group model - equivalent to procurement.group in Odoo"""
    name = models.CharField(max_length=255)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    def __str__(self):
        return self.name

# Main Sales Models

class SaleOrder(BaseModel):
    """Sales Order model - equivalent to sale.order in Odoo"""

    STATE_CHOICES = [
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('sale', 'Sales Order'),
        ('cancel', 'Cancelled'),
    ]

    INVOICE_STATUS_CHOICES = [
        ('upselling', 'Upselling Opportunity'),
        ('invoiced', 'Fully Invoiced'),
        ('to invoice', 'To Invoice'),
        ('no', 'Nothing to Invoice'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, default='New', help_text="Order Reference")
    origin = models.CharField(max_length=255, blank=True, help_text="Source Document")
    client_order_ref = models.CharField(max_length=255, blank=True, help_text="Customer Reference")

    # Partner Information
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, help_text="Customer")
    partner_invoice_id = models.ForeignKey(Partner, on_delete=models.PROTECT,
                                         related_name='sale_invoice_orders',
                                         help_text="Invoice Address")
    partner_shipping_id = models.ForeignKey(Partner, on_delete=models.PROTECT,
                                          related_name='sale_shipping_orders',
                                          help_text="Delivery Address")

    # Dates
    date_order = models.DateTimeField(default=timezone.now, help_text="Order Date")
    validity_date = models.DateField(blank=True, null=True, help_text="Expiration Date")
    commitment_date = models.DateTimeField(blank=True, null=True, help_text="Delivery Date")

    # State and Control
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft', db_index=True)
    locked = models.BooleanField(default=False, help_text="Locked orders cannot be modified")

    # Online Signature and Payment
    require_signature = models.BooleanField(default=False, help_text="Request online signature from customer")
    require_payment = models.BooleanField(default=False, help_text="Request online payment from customer")
    prepayment_percent = models.FloatField(default=0.0, help_text="Prepayment percentage required")
    signature = models.ImageField(upload_to='signatures/', null=True, blank=True, help_text="Customer Signature")
    signed_by = models.CharField(max_length=255, blank=True, help_text="Signed By")
    signed_on = models.DateTimeField(null=True, blank=True, help_text="Signature Date")

    # Additional Reference Fields
    reference = models.CharField(max_length=255, blank=True, help_text="Payment Reference")

    # Company and Currency
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Accounting Integration
    journal_id = models.ForeignKey(
        'accounting.AccountJournal',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Invoicing Journal"
    )
    fiscal_position_id = models.ForeignKey(
        'accounting.AccountFiscalPosition',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Fiscal Position for tax and account adaptation"
    )
    payment_term_id = models.ForeignKey(
        'accounting.AccountPaymentTerm',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Payment Terms"
    )

    # Amounts (computed from order lines)
    amount_untaxed = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_undiscounted = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Invoicing
    invoice_status = models.CharField(max_length=20, choices=INVOICE_STATUS_CHOICES, default='no')
    amount_to_invoice = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_invoiced = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Advanced Invoicing Features
    INVOICE_POLICY_CHOICES = [
        ('order', 'Ordered quantities'),
        ('delivery', 'Delivered quantities'),
    ]

    invoice_policy = models.CharField(max_length=20, choices=INVOICE_POLICY_CHOICES,
                                    default='order', help_text="Invoicing Policy")

    # Subscription & Recurring Billing
    is_subscription = models.BooleanField(default=False, help_text="Is Subscription Order")
    subscription_management = models.CharField(max_length=20, choices=[
        ('create', 'Create a new subscription'),
        ('renew', 'Renew existing subscription'),
        ('upsell', 'Upsell existing subscription'),
    ], blank=True, help_text="Subscription Management")

    # Recurring billing configuration
    recurring_rule_type = models.CharField(max_length=20, choices=[
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    ], blank=True, help_text="Recurring Rule")

    recurring_interval = models.IntegerField(default=1, help_text="Repeat every X periods")
    next_invoice_date = models.DateField(null=True, blank=True, help_text="Next Invoice Date")

    # Milestone billing
    milestone_billing = models.BooleanField(default=False, help_text="Milestone-based billing")

    # Approval workflow
    APPROVAL_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    approval_status = models.CharField(max_length=20, choices=APPROVAL_STATUS_CHOICES,
                                     default='draft', help_text="Approval Status")
    approved_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='approved_orders', help_text="Approved By")
    approved_date = models.DateTimeField(null=True, blank=True, help_text="Approval Date")

    # Credit management
    credit_limit_check = models.BooleanField(default=False, help_text="Check Credit Limit")
    credit_limit_exceeded = models.BooleanField(default=False, help_text="Credit Limit Exceeded")

    # Delivery
    DELIVERY_STATUS_CHOICES = [
        ('no', 'Nothing to Deliver'),
        ('to_deliver', 'To Deliver'),
        ('partial', 'Partially Delivered'),
        ('done', 'Delivered'),
    ]

    delivery_status = models.CharField(max_length=20, choices=DELIVERY_STATUS_CHOICES, default='no')

    # Sales Information
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Salesperson")
    team_id = models.ForeignKey(SalesTeam, on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Sales Team")

    # Terms and Conditions
    note = models.TextField(blank=True, help_text="Terms and conditions")

    # Delivery
    carrier_id = models.ForeignKey(DeliveryCarrier, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Delivery Method")

    # Fiscal and Payment
    fiscal_position_id = models.ForeignKey('accounting.AccountFiscalPosition', on_delete=models.SET_NULL,
                                         null=True, blank=True, help_text="Fiscal Position")
    payment_term_id = models.ForeignKey('accounting.AccountPaymentTerm', on_delete=models.SET_NULL,
                                       null=True, blank=True, help_text="Payment Terms")

    # Pricelist
    pricelist_id = models.ForeignKey(ProductPricelist, on_delete=models.PROTECT,
                                    help_text="Pricelist")

    # Procurement and Warehouse
    warehouse_id = models.ForeignKey(StockWarehouse, on_delete=models.SET_NULL, null=True, blank=True)
    procurement_group_id = models.ForeignKey(ProcurementGroup, on_delete=models.SET_NULL,
                                           null=True, blank=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(
                    models.Q(state='sale', date_order__isnull=False) |
                    ~models.Q(state='sale')
                ),
                name='date_order_conditional_required'
            )
        ]
        indexes = [
            models.Index(fields=['date_order']),
            models.Index(fields=['state']),
            models.Index(fields=['partner_id']),
            models.Index(fields=['user_id']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()

        # Confirmed sales order requires a confirmation date
        if self.state == 'sale' and not self.date_order:
            raise ValidationError("A confirmed sales order requires a confirmation date.")

        # Validate partner addresses
        if self.partner_id:
            if not self.partner_invoice_id:
                self.partner_invoice_id = self.partner_id
            if not self.partner_shipping_id:
                self.partner_shipping_id = self.partner_id

    def action_confirm(self):
        """Confirm the sales order - simplified version following Odoo pattern"""
        if self.state not in ['draft', 'sent']:
            raise ValidationError("Only draft or sent orders can be confirmed.")

        # Pre-confirmation validations
        self._validate_before_confirmation()

        # Check credit limit if enabled
        self._check_credit_limit()

        # Update state and date
        self.state = 'sale'
        if not self.date_order:
            self.date_order = timezone.now()

        # Generate sequence number if needed
        if self.name == 'New':
            self.name = self._get_sequence_number()

        self.save()

        # Call the internal confirmation method (can be extended by other modules)
        self._action_confirm()

        return True

    def _action_confirm(self):
        """Internal confirmation method - can be extended by other modules"""
        # Create procurement group
        self._create_procurement_group()

        # Update statuses
        self._compute_invoice_status()
        self._compute_delivery_status()

    def _validate_before_confirmation(self):
        """Validate order before confirmation"""
        # Check if order has lines
        if not self.order_line.exists():
            raise ValidationError("Cannot confirm order without order lines.")

        # Check if order has at least one non-display line (actual product/service line)
        # In Odoo, display lines have display_type set to 'line_section' or 'line_note'
        # Regular product lines have display_type=None or empty
        product_lines = self.order_line.filter(
            Q(display_type__isnull=True) | Q(display_type='')
        )
        if not product_lines.exists():
            raise ValidationError("Cannot confirm order without product lines.")

        # Check if all non-display lines have products or names
        lines_without_product_or_name = product_lines.filter(
            product_id__isnull=True,
            name__isnull=True
        )
        if lines_without_product_or_name.exists():
            raise ValidationError("All order lines must have a product or description.")

        # Check product availability
        self._check_product_availability()

    def _check_product_availability(self):
        """Check if products are available in stock"""
        # This would check stock levels for stockable products
        # For now, simplified implementation
        pass



    def _check_product_availability(self):
        """Check if products are available in stock"""
        # This would check stock levels for stockable products
        # For now, simplified implementation
        pass

    def _create_delivery_orders(self):
        """Create delivery orders for stockable products"""
        from inventory.models import StockPicking, StockMove

        # Get stockable product lines (simplified since product_id is not yet implemented)
        # For now, assume all non-display lines are stockable
        stockable_lines = self.order_line.filter(
            display_type__isnull=True
        )

        if not stockable_lines.exists():
            return

        # Create delivery picking
        picking_vals = {
            'partner_id': self.partner_shipping_id or self.partner_id,
            'origin': self.name,
            'move_type': 'direct',
            'company_id': self.company_id,
            'picking_type_id': self._get_delivery_picking_type(),
            'location_id': self._get_source_location(),
            'location_dest_id': self._get_customer_location(),
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

        try:
            picking = StockPicking.objects.create(**picking_vals)

            # Create stock moves for each line (simplified since product_id is not yet implemented)
            for line in stockable_lines:
                move_vals = {
                    'name': f"{self.name}: {line.name}",
                    # 'product_id': line.product_id,  # Will be added when product model is implemented
                    'product_uom_qty': line.product_uom_qty,
                    'product_uom': line.product_uom,
                    'picking_id': picking,
                    'location_id': picking.location_id,
                    'location_dest_id': picking.location_dest_id,
                    'sale_line_id': line,
                    'company_id': self.company_id,
                    'create_uid': self.create_uid,
                    'write_uid': self.write_uid,
                }

                try:
                    StockMove.objects.create(**move_vals)
                except Exception as e:
                    # Log error but don't fail the confirmation
                    print(f"Warning: Could not create stock move: {e}")

            # Confirm the picking
            picking.action_confirm()

        except Exception as e:
            # Log error but don't fail the confirmation
            print(f"Warning: Could not create delivery order: {e}")

    def _get_delivery_picking_type(self):
        """Get the delivery picking type"""
        from inventory.models import StockPickingType
        try:
            return StockPickingType.objects.filter(
                code='outgoing',
                company_id=self.company_id
            ).first()
        except:
            return None

    def _get_source_location(self):
        """Get source location for delivery"""
        from inventory.models import StockLocation
        try:
            return StockLocation.objects.filter(
                usage='internal',
                company_id=self.company_id
            ).first()
        except:
            return None

    def _get_customer_location(self):
        """Get customer location"""
        from inventory.models import StockLocation
        try:
            return StockLocation.objects.filter(
                usage='customer'
            ).first()
        except:
            return None

    def _compute_delivery_status(self):
        """Compute delivery status based on pickings and order lines"""
        try:
            from inventory.models import StockPicking
        except ImportError:
            # Inventory module not available
            self.delivery_status = 'no'
            return

        # Get stockable product lines
        stockable_lines = self.order_line.filter(
            product_id__isnull=False,
            product_id__product_tmpl_id__detailed_type='product',  # Storable products
            display_type__isnull=True
        )

        if not stockable_lines.exists():
            self.delivery_status = 'no'
            return

        # Get related pickings
        try:
            pickings = StockPicking.objects.filter(origin=self.name)

            if not pickings.exists():
                self.delivery_status = 'to_deliver'
                return

            # Check delivery status based on pickings
            all_done = all(picking.state == 'done' for picking in pickings)
            any_done = any(picking.state == 'done' for picking in pickings)

            if all_done:
                self.delivery_status = 'done'
            elif any_done:
                self.delivery_status = 'partial'
            else:
                self.delivery_status = 'to_deliver'

        except Exception:
            # If inventory module not available, use simplified logic
            self.delivery_status = 'to_deliver'

    def _trigger_post_confirmation_workflows(self):
        """Trigger workflows after confirmation"""
        # Send confirmation email
        self._send_confirmation_email()

        # Update partner statistics
        self._update_partner_statistics()

        # Trigger automatic invoicing if configured
        if self._should_auto_invoice():
            self._create_automatic_invoice()

    def _send_confirmation_email(self):
        """Send order confirmation email to customer"""
        # This would send email notification
        # For now, simplified implementation
        pass

    def _update_partner_statistics(self):
        """Update partner sales statistics"""
        # This would update partner sales history
        # For now, simplified implementation
        pass

    def _should_auto_invoice(self):
        """Check if order should be automatically invoiced"""
        # This would check invoicing policy
        # For now, return False
        return False

    def _create_automatic_invoice(self):
        """Create automatic invoice if configured"""
        try:
            self._create_invoices()
        except Exception as e:
            print(f"Warning: Could not create automatic invoice: {e}")

    def action_cancel(self):
        """Cancel the sales order"""
        if self.state == 'cancel':
            return

        self.state = 'cancel'
        self.save()

    def action_draft(self):
        """Reset to draft state"""
        if self.state not in ['cancel', 'sent']:
            raise ValidationError("Cannot reset to draft from current state.")

        self.state = 'draft'
        self.save()

    def _check_credit_limit(self):
        """Check customer credit limit"""
        if not self.partner_id.credit_limit:
            return

        # Get total due amount for customer
        total_due = self._get_partner_due_amount()

        if total_due + self.amount_total > self.partner_id.credit_limit:
            raise ValidationError(
                f"Credit limit exceeded for customer {self.partner_id.name}. "
                f"Credit limit: {self.partner_id.credit_limit}, "
                f"Current due: {total_due}, "
                f"Order amount: {self.amount_total}"
            )

    def _get_partner_due_amount(self):
        """Get total due amount for the partner"""
        # This would typically query account.move.line for receivable amounts
        # For now, return 0 - will be implemented when accounting integration is complete
        return Decimal('0.0')

    def _get_sequence_number(self):
        """Generate sequence number for the order"""
        # Simple sequence generation - in production, this would be more sophisticated
        last_order = SaleOrder.objects.filter(
            company_id=self.company_id,
            date_order__year=self.date_order.year
        ).exclude(name='New').order_by('-name').first()

        if last_order and last_order.name:
            try:
                last_num = int(last_order.name.split('/')[-1])
                return f"SO/{self.date_order.year}/{last_num + 1:04d}"
            except (ValueError, IndexError):
                pass

        return f"SO/{self.date_order.year}/0001"

    def _create_procurement_group(self):
        """Create procurement group for the order"""
        if not self.procurement_group_id:
            # This would create a procurement group
            # For now, we'll skip this as it requires inventory module
            pass

    def _compute_amounts(self):
        """Compute total amounts from order lines"""
        lines = self.order_line.all()

        self.amount_untaxed = sum(line.price_subtotal for line in lines if not line.display_type)
        self.amount_tax = sum(line.price_tax for line in lines if not line.display_type)
        self.amount_total = self.amount_untaxed + self.amount_tax

        # Compute undiscounted amount
        undiscounted = Decimal('0.0')
        for line in lines:
            if not line.display_type and line.discount != 100:
                if line.discount > 0:
                    undiscounted += (line.price_subtotal * 100) / (100 - line.discount)
                else:
                    undiscounted += line.price_unit * line.product_uom_qty
        self.amount_undiscounted = undiscounted

    def _compute_invoice_status(self):
        """Compute invoice status based on order lines"""
        lines = self.order_line.filter(display_type__isnull=True)

        if not lines:
            self.invoice_status = 'no'
            return

        # Check if all lines are invoiced
        all_invoiced = all(line.qty_invoiced >= line.product_uom_qty for line in lines)

        if all_invoiced:
            self.invoice_status = 'invoiced'
        elif any(line.qty_to_invoice > 0 for line in lines):
            self.invoice_status = 'to invoice'
        else:
            self.invoice_status = 'no'

    def action_cancel(self):
        """Cancel the sale order"""
        if self.state == 'done':
            raise ValidationError("Cannot cancel a locked sale order.")

        # Check for invoiced lines
        if self.order_line.filter(qty_invoiced__gt=0).exists():
            raise ValidationError("Cannot cancel order with invoiced lines.")

        # Check for delivered lines
        if self.order_line.filter(qty_delivered__gt=0).exists():
            raise ValidationError("Cannot cancel order with delivered lines.")

        self.state = 'cancel'
        self.save()

    def action_unlock(self):
        """Unlock a locked sale order"""
        if self.state == 'done':
            self.state = 'sale'
            self.save()

    def action_done(self):
        """Lock the sale order"""
        if self.state != 'sale':
            raise ValidationError("Only confirmed orders can be locked.")

        self.state = 'done'
        self.save()

    def _create_invoices(self, grouped=False, final=False, specific_lines=None):
        """Create invoices from sale order"""
        from accounting.models import AccountMove, AccountMoveLine, AccountJournal, AccountAccount

        if self.state not in ['sale', 'done']:
            raise ValidationError("Only confirmed orders can be invoiced.")

        # Get lines to invoice
        if specific_lines is not None:
            lines_to_invoice = specific_lines
        else:
            lines_to_invoice = self.order_line.filter(qty_to_invoice__gt=0)

        if not lines_to_invoice:
            raise ValidationError("No lines to invoice.")

        # Get sales journal
        try:
            sales_journal = AccountJournal.objects.get(
                type='sale',
                company_id=self.company_id
            )
        except AccountJournal.DoesNotExist:
            raise ValidationError("No sales journal found for this company.")

        # Prepare invoice values
        invoice_vals = self._prepare_invoice()
        invoice_vals.update({
            'journal_id': sales_journal,
            'move_type': 'out_invoice',
            'state': 'draft',
        })

        # Create invoice
        invoice = AccountMove.objects.create(**invoice_vals)

        # Create invoice lines
        for line in lines_to_invoice:
            if line.display_type:
                # Section/note lines
                invoice_line_vals = {
                    'move_id': invoice,
                    'display_type': line.display_type,
                    'name': line.name,
                    'sequence': line.sequence,
                    'create_uid': line.create_uid,
                    'write_uid': line.write_uid,
                }
            else:
                # Product lines
                # Get income account (simplified - should be from product/category)
                try:
                    income_account = AccountAccount.objects.filter(
                        account_type='income',
                        company_id=self.company_id
                    ).first()
                    if not income_account:
                        raise AccountAccount.DoesNotExist()
                except AccountAccount.DoesNotExist:
                    raise ValidationError("No income account found.")

                invoice_line_vals = {
                    'move_id': invoice,
                    'account_id': income_account,
                    'name': line.name,
                    'quantity': line.qty_to_invoice,
                    'price_unit': line.price_unit,
                    'discount': line.discount,
                    'debit': 0,
                    'credit': line.qty_to_invoice * line.price_unit * (1 - line.discount / 100),
                    'create_uid': line.create_uid,
                    'write_uid': line.write_uid,
                }

                # Update sale line quantities
                line.qty_invoiced += line.qty_to_invoice
                line.save()

            AccountMoveLine.objects.create(**invoice_line_vals)

        # Create receivable line (customer balance)
        try:
            receivable_account = AccountAccount.objects.filter(
                account_type='asset_receivable',
                company_id=self.company_id
            ).first()
            if not receivable_account:
                raise AccountAccount.DoesNotExist()
        except AccountAccount.DoesNotExist:
            raise ValidationError("No receivable account found.")

        total_amount = sum(line.qty_to_invoice * line.price_unit * (1 - line.discount / 100)
                          for line in lines_to_invoice if not line.display_type)

        AccountMoveLine.objects.create(
            move_id=invoice,
            account_id=receivable_account,
            partner_id=self.partner_id,
            name=f"Invoice {invoice.name}",
            debit=total_amount,
            credit=0,
            create_uid=self.create_uid,
            write_uid=self.write_uid,
        )

        # Update order invoice status
        self._compute_invoice_status()
        self.save()

        return [invoice]

    def _prepare_invoice(self):
        """Prepare invoice values from sale order"""
        return {
            'move_type': 'out_invoice',
            'partner_id': self.partner_id,
            'partner_shipping_id': self.partner_shipping_id if self.partner_shipping_id else self.partner_id,
            'currency_id': self.currency_id,
            'payment_reference': self.client_order_ref or '',
            'invoice_origin': self.name,
            'invoice_payment_term_id': self.payment_term_id,
            'fiscal_position_id': self.fiscal_position_id,
            'company_id': self.company_id,
            'user_id': self.user_id,
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

    def unlink(self):
        """Override unlink to add business rules"""
        for order in self:
            if order.state not in ('draft', 'cancel'):
                raise ValidationError("Cannot delete confirmed orders.")
            if order.order_line.filter(qty_invoiced__gt=0).exists():
                raise ValidationError("Cannot delete orders with invoiced lines.")
        return super().unlink()

    # ===== ADVANCED INVOICING METHODS =====

    def _create_recurring_invoices(self):
        """Create recurring invoices for subscription orders"""
        if not self.is_subscription:
            raise ValidationError("Only subscription orders can create recurring invoices.")

        if not self.next_invoice_date:
            raise ValidationError("Next invoice date must be set for recurring invoices.")

        from datetime import date
        if self.next_invoice_date > date.today():
            return []  # Not time to invoice yet

        # Create invoice using existing method
        invoices = self._create_invoices()

        # Calculate next invoice date
        self._calculate_next_invoice_date()

        return invoices

    def _calculate_next_invoice_date(self):
        """Calculate next invoice date based on recurring rule"""
        if not self.next_invoice_date:
            return

        from dateutil.relativedelta import relativedelta

        if self.recurring_rule_type == 'daily':
            self.next_invoice_date += timedelta(days=self.recurring_interval)
        elif self.recurring_rule_type == 'weekly':
            self.next_invoice_date += timedelta(weeks=self.recurring_interval)
        elif self.recurring_rule_type == 'monthly':
            self.next_invoice_date += relativedelta(months=self.recurring_interval)
        elif self.recurring_rule_type == 'yearly':
            self.next_invoice_date += relativedelta(years=self.recurring_interval)

        self.save()

    def _create_milestone_invoices(self, milestone_lines=None):
        """Create milestone-based invoices"""
        if not self.milestone_billing:
            raise ValidationError("Order is not configured for milestone billing.")

        if not milestone_lines:
            # Get lines marked for milestone invoicing
            milestone_lines = self.order_line.filter(
                is_milestone=True,
                milestone_reached=True,
                qty_invoiced=0
            )

        if not milestone_lines:
            raise ValidationError("No milestone lines ready for invoicing.")

        # Temporarily set qty_to_invoice for milestone lines
        for line in milestone_lines:
            line.qty_to_invoice = line.product_uom_qty
            line.save()

        # Create invoice with specific milestone lines
        invoices = self._create_invoices(specific_lines=milestone_lines)

        return invoices

    def action_request_approval(self):
        """Request approval for the order"""
        if self.approval_status != 'draft':
            raise ValidationError("Only draft orders can request approval.")

        self.approval_status = 'pending'
        self.save()

        # TODO: Send notification to approvers
        # self._send_approval_notification()

    def action_approve(self, user):
        """Approve the order"""
        if self.approval_status != 'pending':
            raise ValidationError("Only pending orders can be approved.")

        self.approval_status = 'approved'
        self.approved_by = user
        self.approved_date = timezone.now()
        self.save()

    def action_reject(self, user, reason=None):
        """Reject the order"""
        if self.approval_status != 'pending':
            raise ValidationError("Only pending orders can be rejected.")

        self.approval_status = 'rejected'
        self.approved_by = user
        self.approved_date = timezone.now()
        self.save()

        # TODO: Add rejection reason field and save it

    def _check_credit_limit(self):
        """Check customer credit limit"""
        if not self.credit_limit_check:
            return True

        # Get customer's credit limit (simplified)
        customer_credit_limit = getattr(self.partner_id, 'credit_limit', 0)
        if customer_credit_limit <= 0:
            return True  # No limit set

        # Calculate current outstanding amount
        from accounting.models import AccountMove
        outstanding_amount = AccountMove.objects.filter(
            partner_id=self.partner_id,
            move_type='out_invoice',
            payment_state__in=['not_paid', 'partial']
        ).aggregate(
            total=models.Sum('amount_residual')
        )['total'] or 0

        # Check if new order would exceed limit
        total_exposure = outstanding_amount + self.amount_total

        if total_exposure > customer_credit_limit:
            self.credit_limit_exceeded = True
            self.save()
            if self.credit_limit_check:
                raise ValidationError(
                    f"Credit limit exceeded for customer {self.partner_id.name}. "
                    f"Credit limit: {customer_credit_limit}, Total exposure: {total_exposure}"
                )
            return False

        self.credit_limit_exceeded = False
        self.save()
        return True

    def _post_invoice_creation_hook(self, invoices):
        """Extension point for additional invoice processing"""
        # This method can be overridden by extension modules
        # For example, subscription modules can add recurring logic here
        pass

    def action_invoice_sent(self):
        """Mark order as invoice sent"""
        # Update invoice status and send email
        # This integrates with email functionality
        pass

class SaleOrderLine(BaseModel):
    """Sales Order Line model - equivalent to sale.order.line in Odoo"""

    DISPLAY_TYPE_CHOICES = [
        ('line_section', 'Section'),
        ('line_note', 'Note'),
    ]

    # Parent order
    order_id = models.ForeignKey(SaleOrder, on_delete=models.CASCADE, related_name='order_line')

    # Product and description
    product_id = models.ForeignKey('inventory.Product', on_delete=models.PROTECT, null=True, blank=True)
    product_template_id = models.ForeignKey(
        'inventory.ProductTemplate',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Product Template"
    )
    name = models.TextField(help_text="Description")

    # Line Type Flags
    is_downpayment = models.BooleanField(default=False, help_text="Is a down payment line")
    is_expense = models.BooleanField(default=False, help_text="Is an expense line")

    # Quantities
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1.0,
                                        help_text="Ordered Quantity")
    qty_delivered = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                      help_text="Delivered Quantity")
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Invoiced Quantity")
    qty_to_invoice = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                       help_text="To Invoice Quantity")

    # Unit of Measure
    product_uom = models.ForeignKey(ProductUom, on_delete=models.PROTECT, null=True, blank=True,
                                   help_text="Unit of Measure")

    # Pricing
    price_unit = models.DecimalField(max_digits=20, decimal_places=4, default=0.0, help_text="Unit Price")
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0.0, help_text="Discount (%)")

    # Computed amounts
    price_subtotal = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                       help_text="Subtotal")
    price_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                  help_text="Tax Amount")
    price_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                    help_text="Total")
    price_reduce = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                     help_text="Unit Price with Discount")
    price_reduce_taxexcl = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                             help_text="Unit Price with Discount (Tax Excluded)")

    # Taxes
    tax_id = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes")

    # Delivery and Packaging
    customer_lead = models.IntegerField(default=0, help_text="Lead Time (days)")
    product_packaging_id = models.ForeignKey(
        'inventory.ProductPackaging',
        on_delete=models.PROTECT,
        null=True, blank=True,
        help_text="Product Packaging"
    )
    product_packaging_qty = models.FloatField(default=0.0, help_text="Packaging Quantity")

    # Delivery Quantity Method
    QTY_DELIVERED_METHOD_CHOICES = [
        ('manual', 'Manual'),
        ('analytic', 'Analytic From Expenses'),
    ]
    qty_delivered_method = models.CharField(
        max_length=20,
        choices=QTY_DELIVERED_METHOD_CHOICES,
        default='manual',
        help_text="Method to update delivered quantity"
    )

    # Display and ordering
    sequence = models.IntegerField(default=10, help_text="Sequence")
    display_type = models.CharField(max_length=20, choices=DISPLAY_TYPE_CHOICES, null=True, blank=True,
                                   help_text="Technical field for UX purpose")

    # Milestone Billing
    is_milestone = models.BooleanField(default=False, help_text="Is Milestone Line")
    milestone_name = models.CharField(max_length=255, blank=True, help_text="Milestone Name")
    milestone_date = models.DateField(null=True, blank=True, help_text="Milestone Due Date")
    milestone_reached = models.BooleanField(default=False, help_text="Milestone Reached")
    milestone_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                             help_text="Milestone Percentage")

    # Time & Material Billing
    is_service = models.BooleanField(default=False, help_text="Is Service Line")
    timesheet_ids = models.ManyToManyField('project.AccountAnalyticLine', blank=True,
                                         help_text="Related Timesheets")

    # Subscription Line
    is_subscription_line = models.BooleanField(default=False, help_text="Is Subscription Line")
    subscription_start_date = models.DateField(null=True, blank=True, help_text="Subscription Start")
    subscription_end_date = models.DateField(null=True, blank=True, help_text="Subscription End")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # State tracking - computed from order
    # state will be accessed via order_id.state

    # Invoice lines relationship (will be added when accounting integration is complete)
    # invoice_lines = models.ManyToManyField('account.MoveLine', blank=True)

    class Meta:
        constraints = [
            # Equivalent to Odoo's accountable_required_fields constraint
            models.CheckConstraint(
                check=models.Q(display_type__isnull=False) |
                      (models.Q(product_id__isnull=False) & models.Q(product_uom__isnull=False)),
                name='accountable_required_fields'
            ),
            # Equivalent to Odoo's non_accountable_null_fields constraint
            models.CheckConstraint(
                check=models.Q(display_type__isnull=True) |
                      (models.Q(product_id__isnull=True) &
                       models.Q(price_unit=0) &
                       models.Q(product_uom_qty=0) &
                       models.Q(product_uom__isnull=True) &
                       models.Q(customer_lead=0)),
                name='non_accountable_null_fields'
            ),
        ]
        indexes = [
            models.Index(fields=['order_id']),
            models.Index(fields=['sequence']),
        ]

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"

    def save(self, *args, **kwargs):
        # Set company and currency from order
        if self.order_id:
            self.company_id = self.order_id.company_id
            self.currency_id = self.order_id.currency_id

        # Compute amounts
        self._compute_amounts()

        super().save(*args, **kwargs)

        # Update order totals
        if self.order_id:
            self.order_id._compute_amounts()
            self.order_id.save()

    def _prepare_invoice_line(self):
        """Prepare invoice line values from sale order line"""
        if self.display_type:
            return {
                'display_type': self.display_type,
                'name': self.name,
                'sequence': self.sequence,
            }

        return {
            'name': self.name,
            'quantity': self.qty_to_invoice,
            'price_unit': self.price_unit,
            'discount': self.discount,
            'product_id': getattr(self, 'product_id', None),
            'product_uom_id': self.product_uom,
            'tax_ids': [(6, 0, self.tax_id.values_list('id', flat=True))],
            'analytic_account_id': getattr(self, 'analytic_account_id', None),
            'sale_line_ids': [(4, self.id)],
        }

    def _get_delivered_qty(self):
        """Get delivered quantity for this line"""
        # Simplified - in real implementation would check stock moves
        return self.qty_delivered

    def _compute_qty_to_invoice(self):
        """Compute quantity to invoice"""
        if self.display_type:
            self.qty_to_invoice = 0
            return

        # Based on invoice policy
        if hasattr(self, 'product_id') and self.product_id:
            if getattr(self.product_id, 'invoice_policy', 'order') == 'delivery':
                # Invoice based on delivered quantity
                self.qty_to_invoice = max(0, self.qty_delivered - self.qty_invoiced)
            else:
                # Invoice based on ordered quantity
                self.qty_to_invoice = max(0, self.product_uom_qty - self.qty_invoiced)
        else:
            self.qty_to_invoice = max(0, self.product_uom_qty - self.qty_invoiced)

    def _check_line_unlink(self):
        """Check if line can be deleted"""
        if self.order_id.state in ['sale', 'done']:
            if self.qty_invoiced > 0:
                raise ValidationError("Cannot delete invoiced order lines.")
            if self.qty_delivered > 0:
                raise ValidationError("Cannot delete delivered order lines.")

    def unlink(self):
        """Override unlink to add business rules"""
        for line in self:
            line._check_line_unlink()
        return super().unlink()

    def clean(self):
        super().clean()

        # Validate required fields for accountable lines
        if not self.display_type:
            if not self.product_uom:
                raise ValidationError("Unit of Measure is required for accountable lines.")

        # Validate forbidden values for non-accountable lines
        if self.display_type:
            if (self.price_unit != 0 or self.product_uom_qty != 0 or
                self.product_uom or self.customer_lead != 0):
                raise ValidationError("Non-accountable lines cannot have product-related values.")

    def _compute_amounts(self):
        """Compute line amounts including taxes"""
        if self.display_type:
            # Section and note lines have no amounts
            self.price_subtotal = 0.0
            self.price_tax = 0.0
            self.price_total = 0.0
            self.price_reduce = 0.0
            self.price_reduce_taxexcl = 0.0
            return

        # Calculate base amount with discount
        base_amount = Decimal(str(self.price_unit)) * Decimal(str(self.product_uom_qty))
        discount_amount = base_amount * (Decimal(str(self.discount)) / Decimal('100'))
        subtotal = base_amount - discount_amount

        # Calculate taxes (simplified - in real implementation would use tax engine)
        tax_amount = Decimal('0.0')
        if self.tax_id.exists():
            # Simple tax calculation - sum all tax rates
            taxes = self.tax_id.all()
            total_tax_rate = sum(Decimal(str(tax.amount)) for tax in taxes if tax.amount_type == 'percent')
            tax_amount = subtotal * (total_tax_rate / Decimal('100'))

        # Update computed fields
        self.price_subtotal = subtotal
        self.price_tax = tax_amount
        self.price_total = subtotal + tax_amount
        self.price_reduce = Decimal(str(self.price_unit)) * (Decimal('1') - Decimal(str(self.discount)) / Decimal('100')) if self.discount else Decimal(str(self.price_unit))
        self.price_reduce_taxexcl = self.price_subtotal / Decimal(str(self.product_uom_qty)) if self.product_uom_qty else Decimal('0.0')

        # Update quantities to invoice
        self.qty_to_invoice = max(Decimal('0'), Decimal(str(self.product_uom_qty)) - Decimal(str(self.qty_invoiced)))

    def save(self, *args, **kwargs):
        """Override save to compute amounts and update order totals"""
        # Set company and currency from parent order if not set
        if self.order_id:
            if not self.company_id_id:
                self.company_id = self.order_id.company_id
            if not self.currency_id_id:
                self.currency_id = self.order_id.currency_id

        # Compute line amounts
        self._compute_amounts()

        # Save the line
        super().save(*args, **kwargs)

        # Update order totals (avoid circular saves)
        if self.order_id and not kwargs.get('skip_order_update', False):
            self.order_id._compute_amounts()
            # Use update to avoid triggering save signals
            SaleOrder.objects.filter(id=self.order_id.id).update(
                amount_untaxed=self.order_id.amount_untaxed,
                amount_tax=self.order_id.amount_tax,
                amount_total=self.order_id.amount_total,
                amount_undiscounted=self.order_id.amount_undiscounted
            )

    def _get_display_price(self, product):
        """Get display price for the product"""
        # This would integrate with pricelist engine
        # For now, return product list price
        return product.list_price if product else 0.0



