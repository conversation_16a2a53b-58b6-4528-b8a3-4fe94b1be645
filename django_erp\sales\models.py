from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
from core.models import BaseModel, Company, Partner, Currency
from accounting.models import AccountJournal, AccountTax

# Supporting Models for Sales Module (defined first to avoid forward references)

class SalesTeam(BaseModel):
    """Sales Team model - equivalent to crm.team in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10, blank=True)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Team Leader")
    member_ids = models.ManyToManyField('auth.User', related_name='sales_team_members', blank=True)

    def __str__(self):
        return self.name

class ProductPricelist(BaseModel):
    """Product Pricelist model - equivalent to product.pricelist in Odoo"""
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    sequence = models.IntegerField(default=16)

    def __str__(self):
        return self.name

class ProductUomCategory(BaseModel):
    """Unit of Measure Category model - equivalent to uom.category in Odoo"""
    name = models.CharField(max_length=255)

    def __str__(self):
        return self.name

class ProductUom(BaseModel):
    """Unit of Measure model - equivalent to uom.uom in Odoo"""
    name = models.CharField(max_length=255)
    category_id = models.ForeignKey(ProductUomCategory, on_delete=models.PROTECT)
    factor = models.DecimalField(max_digits=16, decimal_places=6, default=1.0)
    factor_inv = models.DecimalField(max_digits=16, decimal_places=6, default=1.0)
    uom_type = models.CharField(max_length=20, choices=[
        ('bigger', 'Bigger than the reference Unit of Measure'),
        ('reference', 'Reference Unit of Measure for this category'),
        ('smaller', 'Smaller than the reference Unit of Measure'),
    ], default='reference')
    rounding = models.DecimalField(max_digits=16, decimal_places=6, default=0.01)
    active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

class AccountFiscalPosition(BaseModel):
    """Fiscal Position model - equivalent to account.fiscal.position in Odoo"""
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    sequence = models.IntegerField(default=10)
    auto_apply = models.BooleanField(default=False)
    vat_required = models.BooleanField(default=False)
    note = models.TextField(blank=True)

    def __str__(self):
        return self.name

class AccountPaymentTerm(BaseModel):
    """Payment Terms model - equivalent to account.payment.term in Odoo"""
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    note = models.TextField(blank=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    sequence = models.IntegerField(default=10)

    def __str__(self):
        return self.name

class DeliveryCarrier(BaseModel):
    """Delivery Carrier model - equivalent to delivery.carrier in Odoo"""
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    delivery_type = models.CharField(max_length=50, choices=[
        ('fixed', 'Fixed Price'),
        ('base_on_rule', 'Based on Rules'),
    ], default='fixed')
    fixed_price = models.DecimalField(max_digits=16, decimal_places=2, default=0.0)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)

    def __str__(self):
        return self.name

class StockWarehouse(BaseModel):
    """Warehouse model - equivalent to stock.warehouse in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, help_text="Address")

    class Meta:
        unique_together = [['code', 'company_id']]

    def __str__(self):
        return f"{self.code} - {self.name}"

class ProcurementGroup(BaseModel):
    """Procurement Group model - equivalent to procurement.group in Odoo"""
    name = models.CharField(max_length=255)
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    def __str__(self):
        return self.name

# Main Sales Models

class SaleOrder(BaseModel):
    """Sales Order model - equivalent to sale.order in Odoo"""

    STATE_CHOICES = [
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('sale', 'Sales Order'),
        ('cancel', 'Cancelled'),
    ]

    INVOICE_STATUS_CHOICES = [
        ('upselling', 'Upselling Opportunity'),
        ('invoiced', 'Fully Invoiced'),
        ('to invoice', 'To Invoice'),
        ('no', 'Nothing to Invoice'),
    ]

    # Basic Information
    name = models.CharField(max_length=255, default='New', help_text="Order Reference")
    origin = models.CharField(max_length=255, blank=True, help_text="Source Document")
    client_order_ref = models.CharField(max_length=255, blank=True, help_text="Customer Reference")

    # Partner Information
    partner_id = models.ForeignKey(Partner, on_delete=models.PROTECT, help_text="Customer")
    partner_invoice_id = models.ForeignKey(Partner, on_delete=models.PROTECT,
                                         related_name='sale_invoice_orders',
                                         help_text="Invoice Address")
    partner_shipping_id = models.ForeignKey(Partner, on_delete=models.PROTECT,
                                          related_name='sale_shipping_orders',
                                          help_text="Delivery Address")

    # Dates
    date_order = models.DateTimeField(default=timezone.now, help_text="Order Date")
    validity_date = models.DateField(blank=True, null=True, help_text="Expiration Date")
    commitment_date = models.DateTimeField(blank=True, null=True, help_text="Delivery Date")

    # State and Control
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft', db_index=True)
    locked = models.BooleanField(default=False, help_text="Locked orders cannot be modified")

    # Company and Currency
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Amounts (computed from order lines)
    amount_untaxed = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_undiscounted = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Invoicing
    invoice_status = models.CharField(max_length=20, choices=INVOICE_STATUS_CHOICES, default='no')
    amount_to_invoice = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_invoiced = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Sales Information
    user_id = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Salesperson")
    team_id = models.ForeignKey(SalesTeam, on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Sales Team")

    # Terms and Conditions
    note = models.TextField(blank=True, help_text="Terms and conditions")

    # Delivery
    carrier_id = models.ForeignKey(DeliveryCarrier, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Delivery Method")

    # Fiscal and Payment
    fiscal_position_id = models.ForeignKey(AccountFiscalPosition, on_delete=models.SET_NULL,
                                         null=True, blank=True, help_text="Fiscal Position")
    payment_term_id = models.ForeignKey(AccountPaymentTerm, on_delete=models.SET_NULL,
                                       null=True, blank=True, help_text="Payment Terms")

    # Pricelist
    pricelist_id = models.ForeignKey(ProductPricelist, on_delete=models.PROTECT,
                                    help_text="Pricelist")

    # Procurement and Warehouse
    warehouse_id = models.ForeignKey(StockWarehouse, on_delete=models.SET_NULL, null=True, blank=True)
    procurement_group_id = models.ForeignKey(ProcurementGroup, on_delete=models.SET_NULL,
                                           null=True, blank=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(
                    models.Q(state='sale', date_order__isnull=False) |
                    ~models.Q(state='sale')
                ),
                name='date_order_conditional_required'
            )
        ]
        indexes = [
            models.Index(fields=['date_order']),
            models.Index(fields=['state']),
            models.Index(fields=['partner_id']),
            models.Index(fields=['user_id']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()

        # Confirmed sales order requires a confirmation date
        if self.state == 'sale' and not self.date_order:
            raise ValidationError("A confirmed sales order requires a confirmation date.")

        # Validate partner addresses
        if self.partner_id:
            if not self.partner_invoice_id:
                self.partner_invoice_id = self.partner_id
            if not self.partner_shipping_id:
                self.partner_shipping_id = self.partner_id

    def action_confirm(self):
        """Confirm the sales order"""
        if self.state != 'draft':
            raise ValidationError("Only draft orders can be confirmed.")

        # Check credit limit if enabled
        self._check_credit_limit()

        # Update state and date
        self.state = 'sale'
        if not self.date_order:
            self.date_order = timezone.now()

        # Generate sequence number if needed
        if self.name == 'New':
            self.name = self._get_sequence_number()

        self.save()

        # Create procurement group
        self._create_procurement_group()

        # Update invoice status
        self._compute_invoice_status()

    def action_cancel(self):
        """Cancel the sales order"""
        if self.state == 'cancel':
            return

        self.state = 'cancel'
        self.save()

    def action_draft(self):
        """Reset to draft state"""
        if self.state not in ['cancel', 'sent']:
            raise ValidationError("Cannot reset to draft from current state.")

        self.state = 'draft'
        self.save()

    def _check_credit_limit(self):
        """Check customer credit limit"""
        if not self.partner_id.credit_limit:
            return

        # Get total due amount for customer
        total_due = self._get_partner_due_amount()

        if total_due + self.amount_total > self.partner_id.credit_limit:
            raise ValidationError(
                f"Credit limit exceeded for customer {self.partner_id.name}. "
                f"Credit limit: {self.partner_id.credit_limit}, "
                f"Current due: {total_due}, "
                f"Order amount: {self.amount_total}"
            )

    def _get_partner_due_amount(self):
        """Get total due amount for the partner"""
        # This would typically query account.move.line for receivable amounts
        # For now, return 0 - will be implemented when accounting integration is complete
        return Decimal('0.0')

    def _get_sequence_number(self):
        """Generate sequence number for the order"""
        # Simple sequence generation - in production, this would be more sophisticated
        last_order = SaleOrder.objects.filter(
            company_id=self.company_id,
            date_order__year=self.date_order.year
        ).exclude(name='New').order_by('-name').first()

        if last_order and last_order.name:
            try:
                last_num = int(last_order.name.split('/')[-1])
                return f"SO/{self.date_order.year}/{last_num + 1:04d}"
            except (ValueError, IndexError):
                pass

        return f"SO/{self.date_order.year}/0001"

    def _create_procurement_group(self):
        """Create procurement group for the order"""
        if not self.procurement_group_id:
            # This would create a procurement group
            # For now, we'll skip this as it requires inventory module
            pass

    def _compute_amounts(self):
        """Compute total amounts from order lines"""
        lines = self.order_line.all()

        self.amount_untaxed = sum(line.price_subtotal for line in lines if not line.display_type)
        self.amount_tax = sum(line.price_tax for line in lines if not line.display_type)
        self.amount_total = self.amount_untaxed + self.amount_tax

        # Compute undiscounted amount
        undiscounted = Decimal('0.0')
        for line in lines:
            if not line.display_type and line.discount != 100:
                if line.discount > 0:
                    undiscounted += (line.price_subtotal * 100) / (100 - line.discount)
                else:
                    undiscounted += line.price_unit * line.product_uom_qty
        self.amount_undiscounted = undiscounted

    def _compute_invoice_status(self):
        """Compute invoice status based on order lines"""
        lines = self.order_line.filter(display_type__isnull=True)

        if not lines:
            self.invoice_status = 'no'
            return

        # Check if all lines are invoiced
        all_invoiced = all(line.qty_invoiced >= line.product_uom_qty for line in lines)

        if all_invoiced:
            self.invoice_status = 'invoiced'
        elif any(line.qty_to_invoice > 0 for line in lines):
            self.invoice_status = 'to invoice'
        else:
            self.invoice_status = 'no'

    def action_cancel(self):
        """Cancel the sale order"""
        if self.state == 'done':
            raise ValidationError("Cannot cancel a locked sale order.")

        # Check for invoiced lines
        if self.order_line.filter(qty_invoiced__gt=0).exists():
            raise ValidationError("Cannot cancel order with invoiced lines.")

        # Check for delivered lines
        if self.order_line.filter(qty_delivered__gt=0).exists():
            raise ValidationError("Cannot cancel order with delivered lines.")

        self.state = 'cancel'
        self.save()

    def action_unlock(self):
        """Unlock a locked sale order"""
        if self.state == 'done':
            self.state = 'sale'
            self.save()

    def action_done(self):
        """Lock the sale order"""
        if self.state != 'sale':
            raise ValidationError("Only confirmed orders can be locked.")

        self.state = 'done'
        self.save()

    def _create_invoices(self, grouped=False, final=False):
        """Create invoices from sale order"""
        if self.state not in ['sale', 'done']:
            raise ValidationError("Only confirmed orders can be invoiced.")

        # Get lines to invoice
        lines_to_invoice = self.order_line.filter(qty_to_invoice__gt=0)
        if not lines_to_invoice:
            raise ValidationError("No lines to invoice.")

        # This would create account.move records
        # Simplified implementation
        return []

    def _prepare_invoice(self):
        """Prepare invoice values from sale order"""
        return {
            'move_type': 'out_invoice',
            'partner_id': self.partner_id.id,
            'partner_shipping_id': self.partner_shipping_id.id if self.partner_shipping_id else self.partner_id.id,
            'currency_id': self.currency_id.id,
            'payment_reference': self.client_order_ref or '',
            'invoice_origin': self.name,
            'invoice_payment_term_id': self.payment_term_id.id if self.payment_term_id else None,
            'fiscal_position_id': self.fiscal_position_id.id if self.fiscal_position_id else None,
            'company_id': self.company_id.id,
            'user_id': self.user_id.id if self.user_id else None,
            'team_id': self.team_id.id if self.team_id else None,
        }

    def unlink(self):
        """Override unlink to add business rules"""
        for order in self:
            if order.state not in ('draft', 'cancel'):
                raise ValidationError("Cannot delete confirmed orders.")
            if order.order_line.filter(qty_invoiced__gt=0).exists():
                raise ValidationError("Cannot delete orders with invoiced lines.")
        return super().unlink()

class SaleOrderLine(BaseModel):
    """Sales Order Line model - equivalent to sale.order.line in Odoo"""

    DISPLAY_TYPE_CHOICES = [
        ('line_section', 'Section'),
        ('line_note', 'Note'),
    ]

    # Parent order
    order_id = models.ForeignKey(SaleOrder, on_delete=models.CASCADE, related_name='order_line')

    # Product and description (will be linked when inventory module is created)
    # product_id = models.ForeignKey('inventory.Product', on_delete=models.PROTECT, null=True, blank=True)
    name = models.TextField(help_text="Description")

    # Quantities
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1.0,
                                        help_text="Ordered Quantity")
    qty_delivered = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                      help_text="Delivered Quantity")
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                     help_text="Invoiced Quantity")
    qty_to_invoice = models.DecimalField(max_digits=16, decimal_places=4, default=0.0,
                                       help_text="To Invoice Quantity")

    # Unit of Measure
    product_uom = models.ForeignKey(ProductUom, on_delete=models.PROTECT, null=True, blank=True,
                                   help_text="Unit of Measure")

    # Pricing
    price_unit = models.DecimalField(max_digits=20, decimal_places=4, default=0.0, help_text="Unit Price")
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0.0, help_text="Discount (%)")

    # Computed amounts
    price_subtotal = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                       help_text="Subtotal")
    price_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                  help_text="Tax Amount")
    price_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                    help_text="Total")
    price_reduce = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                     help_text="Unit Price with Discount")
    price_reduce_taxexcl = models.DecimalField(max_digits=20, decimal_places=4, default=0.0,
                                             help_text="Unit Price with Discount (Tax Excluded)")

    # Taxes
    tax_id = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes")

    # Delivery
    customer_lead = models.IntegerField(default=0, help_text="Lead Time (days)")

    # Display and ordering
    sequence = models.IntegerField(default=10, help_text="Sequence")
    display_type = models.CharField(max_length=20, choices=DISPLAY_TYPE_CHOICES, null=True, blank=True,
                                   help_text="Technical field for UX purpose")

    # Company
    company_id = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency_id = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # State tracking - computed from order
    # state will be accessed via order_id.state

    # Invoice lines relationship (will be added when accounting integration is complete)
    # invoice_lines = models.ManyToManyField('account.MoveLine', blank=True)

    class Meta:
        # Constraints will be handled in clean() method since product_id is commented out
        pass
        indexes = [
            models.Index(fields=['order_id']),
            models.Index(fields=['sequence']),
        ]

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"

    def save(self, *args, **kwargs):
        # Set company and currency from order
        if self.order_id:
            self.company_id = self.order_id.company_id
            self.currency_id = self.order_id.currency_id

        # Compute amounts
        self._compute_amounts()

        super().save(*args, **kwargs)

        # Update order totals
        if self.order_id:
            self.order_id._compute_amounts()
            self.order_id.save()

    def _prepare_invoice_line(self):
        """Prepare invoice line values from sale order line"""
        if self.display_type:
            return {
                'display_type': self.display_type,
                'name': self.name,
                'sequence': self.sequence,
            }

        return {
            'name': self.name,
            'quantity': self.qty_to_invoice,
            'price_unit': self.price_unit,
            'discount': self.discount,
            'product_id': getattr(self, 'product_id', None),
            'product_uom_id': self.product_uom,
            'tax_ids': [(6, 0, self.tax_id.values_list('id', flat=True))],
            'analytic_account_id': getattr(self, 'analytic_account_id', None),
            'sale_line_ids': [(4, self.id)],
        }

    def _get_delivered_qty(self):
        """Get delivered quantity for this line"""
        # Simplified - in real implementation would check stock moves
        return self.qty_delivered

    def _compute_qty_to_invoice(self):
        """Compute quantity to invoice"""
        if self.display_type:
            self.qty_to_invoice = 0
            return

        # Based on invoice policy
        if hasattr(self, 'product_id') and self.product_id:
            if getattr(self.product_id, 'invoice_policy', 'order') == 'delivery':
                # Invoice based on delivered quantity
                self.qty_to_invoice = max(0, self.qty_delivered - self.qty_invoiced)
            else:
                # Invoice based on ordered quantity
                self.qty_to_invoice = max(0, self.product_uom_qty - self.qty_invoiced)
        else:
            self.qty_to_invoice = max(0, self.product_uom_qty - self.qty_invoiced)

    def _check_line_unlink(self):
        """Check if line can be deleted"""
        if self.order_id.state in ['sale', 'done']:
            if self.qty_invoiced > 0:
                raise ValidationError("Cannot delete invoiced order lines.")
            if self.qty_delivered > 0:
                raise ValidationError("Cannot delete delivered order lines.")

    def unlink(self):
        """Override unlink to add business rules"""
        for line in self:
            line._check_line_unlink()
        return super().unlink()

    def clean(self):
        super().clean()

        # Validate required fields for accountable lines
        if not self.display_type:
            if not self.product_uom:
                raise ValidationError("Unit of Measure is required for accountable lines.")

        # Validate forbidden values for non-accountable lines
        if self.display_type:
            if (self.price_unit != 0 or self.product_uom_qty != 0 or
                self.product_uom or self.customer_lead != 0):
                raise ValidationError("Non-accountable lines cannot have product-related values.")

    def _compute_amounts(self):
        """Compute line amounts including taxes"""
        if self.display_type:
            # Section and note lines have no amounts
            self.price_subtotal = 0.0
            self.price_tax = 0.0
            self.price_total = 0.0
            self.price_reduce = 0.0
            self.price_reduce_taxexcl = 0.0
            return

        # Calculate base amount with discount
        base_amount = Decimal(str(self.price_unit)) * Decimal(str(self.product_uom_qty))
        discount_amount = base_amount * (Decimal(str(self.discount)) / Decimal('100'))
        subtotal = base_amount - discount_amount

        # Calculate taxes (simplified - in real implementation would use tax engine)
        tax_amount = Decimal('0.0')
        if self.tax_id.exists():
            # Simple tax calculation - sum all tax rates
            total_tax_rate = sum(Decimal(str(tax.amount)) for tax in self.tax_id.all() if tax.amount_type == 'percent')
            tax_amount = subtotal * (total_tax_rate / Decimal('100'))

        # Update computed fields
        self.price_subtotal = subtotal
        self.price_tax = tax_amount
        self.price_total = subtotal + tax_amount
        self.price_reduce = Decimal(str(self.price_unit)) * (Decimal('1') - Decimal(str(self.discount)) / Decimal('100')) if self.discount else Decimal(str(self.price_unit))
        self.price_reduce_taxexcl = self.price_subtotal / Decimal(str(self.product_uom_qty)) if self.product_uom_qty else Decimal('0.0')

        # Update quantities to invoice
        self.qty_to_invoice = max(Decimal('0'), Decimal(str(self.product_uom_qty)) - Decimal(str(self.qty_invoiced)))

    def _get_display_price(self, product):
        """Get display price for the product"""
        # This would integrate with pricelist engine
        # For now, return product list price
        return product.list_price if product else 0.0
