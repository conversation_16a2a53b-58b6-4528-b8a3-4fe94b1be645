# Translation of OpenERP Server.
# This file contains the translation of the following modules:
# 	* account_asset
#
msgid ""
msgstr ""
"Project-Id-Version: OpenERP Server 5.0.6\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2009-11-24 12:54:56+0000\n"
"PO-Revision-Date: 2009-11-24 12:54:56+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"\n"
"Error while processing asset '{ref}': {exception}"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
msgid ""
"<span class=\"o_form_label oe_inline\" invisible=\"salvage_type != "
"'percent'\">%</span>"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.view_move_form
msgid "<span class=\"o_stat_text\"> Asset(s)</span>"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model,name:account_asset_management.model_account_account
#, python-format
msgid "Account"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Account Asset"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__carry_forward_missed_depreciations
msgid "Accumulate missed depreciations"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__active
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__active
msgid "Active"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Active Assets"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_ids
msgid "Activities"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_state
msgid "Activity State"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Add an internal note here..."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_compute__date_end
msgid "All depreciation lines prior to this date will be automatically posted"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__allow_reversal
msgid "Allow Reversal of journal entries"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__amount
msgid "Amount"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__depreciated_value
msgid "Amount Already Depreciated"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__analytic_distribution
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__analytic_distribution_search
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
msgid "Analytic Information"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__analytic_precision
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Archived"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Are you sure ?"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__asset_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_move_line__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Asset"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "Asset '%s' Removal Journal Entry"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_asset_id
msgid "Asset Account"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_bank_statement_line__asset_count
#: model:ir.model.fields,field_description:account_asset_management.field_account_move__asset_count
#: model:ir.model.fields,field_description:account_asset_management.field_account_payment__asset_count
msgid "Asset Count"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_group
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__asset_group_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_group_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_group_view_search
msgid "Asset Group"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_group_action
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__group_ids
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__group_ids
#: model:ir.ui.menu,name:account_asset_management.account_asset_group_menu
msgid "Asset Groups"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__line_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Asset Line"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.server,name:account_asset_management.ir_cron_assets_generator_ir_actions_server
msgid "Asset Management: Generate assets"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__name
msgid "Asset Name"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_account__asset_profile_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__profile_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_move_line__asset_profile_id
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
msgid "Asset Profile"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_profile_action
#: model:ir.ui.menu,name:account_asset_management.account_asset_profile_menu
msgid "Asset Profiles"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_line__type__remove
msgid "Asset Removal"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__date_remove
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__date_remove
#, python-format
msgid "Asset Removal Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_sale_id
msgid "Asset Sale Account"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__date_start
#, python-format
msgid "Asset Start Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_line
msgid "Asset depreciation table line"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid "Asset name must be set in the label of the line."
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_profile
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
msgid "Asset profile"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Asset removal."
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_recompute_trigger
msgid "Asset table recompute triggers"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_action
#: model:ir.ui.menu,name:account_asset_management.account_asset_menu
#: model:ir.ui.menu,name:account_asset_management.menu_finance_assets
#: model:ir.ui.menu,name:account_asset_management.menu_finance_config_assets
msgid "Assets"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Assets to be corrected"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__asset_product_item
msgid ""
"By default during the validation of an invoice, an asset is created by "
"invoice line as long as an accounting entry is created by invoice line. With "
"this setting, an accounting entry will be created by product item. So, there "
"will be an asset by product item."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__days_calc
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__days_calc
msgid "Calculate by days"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_asset_move_reverse_view_form
msgid "Cancel"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this profile "
"when created by invoices."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__child_ids
msgid "Child Asset Groups"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method_time
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method_time
msgid ""
"Choose the method to use to compute the dates and number of depreciation "
"lines.\n"
"  * Number of Years: Specify the number of years for the depreciation.\n"
"  * Number of Depreciations: Fix the number of depreciation lines and the "
"time between 2 depreciations.\n"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method
msgid ""
"Choose the method to use to compute the depreciation lines.\n"
"  * Linear: Calculated on basis of: Depreciation Base / Number of "
"Depreciations. Depreciation Base = Purchase Value - Salvage Value.\n"
"  * Linear-Limit: Linear up to Salvage Value. Depreciation Base = Purchase "
"Value.\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor.\n"
"  * Degressive-Linear (only for Time Method = Year): Degressive becomes "
"linear when the annual linear depreciation exceeds the annual degressive "
"depreciation.\n"
"   * Degressive-Limit: Degressive up to Salvage Value. The Depreciation Base "
"is equal to the asset value."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Close"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__code
msgid "Code"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__company_id
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__company_id
msgid "Company"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__currency_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__currency_id
msgid "Company Currency"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__date_completed
msgid "Completion Date"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Comput. Method"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Computation Method"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Compute"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form
msgid "Compute Asset"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.account_asset_compute_action
#: model:ir.model,name:account_asset_management.model_account_asset_compute
#: model:ir.ui.menu,name:account_asset_management.account_asset_compute_menu
msgid "Compute Assets"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_compute.py:0
#, python-format
msgid "Compute Assets errors"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_compute.py:0
#, python-format
msgid "Compute Assets result"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
msgid "Compute Assets results"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_asset_move_reverse_view_form
msgid "Confirm"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Confirm Asset"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Create Move"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__asset_product_item
msgid "Create an asset by product item"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_compute.py:0
#, python-format
msgid "Created Asset Moves"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__create_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__create_uid
msgid "Created by"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__create_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__create_date
msgid "Created on"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__date_end
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__line_date
msgid "Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_recompute_trigger__date_trigger
msgid "Date of the event triggering the need to recompute the Asset Tables."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__line_days
msgid "Days"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_account__asset_profile_id
msgid "Default Asset Profile when creating invoice lines with this account."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive  up to Salvage Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_progress_factor
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_progress_factor
msgid "Degressive Factor"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive-Linear"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Degressive-Linear is only supported for Time Method = Year."
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Delete/Reverse Move"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_expense_depreciation_id
msgid "Depr. Expense Account"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__value_depreciated
msgid "Depreciated Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_line__type__depreciate
msgid "Depreciation"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_depreciation_id
msgid "Depreciation Account"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__depreciation_base
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__depreciation_base
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_line__type__create
#, python-format
msgid "Depreciation Base"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Depreciation Board"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Depreciation Dates"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__move_id
msgid "Depreciation Entry"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__depreciation_line_ids
msgid "Depreciation Lines"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Depreciation Method"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__name
msgid "Depreciation Name"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__display_name
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__display_name
msgid "Display Name"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_recompute_trigger__state__done
msgid "Done"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__draft
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Draft"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Duplicate reporting entries"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_report_account_asset_management_asset_report_xls
msgid "Dynamic XLS asset report generator"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__date_to
msgid "End Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_end
msgid "Ending Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__account_move_line_ids
msgid "Entries"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"Error while processing asset '{ref}': \n"
"\n"
"{tb}"
msgstr ""

#. module: account_asset_management
#: model:ir.ui.menu,name:account_asset_management.account_asset_report_menu
msgid "Financial Assets"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.wiz_account_asset_report_action
#: model:ir.model,name:account_asset_management.model_wiz_account_asset_report
#: model:ir.ui.menu,name:account_asset_management.wiz_account_asset_report_menu
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
msgid "Financial Assets report"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_profile__salvage_type__fixed
msgid "Fixed"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__force_date
msgid "Force accounting date"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "Gain/Loss on Sale"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "General"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "Generate Asset Removal entries"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
msgid "Generate Removal entries"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
msgid "Generate Report"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__has_message
msgid "Has Message"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__move_line_check
msgid "Has accounting entries"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "History"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__id
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__id
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__id
msgid "ID"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__carry_forward_missed_depreciations
msgid ""
"If create an asset in a fiscal period that is now closed\n"
"        the accumulated amount of depreciations that cannot be posted will "
"be\n"
"        carried forward to the first depreciation line of the current open\n"
"        period."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_wiz_asset_move_reverse__journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__use_leap_years
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__use_leap_years
msgid ""
"If not set, the system will distribute evenly the amount to amortize across "
"the years, based on the number of years. So the amount per year will be the "
"depreciation base / number of years.\n"
" If set, the system will consider if the current year is a leap year. The "
"amount to depreciate per year will be calculated as depreciation base / "
"(depreciation end date - start date + 1) * days in the current year."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__allow_reversal
msgid ""
"If set, when pressing the Delete/Reverse Move button in a posted "
"depreciation line will prompt the option to reverse the journal entry, "
"instead of deleting them."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "Illegal value %s in asset.method."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__draft
msgid "Include draft assets"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid ""
"Inconsistent reporting structure.\n"
"Please correct Asset Group '{group}' (id {id})"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__prorata
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__prorata
msgid ""
"Indicates that the first depreciation entry for this asset has to be done "
"from the depreciation start date instead of the first day of the fiscal year."
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Init"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__init_entry
msgid "Initial Balance Entry"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__journal_id
msgid "Journal"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
#, python-format
msgid "Journal Entries"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#: model:ir.model,name:account_asset_management.model_account_move
#, python-format
msgid "Journal Entry"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_asset_management
#: model:ir.actions.act_window,name:account_asset_management.act_entries_open
msgid "Journal Items"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__write_uid
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__write_date
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Linear"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Linear up to Salvage Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_ids
msgid "Messages"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_min_value_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_min_value_id
msgid "Min-Value Account"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Missing depreciation table"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Month"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__name
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__name
#, python-format
msgid "Name"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "New Acquisitions"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__remaining_value
msgid "Next Period Depreciation"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "No"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__note
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_compute__note
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__note
msgid "Note"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__note
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Notes"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Number of Depreciations"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_number
#, python-format
msgid "Number of Years"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Number of Years or end date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_recompute_trigger__state__open
msgid "Open"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Other Information"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__parent_id
msgid "Parent Asset Group"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_group__parent_path
msgid "Parent Path"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__partner_id
msgid "Partner"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset_profile__salvage_type__percent
msgid "Percentage of Price"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Period Depreciation"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Period End Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_period
msgid "Period Length"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Period Start Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method_period
msgid "Period length for the depreciation accounting entries"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_plus_value_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_plus_value_id
msgid "Plus-Value Account"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__move_check
msgid "Posted"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__previous_id
msgid "Previous Depreciation Line"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Profile"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__prorata
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__prorata
#, python-format
msgid "Prorata Temporis"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__purchase_value
#, python-format
msgid "Purchase Value"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Quarter"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__reason
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__reason
#, python-format
msgid "Reason"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__code
#, python-format
msgid "Reference"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__posting_regime
msgid "Removal Entry Policy"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_remove__posting_regime
msgid ""
"Removal Entry Policy \n"
"  * Residual Value: The non-depreciated value will be posted on the "
"'Residual Value Account' \n"
"  * Gain/Loss on Sale: The Gain or Loss will be posted on the 'Plus-Value "
"Account' or 'Min-Value Account' "
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_remove__date_remove
msgid ""
"Removal date must be after the last posted entry in case of early removal"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Remove"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_account_asset_remove
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_remove_view_form
msgid "Remove Asset"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__removed
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Removed"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Removed Assets"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__value_residual
#, python-format
msgid "Residual Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__account_residual_value_id
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__account_residual_value_id
msgid "Residual Value Account"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
msgid "Results :"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__date_reversal
msgid "Reversal date"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_asset_move_reverse_view_form
msgid "Reverse Journal Entry"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid "Reverse Move"
msgstr ""

#. module: account_asset_management
#: model:ir.model,name:account_asset_management.model_wiz_asset_move_reverse
msgid "Reverse posted journal entry on depreciation line"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields.selection,name:account_asset_management.selection__account_asset__state__open
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
msgid "Running"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_remove__sale_value
msgid "Sale Value"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__salvage_type
msgid "Salvage Type"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__salvage_value
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__salvage_value
#, python-format
msgid "Salvage Value"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_group_view_search
msgid "Search Asset Group"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_profile_view_search
msgid "Search Asset Profile"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset_line__init_entry
msgid ""
"Set this flag for entries of previous fiscal years for which Odoo has not "
"generated accounting entries."
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Set to Draft"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__open_asset
msgid "Skip Draft State"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_account_asset_report__date_from
msgid "Start Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__state
msgid "State"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__parent_state
msgid "State of Asset"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__state
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_search
#, python-format
msgid "Status"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"The '_compute_year_amount' method is only intended for Time Method 'Number "
"of Years'."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "The 'account' field is a mandatory entry of the '_xls_%s_fields' list !"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_account.py:0
#, python-format
msgid ""
"The Asset Account defined in the Asset Profile must be equal to the account."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "The Sale Value must be positive!"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#: code:addons/account_asset_management/wizard/wiz_account_asset_report.py:0
#, python-format
msgid "The Start Date must precede the Ending Date."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid ""
"The duration of the asset conflicts with the posted depreciation table entry "
"dates."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__salvage_value
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__salvage_value
msgid ""
"The estimated value that an asset will realize upon its sale at the end of "
"its useful life.\n"
"This value is used to determine the depreciation amounts."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__method_number
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__method_number
msgid "The number of years needed to depreciate your asset"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid "The removal date must be after the last depreciation date."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__depreciation_base
#: model:ir.model.fields,help:account_asset_management.field_account_asset_line__depreciation_base
msgid ""
"This amount represent the depreciation base of the asset (Purchase Value - "
"Salvage Value)."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__purchase_value
msgid ""
"This amount represent the initial value of the asset.\n"
"The Depreciation Base is calculated as follows:\n"
"Purchase Value - Salvage Value."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid "This invoice created the asset(s): %s"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__method_time
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__method_time
msgid "Time Method"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Tot. Depreciation"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "Total Days"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Totals"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_recompute_trigger__date_trigger
msgid "Trigger Date"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_line__type
msgid "Type"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/report/account_asset_report_xls.py:0
#, python-format
msgid "Undetermined error"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__use_leap_years
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset_profile__use_leap_years
msgid "Use Leap Years"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_wiz_asset_move_reverse__journal_id
msgid "Use Specific Journal"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__days_calc
#: model:ir.model.fields,help:account_asset_management.field_account_asset_profile__days_calc
msgid "Use number of days to calculate depreciation amount"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_compute_view_form_result
msgid "View Asset Moves"
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.account_asset_view_form
msgid "View Move"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,field_description:account_asset_management.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__state
#: model:ir.model.fields,help:account_asset_management.field_account_asset_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation "
"lines can be posted to the accounting.\n"
"If the last depreciation line is posted, the asset goes into the 'Close' "
"status.\n"
"When the removal entries are generated, the asset goes into the 'Removed' "
"status."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_profile.py:0
#, python-format
msgid "Year"
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You are not allowed to link an accounting entry to an asset.\n"
"You should generate such entries from the asset."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You are not allowed to remove an accounting entry linked to an asset.\n"
"You should remove such entries from the asset."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "You can only delete assets in draft state."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/wizard/account_asset_remove.py:0
#, python-format
msgid ""
"You can't make an early removal if all the depreciation lines for previous "
"periods are not posted."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot change a depreciation line with an associated accounting entry."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You cannot change an accounting entry linked to an asset depreciation line."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_move.py:0
#, python-format
msgid ""
"You cannot change an accounting item linked to an asset depreciation line."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "You cannot change the profile of an asset with accounting entries."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot delete a depreciation line with an associated accounting entry."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset.py:0
#, python-format
msgid "You cannot delete an asset that contains posted depreciation lines."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid "You cannot remove an asset line of type 'Depreciation Base'."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot set the 'Initial Balance Entry' flag on a depreciation line with "
"prior posted entries."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid "You cannot set the Asset Start Date after already posted entries."
msgstr ""

#. module: account_asset_management
#. odoo-python
#: code:addons/account_asset_management/models/account_asset_line.py:0
#, python-format
msgid ""
"You cannot set the date on a depreciation line prior to already posted "
"entries."
msgstr ""

#. module: account_asset_management
#: model:ir.model.fields,help:account_asset_management.field_account_asset__date_start
msgid ""
"You should manually add depreciation lines with the depreciations of "
"previous fiscal years if the Depreciation Start Date is different from the "
"date for which accounting entries need to be generated."
msgstr ""

#. module: account_asset_management
#: model_terms:ir.ui.view,arch_db:account_asset_management.wiz_account_asset_report_view_form
msgid "or"
msgstr ""
