# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_daily_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-22 12:18+0000\n"
"PO-Revision-Date: 2024-03-22 12:18+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>End Date:</strong>"
msgstr "<strong>Fecha final:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Journals:</strong>"
msgstr "<strong>Diarios:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Ordenado por:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Fecha de inicio:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Movimientos de destino:</strong>"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
msgid "Account Bank Book"
msgstr "Libro de Cuentas bancarias"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "Account Cash Book"
msgstr "Libro de Cuenta Efectivo"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Account Day Book"
msgstr "Libro Diario"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__account_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__account_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__account_ids
msgid "Accounts"
msgstr "Cuentas"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__all
msgid "All"
msgstr "Todo"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__target_move__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__target_move__all
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_daybook_report__target_move__all
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "All Entries"
msgstr "Todos los asientos"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Balance"
msgstr "Saldo"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_bankbook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_bank_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_bankbook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_bankbook
msgid "Bank Book"
msgstr "Libro de Banco"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_bankbook_report
msgid "Bank Book Report"
msgstr "Reporte del Libro de Banco"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Cancel"
msgstr "Cancelar"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_cashbook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_cash_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_cashbook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_cashbook
msgid "Cash Book"
msgstr "Libro de Caja"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_cashbook_report
msgid "Cash Book Report"
msgstr "Reporte del Libro de Caja"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__create_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__create_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__create_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__create_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__create_date
msgid "Created on"
msgstr "Creado en"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Credit"
msgstr "Crédito"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Currency"
msgstr "Moneda"

#. module: om_account_daily_reports
#: model:ir.ui.menu,name:om_account_daily_reports.menu_finance_daily_reports
msgid "Daily Reports"
msgstr "Reportes diarios"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__sortby__sort_date
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__sortby__sort_date
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Date"
msgstr "Fecha"

#. module: om_account_daily_reports
#: model:ir.actions.act_window,name:om_account_daily_reports.action_account_daybook_menu
#: model:ir.actions.report,name:om_account_daily_reports.action_report_day_book
#: model:ir.model,name:om_account_daily_reports.model_report_om_account_daily_reports_report_daybook
#: model:ir.ui.menu,name:om_account_daily_reports.menu_daybook
msgid "Day Book"
msgstr "Libro Diario"

#. module: om_account_daily_reports
#: model:ir.model,name:om_account_daily_reports.model_account_daybook_report
msgid "Day Book Report"
msgstr "Reporte del Libro Diario"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Debit"
msgstr "Débito"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__display_account
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__display_account
msgid "Display Accounts"
msgstr "Cuentas mostradas"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__display_name
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__display_name
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__date_to
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__date_to
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__date_to
msgid "End Date"
msgstr "Fecha final"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Entry Label"
msgstr "Descripción de asiento"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__id
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__id
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__id
msgid "ID"
msgstr ""

#. module: om_account_daily_reports
#: model:ir.model.fields,help:om_account_daily_reports.field_account_bankbook_report__initial_balance
#: model:ir.model.fields,help:om_account_daily_reports.field_account_cashbook_report__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""
"Si seleccionó la fecha, este campo le permite agregar una fila para mostrar "
"la cantidad de débito/crédito/saldo que precede al filtro que ha "
"establecido."

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__initial_balance
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__initial_balance
msgid "Include Initial Balances"
msgstr "Incluir saldos iniciales"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "JRNL"
msgstr ""

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__sortby__sort_journal_partner
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr "Diario y Empresa"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
msgid "Journal and Partner"
msgstr "Diario y Empresa"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__journal_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__journal_ids
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__journal_ids
msgid "Journals"
msgstr "Diarios"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__write_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__write_uid
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__write_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__write_date
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Move"
msgstr "Movimiento"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Partner"
msgstr "Empresa"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__target_move__posted
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__target_move__posted
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_daybook_report__target_move__posted
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Posted Entries"
msgstr "Asientos publicados"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Print"
msgstr "Imprimir"

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_bankbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_cashbook
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.report_daybook
msgid "Ref"
msgstr ""

#. module: om_account_daily_reports
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_bankbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_cashbook_view
#: model_terms:ir.ui.view,arch_db:om_account_daily_reports.account_report_daybook_view
msgid "Report Options"
msgstr "Opciones de reporte"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__sortby
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__sortby
msgid "Sort by"
msgstr "Ordenado por"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__date_from
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__date_from
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__date_from
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: om_account_daily_reports
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_bankbook_report__target_move
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_cashbook_report__target_move
#: model:ir.model.fields,field_description:om_account_daily_reports.field_account_daybook_report__target_move
msgid "Target Moves"
msgstr "Movimientos de destino"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__not_zero
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "Con saldo no es igual a 0"

#. module: om_account_daily_reports
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_bankbook_report__display_account__movement
#: model:ir.model.fields.selection,name:om_account_daily_reports.selection__account_cashbook_report__display_account__movement
msgid "With movements"
msgstr "Con movimientos"
