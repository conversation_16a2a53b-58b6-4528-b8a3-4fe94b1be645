"""
Management command to load standard currencies
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from core.models import Currency


class Command(BaseCommand):
    help = 'Load standard currencies with exchange rates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing currencies',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading currencies...'))
        
        # Standard currencies data
        currencies_data = [
            {
                'name': 'USD',  # ISO code as name
                'symbol': '$',
                'rate': 1.0,  # Base currency
                'active': True,
                'rounding': 0.01,
            },
            {
                'name': 'Euro',
                'symbol': '€',
                'iso_code': 'EUR',
                'rate': 0.85,  # Approximate rate
                'active': True,
                'position': 'after',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'British Pound',
                'symbol': '£',
                'iso_code': 'GBP',
                'rate': 0.73,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'Japanese Yen',
                'symbol': '¥',
                'iso_code': 'JPY',
                'rate': 110.0,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 0,
                'rounding': 1.0,
            },
            {
                'name': 'Canadian Dollar',
                'symbol': 'C$',
                'iso_code': 'CAD',
                'rate': 1.25,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'Australian Dollar',
                'symbol': 'A$',
                'iso_code': 'AUD',
                'rate': 1.35,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'Swiss Franc',
                'symbol': 'CHF',
                'iso_code': 'CHF',
                'rate': 0.92,  # Approximate rate
                'active': True,
                'position': 'after',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'Chinese Yuan',
                'symbol': '¥',
                'iso_code': 'CNY',
                'rate': 6.45,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'Indian Rupee',
                'symbol': '₹',
                'iso_code': 'INR',
                'rate': 74.5,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 2,
                'rounding': 0.01,
            },
            {
                'name': 'Brazilian Real',
                'symbol': 'R$',
                'iso_code': 'BRL',
                'rate': 5.2,  # Approximate rate
                'active': True,
                'position': 'before',
                'decimal_places': 2,
                'rounding': 0.01,
            },
        ]

        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for currency_data in currencies_data:
                iso_code = currency_data['iso_code']
                
                try:
                    currency = Currency.objects.get(iso_code=iso_code)
                    if options['update']:
                        # Update existing currency
                        for field, value in currency_data.items():
                            setattr(currency, field, value)
                        currency.save()
                        updated_count += 1
                        self.stdout.write(f'Updated currency: {currency.name} ({iso_code})')
                    else:
                        self.stdout.write(f'Currency already exists: {currency.name} ({iso_code})')
                        
                except Currency.DoesNotExist:
                    # Create new currency
                    currency = Currency.objects.create(**currency_data)
                    created_count += 1
                    self.stdout.write(f'Created currency: {currency.name} ({iso_code})')

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCurrency loading completed!\n'
                f'Created: {created_count} currencies\n'
                f'Updated: {updated_count} currencies\n'
                f'Total currencies in system: {Currency.objects.count()}'
            )
        )
        
        # Show loaded currencies
        self.stdout.write('\nLoaded currencies:')
        for currency in Currency.objects.filter(active=True).order_by('iso_code'):
            self.stdout.write(f'  {currency.iso_code} - {currency.name} ({currency.symbol})')
