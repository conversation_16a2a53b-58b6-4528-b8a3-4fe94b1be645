<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Sample Financial Statement Templates -->
        <record id="template_balance_sheet_analytics" model="financial.statement.analytics">
            <field name="name">Balance Sheet with Analytics Template</field>
            <field name="statement_type">balance_sheet</field>
            <field name="reporting_period">annual</field>
            <field name="enable_analytics">True</field>
            <field name="enable_charts">True</field>
            <field name="enable_drill_down">True</field>
            <field name="state">draft</field>
        </record>

        <record id="template_complete_set_analytics" model="financial.statement.analytics">
            <field name="name">Complete Financial Statements with Analytics Template</field>
            <field name="statement_type">complete_set</field>
            <field name="reporting_period">annual</field>
            <field name="enable_analytics">True</field>
            <field name="enable_charts">True</field>
            <field name="enable_drill_down">True</field>
            <field name="comparative_period">True</field>
            <field name="state">draft</field>
        </record>

        <!-- Standard Ratio Benchmarks -->
        <record id="benchmark_current_ratio" model="financial.ratio.analysis">
            <field name="statement_id" ref="template_complete_set_analytics"/>
            <field name="ratio_category">liquidity</field>
            <field name="ratio_name">Current Ratio</field>
            <field name="formula">Current Assets / Current Liabilities</field>
            <field name="benchmark_value">2.0</field>
            <field name="industry_average">1.8</field>
            <field name="description">Measures the ability to pay short-term obligations with current assets</field>
            <field name="sequence">10</field>
        </record>

        <record id="benchmark_quick_ratio" model="financial.ratio.analysis">
            <field name="statement_id" ref="template_complete_set_analytics"/>
            <field name="ratio_category">liquidity</field>
            <field name="ratio_name">Quick Ratio</field>
            <field name="formula">(Current Assets - Inventory) / Current Liabilities</field>
            <field name="benchmark_value">1.0</field>
            <field name="industry_average">0.9</field>
            <field name="description">Measures the ability to pay short-term obligations with liquid assets</field>
            <field name="sequence">20</field>
        </record>

        <record id="benchmark_debt_to_equity" model="financial.ratio.analysis">
            <field name="statement_id" ref="template_complete_set_analytics"/>
            <field name="ratio_category">leverage</field>
            <field name="ratio_name">Debt to Equity</field>
            <field name="formula">Total Debt / Total Equity</field>
            <field name="benchmark_value">0.5</field>
            <field name="industry_average">0.6</field>
            <field name="description">Measures the relative proportion of debt and equity financing</field>
            <field name="sequence">30</field>
        </record>

        <record id="benchmark_roe" model="financial.ratio.analysis">
            <field name="statement_id" ref="template_complete_set_analytics"/>
            <field name="ratio_category">profitability</field>
            <field name="ratio_name">Return on Equity</field>
            <field name="formula">Net Income / Average Shareholders Equity × 100</field>
            <field name="benchmark_value">15.0</field>
            <field name="industry_average">12.5</field>
            <field name="description">Measures the return generated on shareholders equity</field>
            <field name="sequence">40</field>
        </record>

        <record id="benchmark_roa" model="financial.ratio.analysis">
            <field name="statement_id" ref="template_complete_set_analytics"/>
            <field name="ratio_category">profitability</field>
            <field name="ratio_name">Return on Assets</field>
            <field name="formula">Net Income / Average Total Assets × 100</field>
            <field name="benchmark_value">10.0</field>
            <field name="industry_average">8.5</field>
            <field name="description">Measures how efficiently assets are used to generate profit</field>
            <field name="sequence">50</field>
        </record>

        <!-- Sample Statement Lines for Balance Sheet -->
        <record id="sample_line_assets_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">ASSETS</field>
            <field name="line_type">header</field>
            <field name="line_section">assets</field>
            <field name="sequence">10</field>
            <field name="bold">True</field>
            <field name="font_size">large</field>
            <field name="indent_level">0</field>
        </record>

        <record id="sample_line_current_assets_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Current Assets</field>
            <field name="line_type">header</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">20</field>
            <field name="bold">True</field>
            <field name="indent_level">1</field>
            <field name="current_amount">1766516.20</field>
            <field name="comparative_amount">1500000.00</field>
        </record>

        <record id="sample_line_bank_cash" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Bank and Cash Accounts</field>
            <field name="line_type">line</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">30</field>
            <field name="indent_level">2</field>
            <field name="current_amount">0.00</field>
            <field name="comparative_amount">0.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="sample_line_receivables_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Receivables</field>
            <field name="line_type">header</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">40</field>
            <field name="indent_level">2</field>
            <field name="bold">True</field>
        </record>

        <record id="sample_line_debtors" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">100400 Debtors</field>
            <field name="line_type">line</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">50</field>
            <field name="indent_level">3</field>
            <field name="current_amount">1670385.83</field>
            <field name="comparative_amount">1415000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="sample_line_current_assets_other" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Current Assets</field>
            <field name="line_type">line</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">60</field>
            <field name="indent_level">2</field>
            <field name="current_amount">96130.37</field>
            <field name="comparative_amount">85000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="sample_line_prepayments" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Prepayments</field>
            <field name="line_type">line</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">70</field>
            <field name="indent_level">2</field>
            <field name="current_amount">0.00</field>
            <field name="comparative_amount">0.00</field>
        </record>

        <record id="sample_line_fixed_assets" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Plus Fixed Assets</field>
            <field name="line_type">subtotal</field>
            <field name="line_section">non_current_assets</field>
            <field name="sequence">80</field>
            <field name="indent_level">1</field>
            <field name="current_amount">0.00</field>
            <field name="comparative_amount">0.00</field>
        </record>

        <record id="sample_line_non_current_assets" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Plus Non-current Assets</field>
            <field name="line_type">subtotal</field>
            <field name="line_section">non_current_assets</field>
            <field name="sequence">90</field>
            <field name="indent_level">1</field>
            <field name="current_amount">0.00</field>
            <field name="comparative_amount">0.00</field>
        </record>

        <!-- LIABILITIES Section -->
        <record id="sample_line_liabilities_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">LIABILITIES</field>
            <field name="line_type">header</field>
            <field name="line_section">liabilities</field>
            <field name="sequence">100</field>
            <field name="bold">True</field>
            <field name="font_size">large</field>
            <field name="indent_level">0</field>
            <field name="current_amount">735685.76</field>
            <field name="comparative_amount">625000.00</field>
        </record>

        <record id="sample_line_current_liabilities_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Current Liabilities</field>
            <field name="line_type">header</field>
            <field name="line_section">current_liabilities</field>
            <field name="sequence">110</field>
            <field name="bold">True</field>
            <field name="indent_level">1</field>
            <field name="current_amount">735685.76</field>
            <field name="comparative_amount">625000.00</field>
        </record>

        <record id="sample_line_current_liabilities" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Current Liabilities</field>
            <field name="line_type">line</field>
            <field name="line_section">current_liabilities</field>
            <field name="sequence">120</field>
            <field name="indent_level">2</field>
            <field name="current_amount">334912.16</field>
            <field name="comparative_amount">285000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="sample_line_payables" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Payables</field>
            <field name="line_type">header</field>
            <field name="line_section">current_liabilities</field>
            <field name="sequence">130</field>
            <field name="indent_level">2</field>
            <field name="bold">True</field>
            <field name="current_amount">400773.60</field>
            <field name="comparative_amount">340000.00</field>
        </record>

        <record id="sample_line_non_current_liabilities" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Plus Non-current Liabilities</field>
            <field name="line_type">subtotal</field>
            <field name="line_section">non_current_liabilities</field>
            <field name="sequence">140</field>
            <field name="indent_level">1</field>
            <field name="current_amount">0.00</field>
            <field name="comparative_amount">0.00</field>
        </record>

        <!-- EQUITY Section -->
        <record id="sample_line_equity_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">EQUITY</field>
            <field name="line_type">header</field>
            <field name="line_section">equity</field>
            <field name="sequence">150</field>
            <field name="bold">True</field>
            <field name="font_size">large</field>
            <field name="indent_level">0</field>
            <field name="current_amount">949542.00</field>
            <field name="comparative_amount">807000.00</field>
        </record>

        <record id="sample_line_unallocated_earnings" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Unallocated Earnings</field>
            <field name="line_type">line</field>
            <field name="line_section">equity</field>
            <field name="sequence">160</field>
            <field name="indent_level">1</field>
            <field name="current_amount">949542.00</field>
            <field name="comparative_amount">807000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="sample_line_current_year_earnings" model="financial.statement.line.analytics">
            <field name="statement_id" ref="template_balance_sheet_analytics"/>
            <field name="name">Current Year Unallocated Earnings</field>
            <field name="line_type">line</field>
            <field name="line_section">equity</field>
            <field name="sequence">170</field>
            <field name="indent_level">1</field>
            <field name="current_amount">500700.00</field>
            <field name="comparative_amount">425600.00</field>
            <field name="is_expandable">True</field>
        </record>

    </data>
</odoo>
