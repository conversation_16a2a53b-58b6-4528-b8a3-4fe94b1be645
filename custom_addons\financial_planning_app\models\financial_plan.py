# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
import logging

_logger = logging.getLogger(__name__)


class FinancialPlan(models.Model):
    _name = 'financial.planning.plan'
    _description = 'Financial Planning Plan'
    _order = 'launch_date desc, name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Plan Name', required=True)
    description = fields.Text(string='Description')
    
    # Launch and planning dates
    launch_date = fields.Date(string='Launch Date', required=True, default=fields.Date.today)
    planning_start_date = fields.Date(string='Planning Start Date', compute='_compute_planning_dates', store=True)
    planning_end_date = fields.Date(string='Planning End Date', compute='_compute_planning_dates', store=True)
    
    # Geographic scope
    continent_ids = fields.Many2many('financial.planning.continent', string='Target Continents')
    country_ids = fields.Many2many('financial.planning.country', string='Target Countries')
    city_ids = fields.Many2many('financial.planning.city', string='Target Cities')
    
    # Financial planning parameters
    base_currency_id = fields.Many2one('res.currency', string='Base Currency', required=True, 
                                      default=lambda self: self.env.company.currency_id)
    initial_investment = fields.Monetary(string='Initial Investment', currency_field='base_currency_id')
    target_revenue_year_1 = fields.Monetary(string='Target Revenue Year 1', currency_field='base_currency_id')
    target_revenue_year_5 = fields.Monetary(string='Target Revenue Year 5', currency_field='base_currency_id')
    
    # Population-based planning
    target_population_coverage = fields.Float(string='Target Population Coverage (%)', digits=(5, 2), 
                                            help='Percentage of target population to reach')
    revenue_per_capita = fields.Monetary(string='Revenue per Capita', currency_field='base_currency_id',
                                       help='Expected revenue per person in target population')
    
    # Growth assumptions
    annual_revenue_growth_rate = fields.Float(string='Annual Revenue Growth Rate (%)', digits=(5, 2), default=15.0)
    market_penetration_rate = fields.Float(string='Market Penetration Rate (%)', digits=(5, 2), default=5.0)
    customer_acquisition_cost = fields.Monetary(string='Customer Acquisition Cost', currency_field='base_currency_id')
    
    # Plan status and workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('review', 'Under Review'),
        ('approved', 'Approved'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)
    
    # Relationships
    forecast_ids = fields.One2many('financial.planning.forecast', 'plan_id', string='Forecasts')
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    
    # Computed fields
    total_target_population = fields.Float(string='Total Target Population', compute='_compute_target_population', 
                                         help='Total population in millions across all target areas')
    potential_market_size = fields.Monetary(string='Potential Market Size', compute='_compute_market_metrics', 
                                          currency_field='base_currency_id')
    forecast_count = fields.Integer(string='Number of Forecasts', compute='_compute_forecast_count')
    
    # Planning period (60 months = 5 years, -6 months for historical)
    planning_months = fields.Integer(string='Planning Months', default=60, readonly=True)
    historical_months = fields.Integer(string='Historical Months', default=6, readonly=True)
    
    # Additional fields
    risk_level = fields.Selection([
        ('very_low', 'Very Low Risk'),
        ('low', 'Low Risk'),
        ('medium', 'Medium Risk'),
        ('high', 'High Risk'),
        ('very_high', 'Very High Risk')
    ], string='Overall Risk Level', default='medium')
    
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)

    @api.depends('launch_date')
    def _compute_planning_dates(self):
        for plan in self:
            if plan.launch_date:
                # Planning starts 6 months before launch date
                plan.planning_start_date = plan.launch_date - relativedelta(months=plan.historical_months)
                # Planning ends 60 months after launch date (5 years)
                plan.planning_end_date = plan.launch_date + relativedelta(months=plan.planning_months)
            else:
                plan.planning_start_date = False
                plan.planning_end_date = False

    @api.depends('country_ids', 'city_ids')
    def _compute_target_population(self):
        for plan in self:
            total_population = 0.0
            
            # Add population from selected countries
            if plan.country_ids:
                total_population += sum(plan.country_ids.mapped('population'))
            
            # Add population from selected cities (if not already counted via countries)
            if plan.city_ids:
                # Only count cities that are not in the selected countries
                city_countries = plan.city_ids.mapped('country_id')
                cities_not_in_countries = plan.city_ids.filtered(lambda c: c.country_id not in plan.country_ids)
                total_population += sum(cities_not_in_countries.mapped('population'))
            
            plan.total_target_population = total_population

    @api.depends('total_target_population', 'revenue_per_capita', 'target_population_coverage')
    def _compute_market_metrics(self):
        for plan in self:
            if plan.total_target_population and plan.revenue_per_capita and plan.target_population_coverage:
                # Calculate potential market size based on population coverage
                covered_population = plan.total_target_population * (plan.target_population_coverage / 100)
                plan.potential_market_size = covered_population * 1000000 * plan.revenue_per_capita  # Convert millions to actual
            else:
                plan.potential_market_size = 0.0

    @api.depends('forecast_ids')
    def _compute_forecast_count(self):
        for plan in self:
            plan.forecast_count = len(plan.forecast_ids)

    def action_generate_forecasts(self):
        """Generate monthly forecasts for the planning period"""
        self.ensure_one()
        
        # Clear existing forecasts
        self.forecast_ids.unlink()
        
        forecast_vals = []
        current_date = self.planning_start_date
        
        while current_date <= self.planning_end_date:
            # Determine if this is historical, launch month, or future
            if current_date < self.launch_date:
                period_type = 'historical'
            elif current_date.year == self.launch_date.year and current_date.month == self.launch_date.month:
                period_type = 'launch'
            else:
                period_type = 'forecast'
            
            # Calculate months from launch
            months_from_launch = (current_date.year - self.launch_date.year) * 12 + (current_date.month - self.launch_date.month)
            
            vals = {
                'plan_id': self.id,
                'period_date': current_date,
                'period_type': period_type,
                'months_from_launch': months_from_launch,
            }
            forecast_vals.append(vals)
            
            # Move to next month
            current_date = current_date + relativedelta(months=1)
        
        # Create all forecasts
        forecasts = self.env['financial.planning.forecast'].create(forecast_vals)
        
        # Generate detailed forecasts for each period
        for forecast in forecasts:
            forecast.calculate_forecast_values()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Forecasts Generated'),
                'message': _('%d monthly forecasts have been generated successfully.') % len(forecasts),
                'type': 'success',
            }
        }

    def action_view_forecasts(self):
        """Action to view forecasts for this plan"""
        self.ensure_one()
        return {
            'name': _('Forecasts for %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'financial.planning.forecast',
            'view_mode': 'tree,form,graph,pivot',
            'domain': [('plan_id', '=', self.id)],
            'context': {'default_plan_id': self.id},
        }

    def action_approve(self):
        """Approve the financial plan"""
        self.ensure_one()
        self.state = 'approved'
        return True

    def action_activate(self):
        """Activate the financial plan"""
        self.ensure_one()
        if self.state != 'approved':
            raise models.UserError(_('Plan must be approved before activation.'))
        self.state = 'active'
        return True

    def get_plan_summary(self):
        """Get comprehensive plan summary"""
        self.ensure_one()
        return {
            'plan_name': self.name,
            'launch_date': self.launch_date,
            'planning_period': f"{self.planning_start_date} to {self.planning_end_date}",
            'target_countries': len(self.country_ids),
            'target_cities': len(self.city_ids),
            'total_target_population': self.total_target_population,
            'potential_market_size': self.potential_market_size,
            'initial_investment': self.initial_investment,
            'target_revenue_year_1': self.target_revenue_year_1,
            'target_revenue_year_5': self.target_revenue_year_5,
            'annual_growth_rate': self.annual_revenue_growth_rate,
            'market_penetration_rate': self.market_penetration_rate,
            'forecast_count': self.forecast_count,
            'state': self.state,
        }
