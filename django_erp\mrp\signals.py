from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.core.exceptions import ValidationError
from .models import Mrp<PERSON>roduction, Mr<PERSON><PERSON><PERSON><PERSON><PERSON>, MrpBom


@receiver(post_save, sender=MrpProduction)
def production_post_save(sender, instance, created, **kwargs):
    """Handle manufacturing order post-save operations"""
    if created:
        # Set default dates if not provided
        if not instance.date_planned_start:
            from django.utils import timezone
            instance.date_planned_start = timezone.now()
            instance.save(update_fields=['date_planned_start'])
        
        # Set default locations if not provided
        if not instance.location_src_id or not instance.location_dest_id:
            # Get default locations from company or warehouse
            # This would be implemented based on warehouse configuration
            pass


@receiver(post_save, sender=Mrp<PERSON>orkorder)
def workorder_post_save(sender, instance, created, **kwargs):
    """Handle work order post-save operations"""
    if created:
        # Set default quantities
        if instance.qty_production == 0:
            instance.qty_production = instance.production_id.product_qty
            instance.qty_remaining = instance.qty_production
            instance.save(update_fields=['qty_production', 'qty_remaining'])
        
        # Set expected duration from operation
        if instance.operation_id and instance.duration_expected == 0:
            instance.duration_expected = instance.operation_id.time_cycle
            instance.save(update_fields=['duration_expected'])


@receiver(pre_delete, sender=MrpBom)
def bom_pre_delete(sender, instance, **kwargs):
    """Prevent deletion of BOM if it's used in active manufacturing orders"""
    active_productions = MrpProduction.objects.filter(
        bom_id=instance,
        state__in=['draft', 'confirmed', 'progress']
    )
    
    if active_productions.exists():
        raise ValidationError(
            f"Cannot delete BOM {instance} because it's used in active manufacturing orders."
        )


# Integration signals with other modules

@receiver(post_save, sender='sales.SaleOrderLine')
def sale_line_post_save(sender, instance, created, **kwargs):
    """Create manufacturing orders from sales order lines if needed"""
    # This would be implemented to create MOs for make-to-order products
    pass


@receiver(post_save, sender='inventory.StockMove')
def stock_move_post_save(sender, instance, created, **kwargs):
    """Handle stock move integration with manufacturing"""
    # Update manufacturing order reservation state when stock moves change
    if hasattr(instance, 'raw_material_production_id') and instance.raw_material_production_id:
        production = instance.raw_material_production_id
        production._compute_reservation_state()
