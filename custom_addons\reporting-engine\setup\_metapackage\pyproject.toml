[project]
name = "odoo-addons-oca-reporting-engine"
version = "17.0.20250606.0"
dependencies = [
    "odoo-addon-base_comment_template>=17.0dev,<17.1dev",
    "odoo-addon-bi_sql_editor>=17.0dev,<17.1dev",
    "odoo-addon-bi_view_editor>=17.0dev,<17.1dev",
    "odoo-addon-kpi>=17.0dev,<17.1dev",
    "odoo-addon-report_async>=17.0dev,<17.1dev",
    "odoo-addon-report_csv>=17.0dev,<17.1dev",
    "odoo-addon-report_layout_config>=17.0dev,<17.1dev",
    "odoo-addon-report_py3o>=17.0dev,<17.1dev",
    "odoo-addon-report_qweb_element_page_visibility>=17.0dev,<17.1dev",
    "odoo-addon-report_qweb_parameter>=17.0dev,<17.1dev",
    "odoo-addon-report_qweb_pdf_watermark>=17.0dev,<17.1dev",
    "odoo-addon-report_qweb_signer>=17.0dev,<17.1dev",
    "odoo-addon-report_substitute>=17.0dev,<17.1dev",
    "odoo-addon-report_wkhtmltopdf_param>=17.0dev,<17.1dev",
    "odoo-addon-report_xlsx>=17.0dev,<17.1dev",
    "odoo-addon-report_xlsx_helper>=17.0dev,<17.1dev",
    "odoo-addon-report_xml>=17.0dev,<17.1dev",
    "odoo-addon-sql_export>=17.0dev,<17.1dev",
    "odoo-addon-sql_export_excel>=17.0dev,<17.1dev",
    "odoo-addon-sql_export_mail>=17.0dev,<17.1dev",
    "odoo-addon-sql_request_abstract>=17.0dev,<17.1dev",
]
classifiers=[
    "Programming Language :: Python",
    "Framework :: Odoo",
    "Framework :: Odoo :: 17.0",
]
