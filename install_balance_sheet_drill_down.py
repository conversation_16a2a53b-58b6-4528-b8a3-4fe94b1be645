#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Balance Sheet Drill-Down Module Installer
This script helps install the Balance Sheet drill-down module
"""

import os
import sys
import shutil
import subprocess

def main():
    print("🎯 Balance Sheet Drill-Down Module Installer")
    print("=" * 50)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    # Check if we're in the right directory
    if not os.path.exists('odoo-bin'):
        print("❌ Error: odoo-bin not found. Please run this from your Odoo root directory.")
        return False
    
    # Check if custom_addons directory exists
    custom_addons_dir = os.path.join(current_dir, 'custom_addons')
    if not os.path.exists(custom_addons_dir):
        print("📁 Creating custom_addons directory...")
        os.makedirs(custom_addons_dir)
    
    # Check if our module exists
    module_dir = os.path.join(custom_addons_dir, 'balance_sheet_drill_down')
    if os.path.exists(module_dir):
        print("✅ Balance Sheet Drill-Down module found!")
        print(f"Module location: {module_dir}")
        
        # List module files
        print("\n📋 Module files:")
        for root, dirs, files in os.walk(module_dir):
            level = root.replace(module_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
    else:
        print("❌ Balance Sheet Drill-Down module not found!")
        return False
    
    print("\n🔧 Installation Steps:")
    print("1. Make sure Odoo is stopped")
    print("2. Update the addons path to include custom_addons")
    print("3. Start Odoo")
    print("4. Go to Apps → Update Apps List")
    print("5. Search for 'Balance Sheet Drill Down'")
    print("6. Install the module")
    
    print("\n📝 Manual Installation Commands:")
    print("1. Stop Odoo if running")
    print("2. Run: python odoo-bin -d erp --addons-path=addons,custom_addons --update=all --stop-after-init")
    print("3. Start Odoo normally")
    print("4. Install the module from Apps menu")
    
    # Check Python version
    print(f"\n🐍 Python version: {sys.version}")
    
    # Try to find the correct Python executable
    python_executables = ['python', 'python3', 'python3.13', 'python3.12', 'python3.11', 'python3.10']
    working_python = None
    
    for py_exec in python_executables:
        try:
            result = subprocess.run([py_exec, '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"Found {py_exec}: {version}")
                if '3.1' in version:  # Python 3.10+
                    working_python = py_exec
                    break
        except (subprocess.TimeoutExpired, FileNotFoundError):
            continue
    
    if working_python:
        print(f"\n✅ Recommended Python executable: {working_python}")
        print(f"Use this command to start Odoo:")
        print(f"{working_python} odoo-bin -d erp --addons-path=addons,custom_addons")
    else:
        print("\n⚠️  Could not find Python 3.10+ executable")
        print("Please make sure Python 3.10+ is installed and accessible")
    
    return True

if __name__ == "__main__":
    main()
    input("\nPress Enter to continue...")
