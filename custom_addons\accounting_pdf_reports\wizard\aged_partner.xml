<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_aged_balance_view" model="ir.ui.view">
        <field name="name">Aged Partner Balance</field>
        <field name="model">account.aged.trial.balance</field>
        <field name="arch" type="xml">
            <form string="Report Options">
                <group col="4">
                    <field name="date_from"/>
                    <field name="period_length"/>
                    <field name="company_id" invisible="1"/>
                    <newline/>
                    <field name="result_selection" widget="radio"
                           invisible="context.get('hide_result_selection')"/>
                    <field name="target_move" widget="radio"/>
                </group>
                <field name="journal_ids" required="0" invisible="1"/>
                <xpath expr="//field[@name='journal_ids']" position="before">
                    <group>
                        <field name="partner_ids" widget="many2many_tags"
                               options="{'no_open': True, 'no_create': True}"/>
                    </group>
                </xpath>
                <footer>
                    <button name="check_report" class="oe_highlight"
                            string="Print" type="object"/>
                    <button string="Cancel" class="btn btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_account_aged_balance_view" model="ir.actions.act_window">
        <field name="name">Aged Partner Balance</field>
        <field name="res_model">account.aged.trial.balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="account_aged_balance_view"/>
        <field name="context"></field>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_aged_trial_balance"
              name="Aged Partner Balance"
              sequence="10"
              action="action_account_aged_balance_view"
              parent="menu_finance_partner_reports"/>

    <record id="action_account_aged_receivable" model="ir.actions.act_window">
        <field name="name">Aged Receivable</field>
        <field name="res_model">account.aged.trial.balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="account_aged_balance_view"/>
        <field name="context">{'default_result_selection': 'customer',
            'hide_result_selection': 1}</field>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_aged_receivable"
              name="Aged Receivable"
              sequence="20"
              action="action_account_aged_receivable"
              parent="menu_finance_partner_reports"/>


    <record id="action_account_aged_payable" model="ir.actions.act_window">
        <field name="name">Aged Payable</field>
        <field name="res_model">account.aged.trial.balance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="account_aged_balance_view"/>
        <field name="context">{'default_result_selection': 'supplier',
            'hide_result_selection': 1}</field>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_aged_payable"
              name="Aged Payable"
              sequence="30"
              action="action_account_aged_payable"
              parent="menu_finance_partner_reports"/>

</odoo>
