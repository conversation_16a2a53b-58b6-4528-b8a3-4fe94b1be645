# Generated by Django 4.2.21 on 2025-07-19 16:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='stocklocation',
            name='barcode_company_uniq',
        ),
        migrations.AlterField(
            model_name='product',
            name='barcode',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='producttemplate',
            name='barcode',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True),
        ),
        migrations.AddConstraint(
            model_name='stocklocation',
            constraint=models.UniqueConstraint(condition=models.Q(('barcode__isnull', False), models.Q(('barcode', ''), _negated=True)), fields=('barcode', 'company_id'), name='barcode_company_uniq'),
        ),
    ]
