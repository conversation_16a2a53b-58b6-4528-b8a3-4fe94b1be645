from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

# Placeholder for sales API views
# TODO: Implement full sales API

class SaleOrderViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class SaleOrderLineViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class ProductPricelistViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None
