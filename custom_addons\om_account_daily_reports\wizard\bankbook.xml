<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_report_bankbook_view" model="ir.ui.view">
        <field name="name">Bank Book</field>
        <field name="model">account.bankbook.report</field>
        <field name="arch" type="xml">
            <form string="Report Options">
                <group>
                    <group>
                        <field name="target_move" widget="radio"/>
                    </group>
                    <group>
                        <field name="sortby" widget="radio"/>
                        <field name="display_account" invisible="1"/>
                    </group>
                </group>
                <group>
                    <field name="initial_balance"/>
                </group>
                <group col="4">
                    <field name="date_from"/>
                    <field name="date_to"/>
                </group>
                <group>
                    <field name="account_ids" widget="many2many_tags" invisible="0"/>
                    <field name="journal_ids" widget="many2many_tags"/>
                </group>
                <footer>
                    <button name="check_report" string="Print" type="object" default_focus="1"
                            class="oe_highlight"/>
                    <button string="Cancel" class="btn btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_account_bankbook_menu" model="ir.actions.act_window">
        <field name="name">Bank Book</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.bankbook.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_report_bankbook_view"/>
        <field name="target">new</field>
    </record>

    <menuitem
            id="menu_bankbook"
            name="Bank Book"
            sequence="10"
            parent="om_account_daily_reports.menu_finance_daily_reports"
            action="action_account_bankbook_menu"
            groups="account.group_account_user,account.group_account_manager"
    />

</odoo>
