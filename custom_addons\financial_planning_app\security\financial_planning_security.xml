<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Financial Planning Security Groups -->
    
    <!-- Financial Planning User Group -->
    <record id="group_financial_planning_user" model="res.groups">
        <field name="name">Financial Planning User</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">Users can view and create financial plans and forecasts</field>
    </record>

    <!-- Financial Planning Manager Group -->
    <record id="group_financial_planning_manager" model="res.groups">
        <field name="name">Financial Planning Manager</field>
        <field name="implied_ids" eval="[(4, ref('group_financial_planning_user'))]"/>
        <field name="comment">Managers can approve financial plans and access all planning data</field>
    </record>

    <!-- Population Data Analyst Group -->
    <record id="group_population_data_analyst" model="res.groups">
        <field name="name">Population Data Analyst</field>
        <field name="implied_ids" eval="[(4, ref('group_financial_planning_user'))]"/>
        <field name="comment">Analysts can manage population and demographic data</field>
    </record>

    <!-- Financial Planning Administrator Group -->
    <record id="group_financial_planning_admin" model="res.groups">
        <field name="name">Financial Planning Administrator</field>
        <field name="implied_ids" eval="[(4, ref('group_financial_planning_manager')), (4, ref('group_population_data_analyst'))]"/>
        <field name="comment">Administrators have full access to all financial planning features</field>
    </record>

    <!-- Record Rules -->
    
    <!-- Financial Plan - Multi-company rule -->
    <record id="financial_plan_company_rule" model="ir.rule">
        <field name="name">Financial Plan: multi-company</field>
        <field name="model_id" ref="model_financial_planning_plan"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- Financial Forecast - Multi-company rule -->
    <record id="financial_forecast_company_rule" model="ir.rule">
        <field name="name">Financial Forecast: multi-company</field>
        <field name="model_id" ref="model_financial_planning_forecast"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- Population Data - Multi-company rule -->
    <record id="population_data_company_rule" model="ir.rule">
        <field name="name">Population Data: multi-company</field>
        <field name="model_id" ref="model_financial_planning_population_data"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- Growth Rate - Multi-company rule -->
    <record id="growth_rate_company_rule" model="ir.rule">
        <field name="name">Growth Rate: accessible to all users</field>
        <field name="model_id" ref="model_financial_planning_growth_rate"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- Continent - Accessible to all users -->
    <record id="continent_access_rule" model="ir.rule">
        <field name="name">Continent: accessible to all users</field>
        <field name="model_id" ref="model_financial_planning_continent"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- Country - Accessible to all users -->
    <record id="country_access_rule" model="ir.rule">
        <field name="name">Country: accessible to all users</field>
        <field name="model_id" ref="model_financial_planning_country"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- City - Accessible to all users -->
    <record id="city_access_rule" model="ir.rule">
        <field name="name">City: accessible to all users</field>
        <field name="model_id" ref="model_financial_planning_city"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

    <!-- Market Sizing - Multi-company rule -->
    <record id="market_sizing_company_rule" model="ir.rule">
        <field name="name">Market Sizing: multi-company</field>
        <field name="model_id" ref="model_financial_planning_market_sizing"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_financial_planning_user'))]"/>
    </record>

</odoo>
