/** @odoo-module **/

import { Component, onMounted, onWillUnmount, useState } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * Financial Analytics Dashboard Component
 */
export class FinancialAnalyticsDashboard extends Component {
    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        
        this.state = useState({
            loading: true,
            dashboardData: {},
            chartInstances: {},
            selectedPeriod: 'current',
            selectedView: 'overview'
        });

        onMounted(() => {
            this.loadDashboardData();
            this.initializeCharts();
        });

        onWillUnmount(() => {
            this.destroyCharts();
        });
    }

    /**
     * Load dashboard data from the server
     */
    async loadDashboardData() {
        try {
            this.state.loading = true;
            
            const statementId = this.props.context?.default_statement_id;
            if (!statementId) {
                this.notification.add("No financial statement selected", { type: "warning" });
                return;
            }

            // Create dashboard record
            const dashboardId = await this.orm.create("financial.analytics.dashboard", {
                statement_id: statementId,
                dashboard_type: this.state.selectedView
            });

            // Read dashboard data
            const dashboardData = await this.orm.read("financial.analytics.dashboard", [dashboardId], [
                'financial_summary_data',
                'ratio_analysis_data', 
                'trend_analysis_data',
                'chart_data',
                'total_assets',
                'total_liabilities',
                'total_equity',
                'net_income',
                'current_ratio',
                'quick_ratio',
                'debt_to_equity_ratio',
                'return_on_equity',
                'return_on_assets'
            ]);

            if (dashboardData.length > 0) {
                this.state.dashboardData = dashboardData[0];
                this.parseJsonData();
                this.renderCharts();
            }

        } catch (error) {
            console.error("Error loading dashboard data:", error);
            this.notification.add("Error loading dashboard data", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Parse JSON data from server
     */
    parseJsonData() {
        const data = this.state.dashboardData;
        
        try {
            data.financialSummary = JSON.parse(data.financial_summary_data || '{}');
            data.ratioAnalysis = JSON.parse(data.ratio_analysis_data || '[]');
            data.trendAnalysis = JSON.parse(data.trend_analysis_data || '{}');
            data.chartData = JSON.parse(data.chart_data || '{}');
        } catch (error) {
            console.error("Error parsing JSON data:", error);
        }
    }

    /**
     * Initialize chart containers
     */
    initializeCharts() {
        // Ensure Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.warn("Chart.js not loaded");
            return;
        }

        // Set default Chart.js configuration
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
        Chart.defaults.plugins.legend.position = 'bottom';
    }

    /**
     * Render all charts
     */
    renderCharts() {
        if (!this.state.dashboardData.chartData) return;

        const chartData = this.state.dashboardData.chartData;

        // Render Asset Composition Chart
        if (chartData.asset_composition) {
            this.renderAssetCompositionChart(chartData.asset_composition);
        }

        // Render Liability & Equity Chart
        if (chartData.liability_equity_pie) {
            this.renderLiabilityEquityChart(chartData.liability_equity_pie);
        }

        // Render Ratio Radar Chart
        if (chartData.ratio_radar) {
            this.renderRatioRadarChart(chartData.ratio_radar);
        }

        // Render Trend Line Chart
        if (chartData.trend_line) {
            this.renderTrendLineChart(chartData.trend_line);
        }

        // Render Comparative Bar Chart
        if (chartData.comparative_bar) {
            this.renderComparativeBarChart(chartData.comparative_bar);
        }
    }

    /**
     * Render Asset Composition Chart
     */
    renderAssetCompositionChart(chartConfig) {
        const ctx = document.getElementById('asset_composition_chart');
        if (!ctx) return;

        if (this.state.chartInstances.assetComposition) {
            this.state.chartInstances.assetComposition.destroy();
        }

        this.state.chartInstances.assetComposition = new Chart(ctx, {
            type: 'doughnut',
            data: chartConfig.data,
            options: {
                ...chartConfig.options,
                plugins: {
                    ...chartConfig.options.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${percentage}%`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Render Liability & Equity Chart
     */
    renderLiabilityEquityChart(chartConfig) {
        const ctx = document.getElementById('capital_structure_chart');
        if (!ctx) return;

        if (this.state.chartInstances.capitalStructure) {
            this.state.chartInstances.capitalStructure.destroy();
        }

        this.state.chartInstances.capitalStructure = new Chart(ctx, {
            type: 'pie',
            data: chartConfig.data,
            options: {
                ...chartConfig.options,
                plugins: {
                    ...chartConfig.options.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${percentage}%`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Render Ratio Radar Chart
     */
    renderRatioRadarChart(chartConfig) {
        const ctx = document.getElementById('ratio_radar_chart');
        if (!ctx) return;

        if (this.state.chartInstances.ratioRadar) {
            this.state.chartInstances.ratioRadar.destroy();
        }

        this.state.chartInstances.ratioRadar = new Chart(ctx, chartConfig);
    }

    /**
     * Render Trend Line Chart
     */
    renderTrendLineChart(chartConfig) {
        const ctx = document.getElementById('trend_line_chart');
        if (!ctx) return;

        if (this.state.chartInstances.trendLine) {
            this.state.chartInstances.trendLine.destroy();
        }

        this.state.chartInstances.trendLine = new Chart(ctx, chartConfig);
    }

    /**
     * Render Comparative Bar Chart
     */
    renderComparativeBarChart(chartConfig) {
        const ctx = document.getElementById('comparative_bar_chart');
        if (!ctx) return;

        if (this.state.chartInstances.comparativeBar) {
            this.state.chartInstances.comparativeBar.destroy();
        }

        this.state.chartInstances.comparativeBar = new Chart(ctx, chartConfig);
    }

    /**
     * Destroy all chart instances
     */
    destroyCharts() {
        Object.values(this.state.chartInstances).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.state.chartInstances = {};
    }

    /**
     * Refresh dashboard data
     */
    async refreshDashboard() {
        await this.loadDashboardData();
        this.notification.add("Dashboard refreshed successfully", { type: "success" });
    }

    /**
     * Change dashboard view
     */
    async changeDashboardView(viewType) {
        this.state.selectedView = viewType;
        await this.loadDashboardData();
    }

    /**
     * Export dashboard to PDF
     */
    async exportDashboard() {
        try {
            // Implementation for PDF export
            this.notification.add("Dashboard export started", { type: "info" });
            
            // Call server method to generate PDF
            const statementId = this.props.context?.default_statement_id;
            if (statementId) {
                const action = await this.orm.call(
                    "financial.analytics.dashboard",
                    "action_export_dashboard",
                    [[statementId]]
                );
                
                if (action) {
                    // Handle the export action
                    this.env.services.action.doAction(action);
                }
            }
        } catch (error) {
            console.error("Error exporting dashboard:", error);
            this.notification.add("Error exporting dashboard", { type: "danger" });
        }
    }

    /**
     * Format currency values
     */
    formatCurrency(value) {
        if (typeof value !== 'number') return value;
        
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    }

    /**
     * Format percentage values
     */
    formatPercentage(value) {
        if (typeof value !== 'number') return value;
        
        return `${value.toFixed(2)}%`;
    }

    /**
     * Get trend indicator class
     */
    getTrendClass(trend) {
        const trendClasses = {
            'improving': 'trend-indicator improving',
            'stable': 'trend-indicator stable',
            'declining': 'trend-indicator declining'
        };
        
        return trendClasses[trend] || 'trend-indicator stable';
    }

    /**
     * Get interpretation badge class
     */
    getInterpretationClass(interpretation) {
        const interpretationClasses = {
            'excellent': 'ratio-interpretation excellent',
            'good': 'ratio-interpretation good',
            'average': 'ratio-interpretation average',
            'needs_attention': 'ratio-interpretation needs_attention',
            'poor': 'ratio-interpretation poor'
        };
        
        return interpretationClasses[interpretation] || 'ratio-interpretation average';
    }
}

FinancialAnalyticsDashboard.template = "financial_statements_analytics.FinancialAnalyticsDashboard";

// Register the component
registry.category("actions").add("financial_analytics_dashboard", FinancialAnalyticsDashboard);
