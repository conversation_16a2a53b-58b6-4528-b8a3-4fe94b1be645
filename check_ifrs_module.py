#!/usr/bin/env python3
"""
Quick diagnostic script to check IFRS module status
Run this from Odoo shell: python odoo-bin shell --config=odoo.conf
"""

def check_ifrs_module():
    """Check IFRS module installation and setup"""
    
    print("🔍 IFRS Module Diagnostic Report")
    print("=" * 50)
    
    try:
        # Check if module is installed
        module = env['ir.module.module'].search([('name', '=', 'ifrs_financial_statements')])
        if module:
            print(f"✅ Module Status: {module.state}")
            print(f"✅ Module Version: {module.installed_version}")
        else:
            print("❌ Module not found in database")
            return
        
        # Check if models exist
        models_to_check = [
            'ifrs.financial.statement',
            'ifrs.statement.line', 
            'ifrs.compliance.check',
            'ifrs.report.template'
        ]
        
        print("\n📊 Model Status:")
        for model_name in models_to_check:
            try:
                model = env[model_name]
                count = model.search_count([])
                print(f"✅ {model_name}: {count} records")
            except Exception as e:
                print(f"❌ {model_name}: Error - {e}")
        
        # Check menu items
        print("\n📋 Menu Status:")
        menus = env['ir.ui.menu'].search([('name', 'ilike', 'IFRS')])
        for menu in menus:
            print(f"✅ Menu: {menu.name} (ID: {menu.id})")
        
        # Check user permissions
        print("\n👤 User Permissions:")
        admin_user = env['res.users'].search([('login', '=', 'admin')], limit=1)
        if admin_user:
            groups = admin_user.groups_id.filtered(lambda g: 'ifrs' in g.name.lower())
            for group in groups:
                print(f"✅ Group: {group.name}")
            if not groups:
                print("⚠️  No IFRS groups assigned to admin user")
        
        # Check if there's any sample data
        print("\n💰 Sample Data:")
        statements = env['ifrs.financial.statement'].search([])
        print(f"✅ Financial Statements: {len(statements)} records")
        for stmt in statements[:3]:  # Show first 3
            print(f"   - {stmt.name} ({stmt.state})")
        
        print("\n🎯 Quick Access URLs:")
        print("Main Menu: http://localhost:8069/web#menu_id=menu_ifrs_main")
        print("Statements: http://localhost:8069/web#action=action_ifrs_financial_statement")
        
    except Exception as e:
        print(f"❌ Error during diagnostic: {e}")

if __name__ == "__main__":
    # This would run if executed in Odoo shell
    check_ifrs_module()
