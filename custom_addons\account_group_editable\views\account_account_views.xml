<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_form_inherit_group_editable" model="ir.ui.view">
        <field name="name">account.account.form.inherit.group.editable</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <field name="group_id" position="replace">
                <field name="group_id" readonly="0" force_save="1" options="{'no_create': False, 'no_open': False}"/>
            </field>
        </field>
    </record>

    <record id="view_account_list_inherit_group_editable" model="ir.ui.view">
        <field name="name">account.account.list.inherit.group.editable</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_list"/>
        <field name="arch" type="xml">
            <field name="group_id" position="replace">
                <field name="group_id" readonly="0" optional="show"/>
            </field>
        </field>
    </record>

    <!-- Also override the tree view if it exists -->
    <record id="view_account_tree_inherit_group_editable" model="ir.ui.view">
        <field name="name">account.account.tree.inherit.group.editable</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_tree"/>
        <field name="arch" type="xml">
            <field name="group_id" position="attributes">
                <attribute name="readonly">0</attribute>
            </field>
        </field>
    </record>
</odoo>
