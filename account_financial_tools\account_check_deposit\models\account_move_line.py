# Copyright 2012-2022 Akretion France (http://www.akretion.com/)
# @author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
# @author: <PERSON><PERSON><PERSON> <<EMAIL>>
# @author: <PERSON> <<EMAIL>>
# @author: <PERSON><PERSON><PERSON> MIMOUNE <<EMAIL>>
# Copyright 2018-2022 Tecnativa - Pedro M. <PERSON>eza
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).

from odoo import fields, models


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    check_deposit_id = fields.Many2one(
        comodel_name="account.check.deposit",
        string="Check Deposit",
        copy=False,
        check_company=True,
    )
