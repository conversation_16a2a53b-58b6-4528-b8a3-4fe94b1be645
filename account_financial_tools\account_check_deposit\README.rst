=====================
Account Check Deposit
=====================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:0aadd5fd211945d77d1ca2c40a4fb85c95b0248fb463f6a187449f0afc0d1d2e
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--financial--tools-lightgray.png?logo=github
    :target: https://github.com/OCA/account-financial-tools/tree/17.0/account_check_deposit
    :alt: OCA/account-financial-tools
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-financial-tools-17-0/account-financial-tools-17-0-account_check_deposit
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-financial-tools&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows you to easily manage check deposits: you can select
all the checks you received and create a global deposit for the selected
checks. This module supports multi-currency ; each deposit has a
currency and all the checks of the deposit must have the same currency
(so, if you have checks in EUR and checks in USD, you must create 2
deposits: one in EUR and one in USD).

**Table of contents**

.. contents::
   :local:

Configuration
=============

In the menu *Invoicing > Configuration > Accounting > Journals*, create
a new journal:

-  Name: Checks Received
-  Type: Bank
-  Short Code: CHK (or any code you want)
-  in the tab *Incoming Payments*, add a line with *Payment Method* =
   *Manual* and *Outstanding receipts account* set to the account for
   the checks in hand.

Note that, on this *Checks Received* journal, the bank account and
suspense account will not be used, so don't worry about these
parameters. The field *Account number* must be empty.

This bank journal will be available as a payment method in Odoo. The
account you configured as *Outstanding Receipts Account* is the account
via which the amounts of checks will transit between the reception of a
check from a customer and the validation of the check deposit in Odoo.

When you validate the check deposit in Odoo, it will generate a new
journal entry that will move the amounts from the *Outstanding Receipts
Account* of the checks received journal to the *Outstanding Receipts
Account* of the bank journal related to the check deposit. It will also
reconcile in the *Outstanding Receipts Account* of the checks received
journal.

Usage
=====

When you receive a check that pays a customer invoice, you can go to
that invoice and click on the button *Register Payment* and select the
*Checks Received* journal as *Journal*.

When you want to deposit checks to the bank, go to the menu *Invoicing >
Customers > Checks Deposits*, create a new check deposit and set the
journal *Checks Received* and select the bank account on which you want
to credit the checks.

Then click on the button *Get All Received Checks* if you want to
deposit all the waiting received checks, or select the checks one by one
by clicking on *Add a line*.

Eventually, validate the deposit and print the report (you probably want
to customize this report).

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-financial-tools/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-financial-tools/issues/new?body=module:%20account_check_deposit%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Akretion
* Tecnativa

Contributors
------------

-  `Akretion <https://www.akretion.com>`__:

   -  Benoît GUILLOT <<EMAIL>>
   -  Chafique DELLI <<EMAIL>>
   -  Alexis de Lattre <<EMAIL>>
   -  Mourad EL HADJ MIMOUNE <<EMAIL>>

-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Pedro M. Baeza

-  `Ecosoft <http://ecosoft.co.th>`__:

   -  Pimolnat Suntian <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-financial-tools <https://github.com/OCA/account-financial-tools/tree/17.0/account_check_deposit>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
