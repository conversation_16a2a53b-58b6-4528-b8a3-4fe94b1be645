-- PostgreSQL Database Setup Script for Django ERP
-- Run this script as postgres superuser

-- Create database
DROP DATABASE IF EXISTS django_erp;
CREATE DATABASE django_erp
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_United States.1252'
    LC_CTYPE = 'English_United States.1252'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create user
DROP USER IF EXISTS "ERPUser";
CREATE USER "ERPUser" WITH
    LOGIN
    NOSUPERUSER
    CREATEDB
    NOCREATEROLE
    INHERIT
    NOREPLICATION
    CONNECTION LIMIT -1
    PASSWORD 'ERPUser';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE django_erp TO "ERPUser";

-- Connect to the new database
\c django_erp;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO "ERPUser";
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "ERPUser";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "ERPUser";

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO "ERPUser";
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO "ERPUser";

-- Display success message
SELECT 'Database django_erp and user ERPUser created successfully!' as status;
