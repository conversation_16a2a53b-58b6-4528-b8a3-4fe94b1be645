# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date
from dateutil.relativedelta import relativedelta


class GrowthRate(models.Model):
    _name = 'financial.planning.growth.rate'
    _description = 'Growth Rate Tracking'
    _order = 'country_id, year desc, month desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    country_id = fields.Many2one('financial.planning.country', string='Country', required=True)
    year = fields.Integer(string='Year', required=True, default=lambda self: date.today().year)
    month = fields.Selection([
        ('01', 'January'), ('02', 'February'), ('03', 'March'),
        ('04', 'April'), ('05', 'May'), ('06', 'June'),
        ('07', 'July'), ('08', 'August'), ('09', 'September'),
        ('10', 'October'), ('11', 'November'), ('12', 'December')
    ], string='Month', required=True, default=lambda self: str(date.today().month).zfill(2))
    
    # Growth rate types
    population_growth_rate = fields.Float(string='Population Growth Rate (%)', digits=(5, 2))
    gdp_growth_rate = fields.Float(string='GDP Growth Rate (%)', digits=(5, 2))
    inflation_rate = fields.Float(string='Inflation Rate (%)', digits=(5, 2))
    unemployment_rate = fields.Float(string='Unemployment Rate (%)', digits=(5, 2))
    
    # Economic indicators
    gdp_value = fields.Monetary(string='GDP Value', currency_field='currency_id', help='GDP value for this period')
    population_value = fields.Float(string='Population Value', help='Population in millions for this period')
    
    # Data source and quality
    data_source = fields.Selection([
        ('official', 'Official Government Data'),
        ('world_bank', 'World Bank'),
        ('imf', 'International Monetary Fund'),
        ('oecd', 'OECD'),
        ('estimated', 'Estimated'),
        ('projected', 'Projected')
    ], string='Data Source', default='official')
    
    data_quality = fields.Selection([
        ('high', 'High Quality'),
        ('medium', 'Medium Quality'),
        ('low', 'Low Quality'),
        ('estimated', 'Estimated')
    ], string='Data Quality', default='high')
    
    # Computed fields
    period_name = fields.Char(string='Period', compute='_compute_period_name', store=True)
    currency_id = fields.Many2one(related='country_id.currency_id', string='Currency', store=True)
    
    # Additional metrics
    export_growth_rate = fields.Float(string='Export Growth Rate (%)', digits=(5, 2))
    import_growth_rate = fields.Float(string='Import Growth Rate (%)', digits=(5, 2))
    foreign_investment_growth = fields.Float(string='FDI Growth Rate (%)', digits=(5, 2))
    
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)

    @api.depends('country_id', 'year', 'month')
    def _compute_name(self):
        for record in self:
            if record.country_id and record.year and record.month:
                month_name = dict(record._fields['month'].selection)[record.month]
                record.name = f"{record.country_id.name} - {month_name} {record.year}"
            else:
                record.name = "New Growth Rate Record"

    @api.depends('year', 'month')
    def _compute_period_name(self):
        for record in self:
            if record.year and record.month:
                month_name = dict(record._fields['month'].selection)[record.month]
                record.period_name = f"{month_name} {record.year}"
            else:
                record.period_name = ""

    @api.constrains('year')
    def _check_year(self):
        current_year = date.today().year
        for record in self:
            if record.year < 1900 or record.year > current_year + 10:
                raise models.ValidationError(_('Year must be between 1900 and %s') % (current_year + 10))

    def name_get(self):
        result = []
        for record in self:
            name = f"{record.country_id.name} - {record.period_name}"
            result.append((record.id, name))
        return result

    @api.model
    def get_latest_growth_rates(self, country_id, months=12):
        """Get latest growth rates for a country"""
        return self.search([
            ('country_id', '=', country_id)
        ], order='year desc, month desc', limit=months)

    @api.model
    def get_average_growth_rate(self, country_id, rate_type='gdp_growth_rate', months=12):
        """Calculate average growth rate for a country over specified months"""
        records = self.get_latest_growth_rates(country_id, months)
        if not records:
            return 0.0
        
        rates = records.mapped(rate_type)
        valid_rates = [rate for rate in rates if rate is not False and rate != 0]
        
        if not valid_rates:
            return 0.0
        
        return sum(valid_rates) / len(valid_rates)

    @api.model
    def create_projected_rates(self, country_id, months_ahead=60):
        """Create projected growth rates for future months"""
        country = self.env['financial.planning.country'].browse(country_id)
        if not country.exists():
            return False
        
        # Get historical average rates
        avg_population_growth = self.get_average_growth_rate(country_id, 'population_growth_rate', 24)
        avg_gdp_growth = self.get_average_growth_rate(country_id, 'gdp_growth_rate', 24)
        avg_inflation = self.get_average_growth_rate(country_id, 'inflation_rate', 24)
        
        # Use country defaults if no historical data
        if avg_population_growth == 0:
            avg_population_growth = country.annual_population_growth_rate
        if avg_gdp_growth == 0:
            avg_gdp_growth = country.annual_gdp_growth_rate
        if avg_inflation == 0:
            avg_inflation = country.inflation_rate or 2.0
        
        # Create future projections
        current_date = date.today()
        projected_records = []
        
        for i in range(1, months_ahead + 1):
            future_date = current_date + relativedelta(months=i)
            
            # Check if record already exists
            existing = self.search([
                ('country_id', '=', country_id),
                ('year', '=', future_date.year),
                ('month', '=', str(future_date.month).zfill(2))
            ])
            
            if not existing:
                vals = {
                    'country_id': country_id,
                    'year': future_date.year,
                    'month': str(future_date.month).zfill(2),
                    'population_growth_rate': avg_population_growth,
                    'gdp_growth_rate': avg_gdp_growth,
                    'inflation_rate': avg_inflation,
                    'data_source': 'projected',
                    'data_quality': 'estimated',
                }
                projected_records.append(vals)
        
        if projected_records:
            return self.create(projected_records)
        return self.browse()

    def get_growth_trend_analysis(self):
        """Analyze growth trends for this record's country"""
        self.ensure_one()
        
        # Get historical data for trend analysis
        historical_records = self.search([
            ('country_id', '=', self.country_id.id),
            ('year', '<=', self.year),
            ('data_source', '!=', 'projected')
        ], order='year desc, month desc', limit=24)
        
        if len(historical_records) < 2:
            return {'trend': 'insufficient_data', 'message': 'Not enough historical data for trend analysis'}
        
        # Analyze GDP growth trend
        gdp_rates = [r.gdp_growth_rate for r in historical_records if r.gdp_growth_rate]
        if len(gdp_rates) >= 2:
            recent_avg = sum(gdp_rates[:6]) / len(gdp_rates[:6]) if len(gdp_rates) >= 6 else gdp_rates[0]
            older_avg = sum(gdp_rates[6:12]) / len(gdp_rates[6:12]) if len(gdp_rates) >= 12 else sum(gdp_rates) / len(gdp_rates)
            
            if recent_avg > older_avg * 1.1:
                gdp_trend = 'improving'
            elif recent_avg < older_avg * 0.9:
                gdp_trend = 'declining'
            else:
                gdp_trend = 'stable'
        else:
            gdp_trend = 'insufficient_data'
        
        return {
            'country': self.country_id.name,
            'period': self.period_name,
            'gdp_trend': gdp_trend,
            'current_gdp_rate': self.gdp_growth_rate,
            'historical_records_count': len(historical_records),
        }
