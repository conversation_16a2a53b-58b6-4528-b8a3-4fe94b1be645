#!/usr/bin/env python
"""
API Test Script
===============

This script tests the Django REST API setup and demonstrates the API functionality.
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken


def test_api_setup():
    """Test basic API setup"""
    print("🚀 Testing Django ERP API Setup")
    print("=" * 50)
    
    # Create test client
    client = APIClient()
    
    # Test 1: Check if API endpoints are accessible
    print("\n📋 Test 1: API Endpoint Accessibility")
    print("-" * 30)
    
    try:
        # Test core endpoints
        response = client.get('/api/v1/companies/')
        print(f"✅ Companies endpoint: {response.status_code}")
        
        response = client.get('/api/v1/partners/')
        print(f"✅ Partners endpoint: {response.status_code}")
        
        response = client.get('/api/v1/currencies/')
        print(f"✅ Currencies endpoint: {response.status_code}")
        
        response = client.get('/api/v1/countries/')
        print(f"✅ Countries endpoint: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")
    
    # Test 2: Authentication
    print("\n🔐 Test 2: Authentication System")
    print("-" * 30)
    
    try:
        # Create test user
        user, created = User.objects.get_or_create(
            username='api_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'API',
                'last_name': 'Test'
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            print("✅ Test user created")
        else:
            print("✅ Test user already exists")
        
        # Test JWT token generation
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        print(f"✅ JWT tokens generated successfully")
        print(f"   Access token length: {len(access_token)}")
        
        # Test authenticated request
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = client.get('/api/v1/users/me/')
        
        if response.status_code == 200:
            print("✅ Authenticated API request successful")
            user_data = response.json()
            print(f"   User: {user_data.get('username', 'N/A')}")
        else:
            print(f"❌ Authenticated request failed: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
    
    # Test 3: API Documentation
    print("\n📚 Test 3: API Documentation")
    print("-" * 30)
    
    try:
        # Test schema endpoint
        response = client.get('/api/schema/')
        if response.status_code == 200:
            print("✅ API schema generation successful")
        else:
            print(f"❌ API schema failed: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Error testing documentation: {e}")
    
    # Test 4: Core API Functionality
    print("\n🏢 Test 4: Core API Functionality")
    print("-" * 30)
    
    try:
        # Test creating a company
        company_data = {
            'name': 'Test API Company',
            'email': '<EMAIL>',
            'phone': '******-0123'
        }
        
        response = client.post('/api/v1/companies/', company_data, format='json')
        if response.status_code in [200, 201]:
            print("✅ Company creation successful")
            company = response.json()
            print(f"   Company ID: {company.get('id', 'N/A')}")
            print(f"   Company Name: {company.get('name', 'N/A')}")
        else:
            print(f"❌ Company creation failed: {response.status_code}")
            if hasattr(response, 'json'):
                print(f"   Error: {response.json()}")
        
        # Test listing companies
        response = client.get('/api/v1/companies/')
        if response.status_code == 200:
            companies = response.json()
            if isinstance(companies, dict) and 'results' in companies:
                count = companies.get('count', 0)
                print(f"✅ Company listing successful: {count} companies found")
            else:
                print(f"✅ Company listing successful: {len(companies)} companies found")
        else:
            print(f"❌ Company listing failed: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Error testing core functionality: {e}")
    
    print("\n🎉 API Testing Complete!")
    print("=" * 50)
    print("The Django ERP API is ready for development!")
    print("\nNext steps:")
    print("• Implement full CRUD operations for all modules")
    print("• Add business logic validation")
    print("• Create comprehensive API documentation")
    print("• Add API rate limiting and security features")


if __name__ == "__main__":
    test_api_setup()
