from django.contrib import admin
from .models import (
    Hr<PERSON><PERSON>artment, HrJob, HrContractType, HrEmployee, 
    HrContract, HrAttendance, HrLeaveType, HrLeave
)


@admin.register(HrDepartment)
class HrDepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'manager_id', 'parent_id', 'company_id', 'active']
    list_filter = ['active', 'company_id', 'parent_id']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'company_id']
        }),
        ('Hierarchy', {
            'fields': ['parent_id', 'manager_id']
        }),
        ('Additional Information', {
            'fields': ['note', 'color']
        }),
    ]


@admin.register(HrJob)
class HrJobAdmin(admin.ModelAdmin):
    list_display = ['name', 'department_id', 'company_id', 'state', 'no_of_recruitment', 'no_of_hired_employee']
    list_filter = ['state', 'department_id', 'company_id', 'active']
    search_fields = ['name', 'description']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'department_id', 'company_id']
        }),
        ('Recruitment', {
            'fields': ['state', 'no_of_recruitment', 'no_of_hired_employee']
        }),
        ('Job Details', {
            'fields': ['description', 'requirements']
        }),
        ('Contract', {
            'fields': ['contract_type_id']
        }),
    ]


@admin.register(HrContractType)
class HrContractTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'sequence']
    ordering = ['sequence', 'name']


@admin.register(HrEmployee)
class HrEmployeeAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'employee_number', 'department_id', 'job_id', 
        'parent_id', 'company_id', 'active'
    ]
    list_filter = [
        'active', 'company_id', 'department_id', 'job_id', 
        'employee_type', 'gender', 'marital'
    ]
    search_fields = ['name', 'employee_number', 'work_email', 'private_email']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'user_id', 'employee_number']
        }),
        ('Personal Information', {
            'fields': [
                'private_name', 'gender', 'marital', 'birthday',
                'identification_id', 'passport_id', 'ssnid', 'sinid'
            ]
        }),
        ('Contact Information', {
            'fields': [
                'work_email', 'work_phone', 'private_email', 
                'phone', 'mobile_phone'
            ]
        }),
        ('Work Information', {
            'fields': [
                'company_id', 'department_id', 'job_id', 
                'parent_id', 'coach_id', 'employee_type', 'work_location'
            ]
        }),
        ('Private Address', {
            'fields': [
                'private_street', 'private_street2', 'private_city',
                'private_state_id', 'private_zip', 'private_country_id'
            ]
        }),
        ('Emergency Contact', {
            'fields': ['emergency_contact', 'emergency_phone']
        }),
        ('Additional Information', {
            'fields': ['bank_account_id', 'notes', 'color']
        }),
    ]


@admin.register(HrContract)
class HrContractAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'employee_id', 'job_id', 'department_id', 
        'date_start', 'date_end', 'state', 'wage'
    ]
    list_filter = ['state', 'company_id', 'department_id', 'type_id']
    search_fields = ['name', 'employee_id__name']
    date_hierarchy = 'date_start'
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'employee_id', 'company_id']
        }),
        ('Contract Details', {
            'fields': ['type_id', 'job_id', 'department_id']
        }),
        ('Dates', {
            'fields': ['date_start', 'date_end', 'trial_date_end']
        }),
        ('State', {
            'fields': ['state']
        }),
        ('Salary', {
            'fields': ['wage', 'currency_id']
        }),
        ('Working Time', {
            'fields': ['resource_calendar_id']
        }),
        ('Notes', {
            'fields': ['notes']
        }),
    ]
    
    actions = ['action_start_contracts', 'action_close_contracts']
    
    def action_start_contracts(self, request, queryset):
        for contract in queryset.filter(state='draft'):
            contract.action_start()
        self.message_user(request, f"Started {queryset.count()} contracts.")
    action_start_contracts.short_description = "Start selected contracts"
    
    def action_close_contracts(self, request, queryset):
        for contract in queryset.filter(state='open'):
            contract.action_close()
        self.message_user(request, f"Closed {queryset.count()} contracts.")
    action_close_contracts.short_description = "Close selected contracts"


@admin.register(HrAttendance)
class HrAttendanceAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'check_in', 'check_out', 'worked_hours']
    list_filter = ['employee_id', 'check_in']
    search_fields = ['employee_id__name']
    date_hierarchy = 'check_in'
    readonly_fields = ['worked_hours']
    
    fieldsets = [
        ('Employee', {
            'fields': ['employee_id']
        }),
        ('Time', {
            'fields': ['check_in', 'check_out', 'worked_hours']
        }),
    ]


@admin.register(HrLeaveType)
class HrLeaveTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'allocation_type', 'validation_type', 'max_leaves', 'company_id']
    list_filter = ['allocation_type', 'validation_type', 'company_id', 'active']
    search_fields = ['name']
    ordering = ['sequence', 'name']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'sequence', 'company_id']
        }),
        ('Configuration', {
            'fields': ['allocation_type', 'request_unit', 'validation_type']
        }),
        ('Limits', {
            'fields': ['max_leaves', 'leaves_taken']
        }),
        ('Display', {
            'fields': ['color_name', 'color']
        }),
    ]


@admin.register(HrLeave)
class HrLeaveAdmin(admin.ModelAdmin):
    list_display = [
        'employee_id', 'holiday_status_id', 'request_date_from', 
        'request_date_to', 'number_of_days', 'state'
    ]
    list_filter = ['state', 'holiday_status_id', 'company_id', 'request_date_from']
    search_fields = ['employee_id__name', 'name']
    date_hierarchy = 'request_date_from'
    readonly_fields = ['number_of_days', 'duration_display']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'employee_id', 'holiday_status_id']
        }),
        ('Dates', {
            'fields': [
                'request_date_from', 'request_date_to',
                'request_hour_from', 'request_hour_to'
            ]
        }),
        ('Duration', {
            'fields': [
                'request_unit_hours', 'request_unit_half', 'request_unit_custom',
                'number_of_days', 'duration_display'
            ]
        }),
        ('Approval', {
            'fields': ['state', 'manager_id']
        }),
        ('Additional Information', {
            'fields': ['company_id', 'notes']
        }),
    ]
    
    actions = ['action_approve_leaves', 'action_refuse_leaves']
    
    def action_approve_leaves(self, request, queryset):
        for leave in queryset:
            if leave.state in ['confirm', 'validate1']:
                leave.action_approve()
        self.message_user(request, f"Approved {queryset.count()} leave requests.")
    action_approve_leaves.short_description = "Approve selected leave requests"
    
    def action_refuse_leaves(self, request, queryset):
        for leave in queryset:
            if leave.state not in ['refuse', 'cancel']:
                leave.action_refuse()
        self.message_user(request, f"Refused {queryset.count()} leave requests.")
    action_refuse_leaves.short_description = "Refuse selected leave requests"
