# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * om_hr_payroll
# 
# Translators:
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-11-30 13:11+0000\n"
"PO-Revision-Date: 2018-11-30 13:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:41
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When user cancel payslip the status is 'Rejected'."
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Address</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Authorized signature</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Bank Account</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contribution_register
msgid "<strong>Date From:</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Date From</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
msgid "<strong>Date To:</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Date To</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Designation</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Email</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Identification No</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Name</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "<strong>Reference</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
msgid "<strong>Register Name:</strong>"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
msgid "<strong>Total</strong>"
msgstr "<strong>Totali</strong>"

#. module: om_hr_payroll
#: model_terms:ir.actions.act_window,help:om_hr_payroll.action_contribution_register_form
msgid ""
"A contribution register is a third party involved in the salary\n"
"            payment of the employees. It can be the social security, the\n"
"            estate or anyone that collect or inject money on payslips."
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Accounting"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Accounting Information"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_active
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_active
msgid "Active"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_contract_advantage_template_view_form
msgid "Advantage Name"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.act_children_salary_rules
msgid "All Children Rules"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip.line,condition_select:0
#: selection:hr.salary.rule,condition_select:0
msgid "Always True"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_amount
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_amount
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Amount"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_amount_select
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_amount_select
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
msgid "Amount Type"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Annually"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_appears_on_payslip
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_appears_on_payslip
msgid "Appears on Payslip"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_condition_python
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_res_config_settings_module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Bi-monthly"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Bi-weekly"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_payslip_lines_contribution_register
msgid "Cancel"
msgstr "Anullo"

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Cancel Payslip"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:98
#, python-format
msgid "Cannot cancel a payslip that is done."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_category_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_id_7804
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Child Rules"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_child_ids
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_child_ids
msgid "Child Salary Rule"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_children_ids
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_children_ids
msgid "Children"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Children Definition"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.actions.act_window,help:om_hr_payroll.action_contribution_register_form
msgid "Click to add a new contribution register."
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip.run,state:0
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_form
msgid "Close"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_code
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Code"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payroll_structure_view_kanban
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "Companies"
msgstr "Kompanitë"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_company_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_company_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_company_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_company_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_company_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_company_id
msgid "Company"
msgstr "Kompani"

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_condition_select
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_condition_select
msgid "Condition Based on"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr ""

#. module: om_hr_payroll
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_payroll_configuration
msgid "Configuration"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_contract_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_contract_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_contract_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_contract_id
msgid "Contract"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.hr_contract_advantage_template_action
#: model:ir.ui.menu,name:om_hr_payroll.hr_contract_advantage_template_menu_action
msgid "Contract Advantage Templates"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_contribution_register_form
msgid "Contribution"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_contribution_register
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_register_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_register_id
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_payslip_lines_contribution_register
msgid "Contribution Register's Payslip Lines"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_contribution_register_form
#: model:ir.ui.menu,name:om_hr_payroll.menu_action_hr_contribution_register_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_contribution_register_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_contribution_register_tree
msgid "Contribution Registers"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_create_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_create_uid
msgid "Created by"
msgstr "Krijuar nga"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_create_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_create_date
msgid "Created on"
msgstr "Krijuar me"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_credit_note
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_credit_note
msgid "Credit Note"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_date_from
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_date_start
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_date_from
msgid "Date From"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_date_to
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_date_end
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_date_to
msgid "Date To"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_default_value
msgid "Default value for this advantage"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_contract_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, accordingly to "
"the contract chosen. If you let empty the field contract, this field isn't "
"mandatory anymore and thus the rules applied will be all the rules set on "
"the structure of all contracts of the employee valid for the chosen period"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_note
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_note
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_note
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_note
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_note
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_contribution_register_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "Përshkrimi"

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Details By Salary Rule Category"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_details_by_salary_rule_category
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Details by Salary Rule Category"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_report_hr_payroll_report_contributionregister_display_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_report_hr_payroll_report_payslip_details_display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: om_hr_payroll
#: selection:hr.payslip,state:0
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip,state:0 selection:hr.payslip.run,state:0
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "Draft"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employee_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_employee_id
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_view_hr_payslip_form
#: model:ir.ui.menu,name:om_hr_payroll.menu_department_tree
msgid "Employee Payslips"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_contract_advantage_template
msgid "Employee's Advantage on Contract"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_employee_ids
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:36
#, python-format
msgid "Error ! You cannot create a recursive Salary Structure."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_register_id
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_register_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip.line,amount_select:0
#: selection:hr.salary.rule,amount_select:0
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_amount_fix
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_amount_fix
msgid "Fixed Amount"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_amount_percentage
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/report/report_contribution_register.py:34
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_res_config_settings_module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "General"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_form
msgid "Generate Payslips"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Grupo Nga"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_report_hr_payroll_report_contributionregister_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_report_hr_payroll_report_payslip_details_id
msgid "ID"
msgstr "ID"

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_run_credit_note
msgid ""
"If its checked, indicates that all payslips generated from here are refund "
"payslips."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_active
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_res_config_settings_module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_credit_note
msgid "Indicates this payslip has a refund of another"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_input_ids
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_input_ids
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
msgid "Inputs"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_note
msgid "Internal Note"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_view_kanban
msgid "Is a Blocking Reason?"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_quantity
msgid ""
"It is used in computation for percentage and fixed amount. For e.g. A rule "
"for Meal Voucher having fixed amount of 1€ per worked day can have its "
"quantity defined in expression like worked_days.WORK100.number_of_days."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_input_amount
msgid ""
"It is used in computation. For e.g. A rule for sales having 1% commission of"
" basic salary for per product can defined in expression like result = "
"inputs.SALEURO.amount * contract.wage*0.01."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_report_hr_payroll_report_contributionregister___last_update
#: model:ir.model.fields,field_description:om_hr_payroll.field_report_hr_payroll_report_payslip_details___last_update
msgid "Last Modified on"
msgstr "Modifikimi i fundit në"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_write_uid
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_write_uid
msgid "Last Updated by"
msgstr "Modifikuar per here te fundit nga"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_employees_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_write_date
#: model:ir.model.fields,field_description:om_hr_payroll.field_payslip_lines_contribution_register_write_date
msgid "Last Updated on"
msgstr "Modifikuar per here te fundit me"

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_category_parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_lower_bound
msgid "Lower Bound"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_contract_advantage_template_lower_bound
msgid "Lower bound authorized by the employer for this advantage"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_paid
msgid "Made Payment Order ? "
msgstr ""

#. module: om_hr_payroll
#: model:res.groups,name:om_hr_payroll.group_hr_payroll_manager
msgid "Manager"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_condition_range_max
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_condition_range_max
msgid "Maximum Range"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_condition_range_min
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_condition_range_min
msgid "Minimum Range"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Miscellaneous"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Monthly"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_name
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_name
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Name"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:197
#, python-format
msgid "Normal Working Days paid at 100%"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_number_of_days
msgid "Number of Days"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_number_of_hours
msgid "Number of Hours"
msgstr ""

#. module: om_hr_payroll
#: model:res.groups,name:om_hr_payroll.group_hr_payroll_user
msgid "Officer"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_parent_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_category_parent_id
msgid "Parent"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_parent_rule_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_parent_rule_id
msgid "Parent Salary Rule"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_partner_id
msgid "Partner"
msgstr "Partner"

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_payslip_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_slip_id
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_payslip_id
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Pay Slip"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "PaySlip Batch"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.report,name:om_hr_payroll.payslip_details_report
msgid "PaySlip Details"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_payslip_lines_contribution_register
msgid "PaySlip Lines"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.report,name:om_hr_payroll.action_contribution_register
msgid "PaySlip Lines By Conribution Register"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
msgid "PaySlip Lines by Contribution Register"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_payslip_lines_contribution_register
msgid "PaySlip Lines by Contribution Registers"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
msgid "PaySlip Name"
msgstr ""

#. module: om_hr_payroll
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_payroll_root
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
msgid "Payroll Rules"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.report,name:om_hr_payroll.action_report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Payslip"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:84
#, python-format
msgid "Payslip 'Date From' must be before 'Date To'."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_payslip_run_id
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_filter
msgid "Payslip Batches"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.act_payslip_lines
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_payslip_count
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Payslip Computation Details"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payslip_input
msgid "Payslip Input"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_line_ids
msgid "Payslip Inputs"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_ids
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Payslip Lines by Contribution Register"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_name
msgid "Payslip Name"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payslip_run
msgid "Payslip Run"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_line_ids
msgid "Payslip Worked Days"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_employee_payslip_count
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_employee_slip_ids
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_slip_ids
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_tree
msgid "Payslips"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_hr_payslip_run_tree
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_payslip_run
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip.line,amount_select:0
#: selection:hr.salary.rule,amount_select:0
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_amount_percentage
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_amount_percentage
msgid "Percentage (%)"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_amount_percentage_base
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_amount_percentage_base
msgid "Percentage based on"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_payslip_lines_contribution_register
msgid "Print"
msgstr "Print"

#. module: om_hr_payroll
#: selection:hr.payslip.line,amount_select:0
#: selection:hr.salary.rule,amount_select:0
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_amount_python_compute
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_amount_python_compute
msgid "Python Code"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_condition_python
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_condition_python
msgid "Python Condition"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip.line,condition_select:0
#: selection:hr.salary.rule,condition_select:0
msgid "Python Expression"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_quantity
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_quantity
msgid "Quantity"
msgstr "Sasia"

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
msgid "Quantity/Rate"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Quantity/rate"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Quarterly"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip.line,condition_select:0
#: selection:hr.salary.rule,condition_select:0
msgid "Range"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_condition_range
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_condition_range
msgid "Range Based on"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_rate
msgid "Rate (%)"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_code
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_number
msgid "Reference"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:104
#, python-format
msgid "Refund: "
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contribution_register_register_line_ids
msgid "Register Line"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip,state:0
msgid "Rejected"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_salary_rule_id
msgid "Rule"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_hr_salary_rule_category
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_category_tree_view
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_hr_salary_rule_category_tree_view
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_salary_rule_category_tree_view
msgid "Salary Rule Categories Hierarchy"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_rule_input
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_rule_input_input_id
msgid "Salary Rule Input"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payroll_structure_rule_ids
#: model:ir.ui.menu,name:om_hr_payroll.menu_action_hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_salary_rule_tree
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:396
#: code:addons/om_hr_payroll/models/hr_payslip.py:446
#, python-format
msgid "Salary Slip of %s for %s"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_struct_id
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payroll_structure_tree
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_tree
msgid "Salary Structure"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_view_hr_payroll_structure_list_form
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_payroll_structure_view
msgid "Salary Structures"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_schedule_pay
msgid "Scheduled Pay"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Semi-annually"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_input_sequence
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_sequence
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_worked_days_sequence
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_salary_rule_sequence
msgid "Sequence"
msgstr "Sekuencë"

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr ""

#. module: om_hr_payroll
#: model:ir.actions.act_window,name:om_hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:om_hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_filter
msgid "States"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_run_state
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_state
msgid "Status"
msgstr "Statusi"

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_struct_id
msgid "Structure"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_code
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_input_code
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_worked_days_code
#: model:ir.model.fields,help:om_hr_payroll.field_hr_rule_input_code
msgid "The code that can be used in the salary rules"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_amount_select
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_amount_select
msgid "The computation method for the rule amount."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_input_contract_id
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_worked_days_contract_id
msgid "The contract for which applied this input"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_condition_range_max
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_condition_range_min
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_condition_range
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_by_employees
msgid ""
"This wizard will generate payslips for all selected employee(s) based on the"
" dates and credit note specified on Payslips Run."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_payslip_line_total
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.report_payslip_details
msgid "Total"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,field_description:om_hr_payroll.field_hr_contract_advantage_template_upper_bound
msgid "Upper Bound"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_contract_advantage_template_upper_bound
msgid "Upper bound authorized by the employer for this advantage"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_sequence
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_sequence
msgid "Use to arrange calculation sequence"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_appears_on_payslip
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr ""

#. module: om_hr_payroll
#: selection:hr.payslip,state:0
msgid "Waiting"
msgstr ""

#. module: om_hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Weekly"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr ""

#. module: om_hr_payroll
#: model_terms:ir.ui.view,arch_db:om_hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:198
#, python-format
msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:204
#, python-format
msgid "Wrong python code defined for salary rule %s (%s)."
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:227
#, python-format
msgid "Wrong python condition defined for salary rule %s (%s)."
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:191
#, python-format
msgid "Wrong quantity defined for salary rule %s (%s)."
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_salary_rule.py:221
#, python-format
msgid "Wrong range condition defined for salary rule %s (%s)."
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:128
#, python-format
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/wizard/hr_payroll_payslips_by_employees.py:24
#, python-format
msgid "You must select employee(s) to generate payslip(s)."
msgstr ""

#. module: om_hr_payroll
#: code:addons/om_hr_payroll/models/hr_payslip.py:518
#, python-format
msgid "You must set a contract to create a payslip line."
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_hr_salary_rule
msgid "hr.salary.rule"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_report_hr_payroll_report_contributionregister
msgid "report.om_hr_payroll.report_contributionregister"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_report_hr_payroll_report_payslip_details
msgid "report.om_hr_payroll.report_payslip_details"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model,name:om_hr_payroll.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: om_hr_payroll
#: model:ir.model.fields,help:om_hr_payroll.field_hr_payslip_line_amount_percentage_base
#: model:ir.model.fields,help:om_hr_payroll.field_hr_salary_rule_amount_percentage_base
msgid "result will be affected to a variable"
msgstr ""
