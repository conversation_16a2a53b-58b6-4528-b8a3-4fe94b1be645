# -*- coding: utf-8 -*-
{
    'name': 'Enhanced Balance Sheet with Drill-Down',
    'version': '********.0',
    'category': 'Accounting/Financial Reports',
    'summary': 'Professional Balance Sheet with Segregated Equity & Liabilities and Drill-Down Features',
    'description': """
        Enhanced Balance Sheet Report
        
        This module provides a professional Balance Sheet presentation with:
        
        📊 **Enhanced Balance Sheet Features:**
        - Separate sections for Liabilities and Equity (not combined)
        - Professional IFRS-compliant presentation
        - Clear segregation of Current vs Non-Current items
        - Proper subtotals and grand totals
        
        🔍 **Advanced Drill-Down Navigation:**
        - Account Level → Ledger Level → Entry Level navigation
        - Click on any line item to drill down to account details
        - View journal entries and move lines
        - Enhanced filtering and search capabilities
        
        📈 **Professional Presentation:**
        - Clean, modern layout with proper formatting
        - Bold headers and underlined totals
        - Indented sub-items for better readability
        - Comparative period support
        - Multi-currency support
        
        🎯 **Key Features:**
        - Assets section with Current and Non-Current breakdown
        - Liabilities section (separate from Equity)
        - Equity section with detailed components
        - Auto-balancing verification (Assets = Liabilities + Equity)
        - Export to Excel/PDF capabilities
        - Real-time data updates
        
        Perfect for listed companies requiring professional financial statement
        presentation with detailed drill-down capabilities for auditing and analysis.
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'account',
        'web',
    ],
    'data': [
        # Security
        'security/ir.model.access.csv',
        
        # Views
        'views/enhanced_balance_sheet_views.xml',
        'views/balance_sheet_report_views.xml',
        
        # Reports
        'reports/balance_sheet_report_templates.xml',
        
        # Menu
        'views/enhanced_balance_sheet_menu.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'enhanced_balance_sheet/static/src/css/balance_sheet.css',
            'enhanced_balance_sheet/static/src/js/balance_sheet_drill_down.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    'sequence': 15,
}
