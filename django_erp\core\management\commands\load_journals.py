"""
Management command to load accounting journals from Odoo data
"""

import json
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from accounting.models import AccountJournal, AccountAccount
from core.models import Company

User = get_user_model()


class Command(BaseCommand):
    help = 'Load accounting journals from Odoo data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing journals',
        )
        parser.add_argument(
            '--company',
            type=str,
            help='Company name to associate journals with',
            default='Default Company'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Loading accounting journals from Odoo data...'))
        
        # Load journals from extracted JSON file
        json_file = 'journals_data.json'
        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(
                    f'Journals data file {json_file} not found. '
                    'Please run: python extract_odoo_data.py first'
                )
            )
            return
        
        with open(json_file, 'r') as f:
            journals_data = json.load(f)

        created_count = 0
        updated_count = 0
        
        # Get or create admin user for create_uid/write_uid
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )
        
        # Get company
        try:
            company = Company.objects.get(name=options['company'])
        except Company.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Company "{options["company"]}" not found. Please create it first.')
            )
            return
        
        # Helper function to get default account by type
        def get_default_account(journal_type):
            """Get appropriate default account based on journal type"""
            account_mappings = {
                'sale': '121000',      # Account Receivable
                'purchase': '211000',  # Account Payable
                'cash': '101501',      # Cash
                'bank': '101401',      # Bank
                'general': '101000',   # Current Assets
            }

            account_code = account_mappings.get(journal_type, '101000')
            try:
                return AccountAccount.objects.get(code=account_code, company_id=company)
            except AccountAccount.DoesNotExist:
                # Fallback to first available account
                return AccountAccount.objects.filter(company_id=company).first()

        with transaction.atomic():
            for journal_data in journals_data:
                code = journal_data['code']
                journal_type = journal_data.get('type', 'general')

                # Extract name from nested structure
                if isinstance(journal_data['name'], dict):
                    name = journal_data['name'].get('en_US', code)
                else:
                    name = journal_data['name']

                # Get default account for this journal type
                default_account = get_default_account(journal_type)
                if not default_account:
                    self.stdout.write(
                        self.style.ERROR(f'No accounts found for journal {code}. Please load chart of accounts first.')
                    )
                    return

                # Prepare clean data for Django model
                clean_data = {
                    'name': name,
                    'code': code,
                    'type': journal_type,
                    'active': journal_data.get('active', True),
                    'company_id': company,
                    'default_account_id': default_account,
                    'create_uid': admin_user,
                    'write_uid': admin_user,
                }
                
                try:
                    journal = AccountJournal.objects.get(code=code, company_id=company)
                    if options['update']:
                        # Update existing journal
                        for field, value in clean_data.items():
                            if field not in ['create_uid']:  # Don't update create_uid
                                setattr(journal, field, value)
                        journal.save()
                        updated_count += 1
                        self.stdout.write(f'Updated journal: {code} - {name}')
                    else:
                        self.stdout.write(f'Journal already exists: {code} - {name}')
                        
                except AccountJournal.DoesNotExist:
                    # Create new journal
                    journal = AccountJournal.objects.create(**clean_data)
                    created_count += 1
                    self.stdout.write(f'Created journal: {code} - {name}')

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nJournal loading completed!\n'
                f'Created: {created_count} journals\n'
                f'Updated: {updated_count} journals\n'
                f'Total journals in system: {AccountJournal.objects.count()}\n'
                f'Company: {company.name}'
            )
        )
        
        # Show journals by type
        self.stdout.write('\nJournals by type:')
        journal_types = [
            ('sale', 'Sales'),
            ('purchase', 'Purchase'),
            ('cash', 'Cash'),
            ('bank', 'Bank'),
            ('general', 'General'),
        ]
        
        for journal_type, type_name in journal_types:
            count = AccountJournal.objects.filter(
                type=journal_type,
                company_id=company,
                active=True
            ).count()
            if count > 0:
                journals = AccountJournal.objects.filter(
                    type=journal_type,
                    company_id=company,
                    active=True
                )
                journal_list = ', '.join([f'{j.code}' for j in journals])
                self.stdout.write(f'  {type_name}: {count} journals ({journal_list})')
