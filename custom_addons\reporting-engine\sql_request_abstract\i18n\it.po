# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sql_request_abstract
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-12-01 02:11+0000\n"
"PO-Revision-Date: 2025-06-21 13:27+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.10.4\n"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__group_ids
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Allowed Groups"
msgstr "Gruppi consentiti"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__user_ids
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Allowed Users"
msgstr "Utenti consentiti"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_attachment_count
msgid "Attachment Count"
msgstr "Conteggio allegati"

#. module: sql_request_abstract
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__bi_sql_view__state__draft
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_export__state__draft
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_request_mixin__state__draft
msgid "Draft"
msgstr "Bozza"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__has_group_changed
msgid "Has Group Changed"
msgstr "Ha il gruppo modificato"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__has_message
msgid "Has Message"
msgstr "Ha un messaggio"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi hanno un errore di consegna."

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_is_follower
msgid "Is Follower"
msgstr "Segue"

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid "It is not allowed to execute a not checked request."
msgstr "Non è consentito eseguire una richiesta non controllata."

#. module: sql_request_abstract
#: model:res.groups,name:sql_request_abstract.group_sql_request_manager
msgid "Manager"
msgstr "Supervisore"

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid ""
"Materialized View requires PostgreSQL 9.3 or greater but PostgreSQL %s is "
"currently installed."
msgstr ""
"La vsta realizzata richiede PostgreSQL 9.3 o superiore ma attualmente è "
"installato PostgreSQL %s."

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__name
msgid "Name"
msgstr "Nome"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__note
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Note"
msgstr "Nota"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Preview Results"
msgstr "Anteprima risulati"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__query
msgid "Query"
msgstr "Query"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "SQL Query"
msgstr "Query SQL"

#. module: sql_request_abstract
#: model:ir.module.category,name:sql_request_abstract.category_sql_abstract
msgid "SQL Request"
msgstr "Richiesta SQL"

#. module: sql_request_abstract
#: model:ir.model,name:sql_request_abstract.model_sql_request_mixin
msgid "SQL Request Mixin"
msgstr "Mixin richiesta SQL"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "SQL Settings"
msgstr "Impostazioni SQL"

#. module: sql_request_abstract
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__bi_sql_view__state__sql_valid
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_export__state__sql_valid
#: model:ir.model.fields.selection,name:sql_request_abstract.selection__sql_request_mixin__state__sql_valid
msgid "SQL Valid"
msgstr "SQL valido"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Security"
msgstr "Sicurezza"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Set to Draft"
msgstr "Imposta a bozza"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__state
msgid "State"
msgstr "Stato"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__state
msgid ""
"State of the Request:\n"
" * 'Draft': Not tested\n"
" * 'SQL Valid': SQL Request has been checked and is valid"
msgstr ""
"Stato della richiesta:\n"
" * 'Bozza': non restata\n"
" * 'SQL valido': la richiesta SQL è stata testata ed è valida"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__has_group_changed
msgid ""
"Technical fields, used in modules that depends on this one to know if groups "
"has changed, and that according access should be updated."
msgstr ""
"Campo tecnico utilizzato nei moduli che dipendono da questo per sapere se i "
"gruppi sono cambiati e che le atorizzazoni di accesso devono essere "
"aggiornate."

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid ""
"The SQL query is not valid:\n"
"\n"
" %s"
msgstr ""
"La query SQL non è valida:\n"
"\n"
" %s"

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid "The query is not allowed because it contains unsafe word '%s'"
msgstr "La query non è consentita perchè contiene parola non sicura '%s'"

#. module: sql_request_abstract
#. odoo-python
#: code:addons/sql_request_abstract/models/sql_request_mixin.py:0
#, python-format
msgid "Unimplemented mode : '%s'"
msgstr "Modo non implementato: '%s'"

#. module: sql_request_abstract
#: model:res.groups,name:sql_request_abstract.group_sql_request_user
msgid "User"
msgstr "Utente"

#. module: sql_request_abstract
#: model_terms:ir.ui.view,arch_db:sql_request_abstract.view_sql_request_mixin_form
msgid "Validate SQL Expression"
msgstr "Valida espressione SQL"

#. module: sql_request_abstract
#: model:ir.model.fields,field_description:sql_request_abstract.field_sql_request_mixin__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: sql_request_abstract
#: model:ir.model.fields,help:sql_request_abstract.field_sql_request_mixin__query
msgid ""
"You can't use the following words: DELETE, DROP, CREATE, INSERT, ALTER, "
"TRUNCATE, EXECUTE, UPDATE."
msgstr ""
"Non si possono usare le segenti parole: DELETE, DROP, CREATE, INSERT, ALTER, "
"TRUNCATE, EXECUTE, UPDATE."

#~ msgid "SMS Delivery error"
#~ msgstr "Errore consegna SMS"

#~ msgid "Display Name"
#~ msgstr "Nome da visualizzare"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Ultima modifica il"
