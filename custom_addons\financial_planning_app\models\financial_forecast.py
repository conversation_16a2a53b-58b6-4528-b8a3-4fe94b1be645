# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, date
import math


class FinancialForecast(models.Model):
    _name = 'financial.planning.forecast'
    _description = 'Financial Planning Forecast'
    _order = 'plan_id, period_date'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    plan_id = fields.Many2one('financial.planning.plan', string='Financial Plan', required=True, ondelete='cascade')
    
    # Period information
    period_date = fields.Date(string='Period Date', required=True)
    period_type = fields.Selection([
        ('historical', 'Historical'),
        ('launch', 'Launch Month'),
        ('forecast', 'Forecast')
    ], string='Period Type', required=True)
    months_from_launch = fields.Integer(string='Months from Launch', help='Negative for pre-launch, 0 for launch month, positive for post-launch')
    
    # Population-based metrics
    target_population = fields.Float(string='Target Population (M)', help='Target population in millions for this period')
    population_growth_rate = fields.Float(string='Population Growth Rate (%)', digits=(5, 2))
    market_penetration = fields.Float(string='Market Penetration (%)', digits=(5, 2))
    active_customers = fields.Float(string='Active Customers (M)', help='Number of active customers in millions')
    
    # Financial projections
    revenue = fields.Monetary(string='Revenue', currency_field='currency_id')
    revenue_growth_rate = fields.Float(string='Revenue Growth Rate (%)', digits=(5, 2))
    cost_of_sales = fields.Monetary(string='Cost of Sales', currency_field='currency_id')
    gross_profit = fields.Monetary(string='Gross Profit', currency_field='currency_id', compute='_compute_profit_metrics', store=True)
    gross_margin = fields.Float(string='Gross Margin (%)', digits=(5, 2), compute='_compute_profit_metrics', store=True)
    
    # Operating expenses
    marketing_expenses = fields.Monetary(string='Marketing Expenses', currency_field='currency_id')
    operational_expenses = fields.Monetary(string='Operational Expenses', currency_field='currency_id')
    administrative_expenses = fields.Monetary(string='Administrative Expenses', currency_field='currency_id')
    total_expenses = fields.Monetary(string='Total Expenses', currency_field='currency_id', compute='_compute_expense_metrics', store=True)
    
    # Profitability
    ebitda = fields.Monetary(string='EBITDA', currency_field='currency_id', compute='_compute_profit_metrics', store=True)
    ebitda_margin = fields.Float(string='EBITDA Margin (%)', digits=(5, 2), compute='_compute_profit_metrics', store=True)
    net_profit = fields.Monetary(string='Net Profit', currency_field='currency_id', compute='_compute_profit_metrics', store=True)
    net_margin = fields.Float(string='Net Margin (%)', digits=(5, 2), compute='_compute_profit_metrics', store=True)
    
    # Cash flow
    cash_inflow = fields.Monetary(string='Cash Inflow', currency_field='currency_id')
    cash_outflow = fields.Monetary(string='Cash Outflow', currency_field='currency_id')
    net_cash_flow = fields.Monetary(string='Net Cash Flow', currency_field='currency_id', compute='_compute_cash_flow', store=True)
    cumulative_cash_flow = fields.Monetary(string='Cumulative Cash Flow', currency_field='currency_id')
    
    # Investment and funding
    investment_required = fields.Monetary(string='Investment Required', currency_field='currency_id')
    funding_received = fields.Monetary(string='Funding Received', currency_field='currency_id')
    
    # Key performance indicators
    customer_acquisition_cost = fields.Monetary(string='Customer Acquisition Cost', currency_field='currency_id')
    customer_lifetime_value = fields.Monetary(string='Customer Lifetime Value', currency_field='currency_id')
    revenue_per_customer = fields.Monetary(string='Revenue per Customer', currency_field='currency_id', compute='_compute_customer_metrics', store=True)
    
    # Currency and company
    currency_id = fields.Many2one(related='plan_id.base_currency_id', string='Currency', store=True)
    company_id = fields.Many2one(related='plan_id.company_id', string='Company', store=True)
    
    # Additional fields
    notes = fields.Text(string='Notes')
    is_actual = fields.Boolean(string='Is Actual Data', default=False, help='True if this contains actual historical data')

    @api.depends('plan_id', 'period_date')
    def _compute_name(self):
        for forecast in self:
            if forecast.plan_id and forecast.period_date:
                forecast.name = f"{forecast.plan_id.name} - {forecast.period_date.strftime('%B %Y')}"
            else:
                forecast.name = "New Forecast"

    @api.depends('revenue', 'cost_of_sales', 'total_expenses')
    def _compute_profit_metrics(self):
        for forecast in self:
            # Gross profit and margin
            forecast.gross_profit = forecast.revenue - forecast.cost_of_sales
            if forecast.revenue > 0:
                forecast.gross_margin = (forecast.gross_profit / forecast.revenue) * 100
            else:
                forecast.gross_margin = 0.0
            
            # EBITDA and margin
            forecast.ebitda = forecast.gross_profit - forecast.total_expenses
            if forecast.revenue > 0:
                forecast.ebitda_margin = (forecast.ebitda / forecast.revenue) * 100
            else:
                forecast.ebitda_margin = 0.0
            
            # Net profit and margin (simplified, assuming EBITDA = Net Profit for now)
            forecast.net_profit = forecast.ebitda
            if forecast.revenue > 0:
                forecast.net_margin = (forecast.net_profit / forecast.revenue) * 100
            else:
                forecast.net_margin = 0.0

    @api.depends('marketing_expenses', 'operational_expenses', 'administrative_expenses')
    def _compute_expense_metrics(self):
        for forecast in self:
            forecast.total_expenses = forecast.marketing_expenses + forecast.operational_expenses + forecast.administrative_expenses

    @api.depends('cash_inflow', 'cash_outflow')
    def _compute_cash_flow(self):
        for forecast in self:
            forecast.net_cash_flow = forecast.cash_inflow - forecast.cash_outflow

    @api.depends('revenue', 'active_customers')
    def _compute_customer_metrics(self):
        for forecast in self:
            if forecast.active_customers > 0:
                # Convert customers from millions to actual number for calculation
                actual_customers = forecast.active_customers * 1000000
                forecast.revenue_per_customer = forecast.revenue / actual_customers
            else:
                forecast.revenue_per_customer = 0.0

    def calculate_forecast_values(self):
        """Calculate forecast values based on plan parameters and growth assumptions"""
        self.ensure_one()
        
        plan = self.plan_id
        if not plan:
            return
        
        # Calculate target population for this period
        base_population = plan.total_target_population
        if self.months_from_launch != 0:
            # Apply population growth
            avg_growth_rate = 0.0
            if plan.country_ids:
                avg_growth_rate = sum(plan.country_ids.mapped('annual_population_growth_rate')) / len(plan.country_ids)
            
            # Monthly growth rate
            monthly_growth_rate = avg_growth_rate / 12 / 100
            growth_factor = (1 + monthly_growth_rate) ** self.months_from_launch
            self.target_population = base_population * growth_factor
        else:
            self.target_population = base_population
        
        # Calculate market penetration based on period type
        if self.period_type == 'historical':
            # Historical periods have zero or minimal penetration
            self.market_penetration = 0.0
            self.active_customers = 0.0
            self.revenue = 0.0
        elif self.period_type == 'launch':
            # Launch month starts with initial penetration
            self.market_penetration = plan.market_penetration_rate * 0.1  # 10% of target in launch month
            self.active_customers = self.target_population * (self.market_penetration / 100)
        else:
            # Forecast periods - gradual market penetration growth
            months_since_launch = max(1, self.months_from_launch)
            
            # S-curve adoption model
            max_penetration = plan.market_penetration_rate
            growth_rate = 0.1  # Penetration growth rate
            
            # Logistic growth function
            penetration_factor = 1 / (1 + math.exp(-growth_rate * (months_since_launch - 12)))
            self.market_penetration = max_penetration * penetration_factor
            self.active_customers = self.target_population * (self.market_penetration / 100)
        
        # Calculate revenue
        if self.active_customers > 0 and plan.revenue_per_capita > 0:
            # Convert customers from millions to actual number
            actual_customers = self.active_customers * 1000000
            self.revenue = actual_customers * plan.revenue_per_capita
        
        # Calculate revenue growth rate
        if self.months_from_launch > 0:
            monthly_growth_rate = plan.annual_revenue_growth_rate / 12
            self.revenue_growth_rate = monthly_growth_rate
        
        # Calculate costs and expenses (simplified model)
        if self.revenue > 0:
            # Cost of sales (assume 40% of revenue)
            self.cost_of_sales = self.revenue * 0.4
            
            # Marketing expenses (higher in early months)
            if self.months_from_launch <= 12:
                marketing_rate = 0.25  # 25% of revenue in first year
            else:
                marketing_rate = 0.15  # 15% of revenue after first year
            self.marketing_expenses = self.revenue * marketing_rate
            
            # Operational expenses (10% of revenue)
            self.operational_expenses = self.revenue * 0.1
            
            # Administrative expenses (5% of revenue)
            self.administrative_expenses = self.revenue * 0.05
        
        # Calculate cash flow
        self.cash_inflow = self.revenue
        self.cash_outflow = self.cost_of_sales + self.total_expenses
        
        # Calculate customer metrics
        if self.active_customers > 0:
            self.customer_acquisition_cost = plan.customer_acquisition_cost or (self.marketing_expenses / (self.active_customers * 1000000))
            self.customer_lifetime_value = plan.revenue_per_capita * 24  # Assume 24 month lifetime
        
        # Investment requirements (higher in early periods)
        if self.months_from_launch <= 0:
            # Pre-launch investment
            self.investment_required = plan.initial_investment / plan.historical_months
        elif self.months_from_launch <= 12:
            # First year operational investment
            self.investment_required = self.cash_outflow - self.cash_inflow if self.cash_outflow > self.cash_inflow else 0
        
        return True

    def get_forecast_summary(self):
        """Get comprehensive forecast summary"""
        self.ensure_one()
        return {
            'period': self.period_date.strftime('%B %Y'),
            'period_type': self.period_type,
            'months_from_launch': self.months_from_launch,
            'target_population': self.target_population,
            'market_penetration': self.market_penetration,
            'active_customers': self.active_customers,
            'revenue': self.revenue,
            'gross_profit': self.gross_profit,
            'gross_margin': self.gross_margin,
            'ebitda': self.ebitda,
            'ebitda_margin': self.ebitda_margin,
            'net_cash_flow': self.net_cash_flow,
            'revenue_per_customer': self.revenue_per_customer,
        }
