<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Market Sizing Tree View -->
    <record id="view_market_sizing_tree" model="ir.ui.view">
        <field name="name">financial.planning.market.sizing.tree</field>
        <field name="model">financial.planning.market.sizing</field>
        <field name="arch" type="xml">
            <tree string="Market Sizing Analysis" decoration-success="state=='approved'" decoration-info="state=='review'" decoration-muted="state=='outdated'">
                <field name="name"/>
                <field name="industry_sector"/>
                <field name="target_customer_segment"/>
                <field name="target_population" widget="float" digits="[16,1]"/>
                <field name="total_available_market" widget="monetary"/>
                <field name="serviceable_available_market" widget="monetary"/>
                <field name="addressable_market" widget="monetary"/>
                <field name="realistic_market_share" widget="percentage"/>
                <field name="date_analysis"/>
                <field name="state" widget="badge" decoration-success="state=='approved'" decoration-info="state=='review'" decoration-warning="state=='draft'" decoration-muted="state=='outdated'"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <!-- Market Sizing Form View -->
    <record id="view_market_sizing_form" model="ir.ui.view">
        <field name="name">financial.planning.market.sizing.form</field>
        <field name="model">financial.planning.market.sizing</field>
        <field name="arch" type="xml">
            <form string="Market Sizing Analysis">
                <header>
                    <button name="action_approve" string="Approve Analysis" type="object" class="btn-primary" attrs="{'invisible': [('state', '!=', 'review')]}"/>
                    <button name="action_mark_outdated" string="Mark as Outdated" type="object" class="btn-secondary" attrs="{'invisible': [('state', '!=', 'approved')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,review,approved"/>
                </header>
                
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Market Sizing Analysis Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Basic Information">
                            <field name="description"/>
                            <field name="date_analysis"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="currency_id"/>
                        </group>
                        <group string="Market Definition">
                            <field name="industry_sector"/>
                            <field name="product_category"/>
                            <field name="target_customer_segment"/>
                            <field name="competitive_intensity"/>
                            <field name="market_maturity"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Target Markets">
                            <group>
                                <group string="Geographic Scope">
                                    <field name="continent_ids" widget="many2many_tags"/>
                                    <field name="country_ids" widget="many2many_tags"/>
                                    <field name="city_ids" widget="many2many_tags"/>
                                </group>
                                <group string="Population Metrics">
                                    <field name="target_population" widget="float" digits="[16,1]"/>
                                    <field name="addressable_population_percentage" widget="percentage"/>
                                    <field name="addressable_population" widget="float" digits="[16,1]"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Total Available Market (TAM)">
                            <group>
                                <group string="TAM Calculation">
                                    <field name="tam_calculation_method"/>
                                    <field name="average_spending_per_person" widget="monetary" attrs="{'invisible': [('tam_calculation_method', '!=', 'population_based')]}"/>
                                    <field name="total_available_market" widget="monetary"/>
                                    <field name="tam_per_capita" widget="monetary" readonly="1"/>
                                </group>
                                <group string="Market Dynamics">
                                    <field name="market_growth_rate" widget="percentage"/>
                                </group>
                            </group>
                            
                            <div class="alert alert-info" role="alert" attrs="{'invisible': [('tam_calculation_method', '!=', 'population_based')]}">
                                <strong>Population-Based TAM:</strong> TAM = Addressable Population × Average Spending per Person
                            </div>
                        </page>
                        
                        <page string="Serviceable Available Market (SAM)">
                            <group>
                                <group string="SAM Calculation">
                                    <field name="geographic_reach_percentage" widget="percentage"/>
                                    <field name="business_model_fit_percentage" widget="percentage"/>
                                    <field name="serviceable_available_market" widget="monetary"/>
                                    <field name="sam_percentage_of_tam" widget="percentage" readonly="1"/>
                                    <field name="sam_per_capita" widget="monetary" readonly="1"/>
                                </group>
                            </group>
                            
                            <div class="alert alert-info" role="alert">
                                <strong>SAM Calculation:</strong> SAM = TAM × Geographic Reach % × Business Model Fit %
                            </div>
                        </page>
                        
                        <page string="Addressable Market (SOM)">
                            <group>
                                <group string="SOM Calculation">
                                    <field name="realistic_market_share" widget="percentage"/>
                                    <field name="addressable_market" widget="monetary"/>
                                    <field name="som_percentage_of_sam" widget="percentage" readonly="1"/>
                                    <field name="som_per_capita" widget="monetary" readonly="1"/>
                                </group>
                            </group>
                            
                            <div class="alert alert-info" role="alert">
                                <strong>SOM Calculation:</strong> SOM = SAM × Realistic Market Share %
                            </div>
                        </page>
                        
                        <page string="Market Analysis">
                            <group>
                                <group string="Key Assumptions">
                                    <field name="key_assumptions" nolabel="1"/>
                                </group>
                                <group string="Market Risks">
                                    <field name="market_risks" nolabel="1"/>
                                </group>
                            </group>
                            <group>
                                <group string="Market Opportunities">
                                    <field name="opportunities" nolabel="1"/>
                                </group>
                                <group string="Data Sources">
                                    <field name="data_sources" nolabel="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Market Summary">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="card-title mb-0">Total Available Market (TAM)</h5>
                                        </div>
                                        <div class="card-body">
                                            <h3 class="text-primary">
                                                <field name="total_available_market" widget="monetary" readonly="1"/>
                                            </h3>
                                            <p class="card-text">
                                                Per Capita: <field name="tam_per_capita" widget="monetary" readonly="1"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="card-title mb-0">Serviceable Available Market (SAM)</h5>
                                        </div>
                                        <div class="card-body">
                                            <h3 class="text-success">
                                                <field name="serviceable_available_market" widget="monetary" readonly="1"/>
                                            </h3>
                                            <p class="card-text">
                                                <field name="sam_percentage_of_tam" widget="percentage" readonly="1"/> of TAM<br/>
                                                Per Capita: <field name="sam_per_capita" widget="monetary" readonly="1"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h5 class="card-title mb-0">Addressable Market (SOM)</h5>
                                        </div>
                                        <div class="card-body">
                                            <h3 class="text-warning">
                                                <field name="addressable_market" widget="monetary" readonly="1"/>
                                            </h3>
                                            <p class="card-text">
                                                <field name="som_percentage_of_sam" widget="percentage" readonly="1"/> of SAM<br/>
                                                Per Capita: <field name="som_per_capita" widget="monetary" readonly="1"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="card-title mb-0">Population Metrics</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>Target Population: <field name="target_population" widget="float" digits="[16,1]" readonly="1"/>M</p>
                                            <p>Addressable Population: <field name="addressable_population" widget="float" digits="[16,1]" readonly="1"/>M</p>
                                            <p>Market Share Target: <field name="realistic_market_share" widget="percentage" readonly="1"/></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-secondary">
                                        <div class="card-header bg-secondary text-white">
                                            <h6 class="card-title mb-0">Market Characteristics</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>Industry: <field name="industry_sector" readonly="1"/></p>
                                            <p>Customer Segment: <field name="target_customer_segment" readonly="1"/></p>
                                            <p>Competition Level: <field name="competitive_intensity" readonly="1"/></p>
                                            <p>Market Growth: <field name="market_growth_rate" widget="percentage" readonly="1"/></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Market Sizing Kanban View -->
    <record id="view_market_sizing_kanban" model="ir.ui.view">
        <field name="name">financial.planning.market.sizing.kanban</field>
        <field name="model">financial.planning.market.sizing</field>
        <field name="arch" type="xml">
            <kanban default_group_by="industry_sector" class="o_kanban_small_column">
                <field name="name"/>
                <field name="industry_sector"/>
                <field name="target_customer_segment"/>
                <field name="total_available_market"/>
                <field name="addressable_market"/>
                <field name="state"/>
                <field name="currency_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div>Segment: <field name="target_customer_segment"/></div>
                                        <div>TAM: <field name="total_available_market" widget="monetary"/></div>
                                        <div>Addressable: <field name="addressable_market" widget="monetary"/></div>
                                        <div class="mt-2">
                                            <span class="badge badge-pill" t-attf-class="badge-#{record.state.raw_value == 'approved' ? 'success' : record.state.raw_value == 'review' ? 'info' : record.state.raw_value == 'outdated' ? 'secondary' : 'warning'}">
                                                <field name="state"/>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Market Sizing Search View -->
    <record id="view_market_sizing_search" model="ir.ui.view">
        <field name="name">financial.planning.market.sizing.search</field>
        <field name="model">financial.planning.market.sizing</field>
        <field name="arch" type="xml">
            <search string="Market Sizing Analysis">
                <field name="name"/>
                <field name="industry_sector"/>
                <field name="target_customer_segment"/>
                <field name="country_ids"/>
                <field name="city_ids"/>
                <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                <filter string="Under Review" name="review" domain="[('state', '=', 'review')]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <separator/>
                <filter string="This Year" name="this_year" domain="[('date_analysis', '&gt;=', datetime.datetime.now().strftime('%Y-01-01')), ('date_analysis', '&lt;=', datetime.datetime.now().strftime('%Y-12-31'))]"/>
                <filter string="High Competition" name="high_competition" domain="[('competitive_intensity', 'in', ['high', 'very_high'])]"/>
                <filter string="Growth Markets" name="growth_markets" domain="[('market_maturity', '=', 'growth')]"/>
                <group expand="0" string="Group By">
                    <filter string="Industry Sector" name="group_industry" context="{'group_by': 'industry_sector'}"/>
                    <filter string="Customer Segment" name="group_segment" context="{'group_by': 'target_customer_segment'}"/>
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Market Maturity" name="group_maturity" context="{'group_by': 'market_maturity'}"/>
                    <filter string="Company" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Market Sizing Action -->
    <record id="action_market_sizing" model="ir.actions.act_window">
        <field name="name">Market Sizing Analysis</field>
        <field name="res_model">financial.planning.market.sizing</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_market_sizing_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first market sizing analysis!
            </p>
            <p>
                Market sizing helps you calculate Total Available Market (TAM), 
                Serviceable Available Market (SAM), and Addressable Market (SOM) 
                based on population demographics and market research.
            </p>
        </field>
    </record>

</odoo>
