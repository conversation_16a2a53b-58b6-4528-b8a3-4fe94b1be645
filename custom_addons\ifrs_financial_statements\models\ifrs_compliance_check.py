# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class IFRSComplianceCheck(models.Model):
    _name = 'ifrs.compliance.check'
    _description = 'IFRS Compliance Check'
    _order = 'check_date desc'

    statement_id = fields.Many2one(
        'ifrs.financial.statement',
        string='Financial Statement',
        required=True,
        ondelete='cascade'
    )
    
    name = fields.Char(string='Check Name', required=True)
    
    ifrs_standard = fields.Selection([
        ('ias_1', 'IAS 1 - Presentation of Financial Statements'),
        ('ias_2', 'IAS 2 - Inventories'),
        ('ias_7', 'IAS 7 - Statement of Cash Flows'),
        ('ias_8', 'IAS 8 - Accounting Policies, Changes in Estimates and Errors'),
        ('ias_10', 'IAS 10 - Events after the Reporting Period'),
        ('ias_12', 'IAS 12 - Income Taxes'),
        ('ias_16', 'IAS 16 - Property, Plant and Equipment'),
        ('ias_19', 'IAS 19 - Employee Benefits'),
        ('ias_21', 'IAS 21 - Effects of Changes in Foreign Exchange Rates'),
        ('ias_23', 'IAS 23 - Borrowing Costs'),
        ('ias_24', 'IAS 24 - Related Party Disclosures'),
        ('ias_32', 'IAS 32 - Financial Instruments: Presentation'),
        ('ias_36', 'IAS 36 - Impairment of Assets'),
        ('ias_37', 'IAS 37 - Provisions, Contingent Liabilities and Assets'),
        ('ias_38', 'IAS 38 - Intangible Assets'),
        ('ias_40', 'IAS 40 - Investment Property'),
        ('ifrs_1', 'IFRS 1 - First-time Adoption of IFRS'),
        ('ifrs_2', 'IFRS 2 - Share-based Payment'),
        ('ifrs_3', 'IFRS 3 - Business Combinations'),
        ('ifrs_5', 'IFRS 5 - Non-current Assets Held for Sale'),
        ('ifrs_7', 'IFRS 7 - Financial Instruments: Disclosures'),
        ('ifrs_8', 'IFRS 8 - Operating Segments'),
        ('ifrs_9', 'IFRS 9 - Financial Instruments'),
        ('ifrs_10', 'IFRS 10 - Consolidated Financial Statements'),
        ('ifrs_11', 'IFRS 11 - Joint Arrangements'),
        ('ifrs_12', 'IFRS 12 - Disclosure of Interests in Other Entities'),
        ('ifrs_13', 'IFRS 13 - Fair Value Measurement'),
        ('ifrs_15', 'IFRS 15 - Revenue from Contracts with Customers'),
        ('ifrs_16', 'IFRS 16 - Leases'),
        ('ifrs_17', 'IFRS 17 - Insurance Contracts'),
    ], string='IFRS Standard')
    
    status = fields.Selection([
        ('passed', 'Passed'),
        ('warning', 'Warning'),
        ('failed', 'Failed'),
        ('not_applicable', 'Not Applicable')
    ], string='Status', required=True, default='passed')
    
    check_date = fields.Datetime(string='Check Date', default=fields.Datetime.now)
    
    description = fields.Text(string='Check Description')
    
    findings = fields.Text(string='Findings')
    
    recommendations = fields.Text(string='Recommendations')
    
    responsible_user = fields.Many2one(
        'res.users',
        string='Responsible User',
        default=lambda self: self.env.user
    )
    
    priority = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ], string='Priority', default='medium')
    
    resolved = fields.Boolean(string='Resolved', default=False)
    
    resolution_date = fields.Datetime(string='Resolution Date')
    
    resolution_notes = fields.Text(string='Resolution Notes')
    
    def action_mark_resolved(self):
        """Mark compliance check as resolved"""
        self.resolved = True
        self.resolution_date = fields.Datetime.now()
        self.status = 'passed'
    
    def action_reopen(self):
        """Reopen compliance check"""
        self.resolved = False
        self.resolution_date = False
