<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Financial Statement with Analytics -->
        <record id="demo_financial_statement_2024" model="financial.statement.analytics">
            <field name="name">Annual Financial Statement with Analytics 2024</field>
            <field name="statement_type">complete_set</field>
            <field name="reporting_period">annual</field>
            <field name="date_from">2024-01-01</field>
            <field name="date_to">2024-12-31</field>
            <field name="comparative_period">True</field>
            <field name="comparative_date_from">2023-01-01</field>
            <field name="comparative_date_to">2023-12-31</field>
            <field name="enable_analytics">True</field>
            <field name="enable_charts">True</field>
            <field name="enable_drill_down">True</field>
            <field name="state">generated</field>
        </record>

        <!-- Demo Balance Sheet Statement -->
        <record id="demo_balance_sheet_2024" model="financial.statement.analytics">
            <field name="name">Balance Sheet with Analytics Q4 2024</field>
            <field name="statement_type">balance_sheet</field>
            <field name="reporting_period">quarterly</field>
            <field name="date_from">2024-10-01</field>
            <field name="date_to">2024-12-31</field>
            <field name="comparative_period">True</field>
            <field name="comparative_date_from">2024-07-01</field>
            <field name="comparative_date_to">2024-09-30</field>
            <field name="enable_analytics">True</field>
            <field name="enable_charts">True</field>
            <field name="enable_drill_down">True</field>
            <field name="state">generated</field>
        </record>

        <!-- Demo Statement Lines for Balance Sheet -->
        <record id="demo_line_assets_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">ASSETS</field>
            <field name="line_type">header</field>
            <field name="line_section">assets</field>
            <field name="sequence">10</field>
            <field name="bold">True</field>
            <field name="font_size">large</field>
            <field name="indent_level">0</field>
            <field name="current_amount">1766516.20</field>
            <field name="comparative_amount">1500000.00</field>
        </record>

        <record id="demo_line_current_assets" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Current Assets</field>
            <field name="line_type">header</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">20</field>
            <field name="bold">True</field>
            <field name="indent_level">1</field>
            <field name="current_amount">1766516.20</field>
            <field name="comparative_amount">1500000.00</field>
        </record>

        <record id="demo_line_cash" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Cash and Cash Equivalents</field>
            <field name="line_type">line</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">30</field>
            <field name="indent_level">2</field>
            <field name="current_amount">96130.37</field>
            <field name="comparative_amount">85000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_line_receivables" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Trade Receivables</field>
            <field name="line_type">line</field>
            <field name="line_section">current_assets</field>
            <field name="sequence">40</field>
            <field name="indent_level">2</field>
            <field name="current_amount">1670385.83</field>
            <field name="comparative_amount">1415000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_line_liabilities_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">LIABILITIES</field>
            <field name="line_type">header</field>
            <field name="line_section">liabilities</field>
            <field name="sequence">100</field>
            <field name="bold">True</field>
            <field name="font_size">large</field>
            <field name="indent_level">0</field>
            <field name="current_amount">735685.76</field>
            <field name="comparative_amount">625000.00</field>
        </record>

        <record id="demo_line_current_liabilities" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Current Liabilities</field>
            <field name="line_type">header</field>
            <field name="line_section">current_liabilities</field>
            <field name="sequence">110</field>
            <field name="bold">True</field>
            <field name="indent_level">1</field>
            <field name="current_amount">735685.76</field>
            <field name="comparative_amount">625000.00</field>
        </record>

        <record id="demo_line_payables" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Trade Payables</field>
            <field name="line_type">line</field>
            <field name="line_section">current_liabilities</field>
            <field name="sequence">120</field>
            <field name="indent_level">2</field>
            <field name="current_amount">400773.60</field>
            <field name="comparative_amount">340000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_line_accrued_liabilities" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Accrued Liabilities</field>
            <field name="line_type">line</field>
            <field name="line_section">current_liabilities</field>
            <field name="sequence">130</field>
            <field name="indent_level">2</field>
            <field name="current_amount">334912.16</field>
            <field name="comparative_amount">285000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_line_equity_header" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">EQUITY</field>
            <field name="line_type">header</field>
            <field name="line_section">equity</field>
            <field name="sequence">200</field>
            <field name="bold">True</field>
            <field name="font_size">large</field>
            <field name="indent_level">0</field>
            <field name="current_amount">1030830.44</field>
            <field name="comparative_amount">875000.00</field>
        </record>

        <record id="demo_line_retained_earnings" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Retained Earnings</field>
            <field name="line_type">line</field>
            <field name="line_section">equity</field>
            <field name="sequence">210</field>
            <field name="indent_level">1</field>
            <field name="current_amount">530130.44</field>
            <field name="comparative_amount">450000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_line_current_year_earnings" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="name">Current Year Earnings</field>
            <field name="line_type">line</field>
            <field name="line_section">equity</field>
            <field name="sequence">220</field>
            <field name="indent_level">1</field>
            <field name="current_amount">500700.00</field>
            <field name="comparative_amount">425000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <!-- Demo Ratio Analysis -->
        <record id="demo_ratio_current" model="financial.ratio.analysis">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="ratio_category">liquidity</field>
            <field name="ratio_name">Current Ratio</field>
            <field name="formula">Current Assets / Current Liabilities</field>
            <field name="ratio_value">2.40</field>
            <field name="comparative_ratio_value">2.20</field>
            <field name="benchmark_value">2.0</field>
            <field name="industry_average">1.8</field>
            <field name="description">Measures the ability to pay short-term obligations with current assets</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_ratio_quick" model="financial.ratio.analysis">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="ratio_category">liquidity</field>
            <field name="ratio_name">Quick Ratio</field>
            <field name="formula">(Current Assets - Inventory) / Current Liabilities</field>
            <field name="ratio_value">1.95</field>
            <field name="comparative_ratio_value">1.75</field>
            <field name="benchmark_value">1.0</field>
            <field name="industry_average">0.9</field>
            <field name="description">Measures the ability to pay short-term obligations with liquid assets</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_ratio_debt_equity" model="financial.ratio.analysis">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="ratio_category">leverage</field>
            <field name="ratio_name">Debt to Equity</field>
            <field name="formula">Total Debt / Total Equity</field>
            <field name="ratio_value">0.71</field>
            <field name="comparative_ratio_value">0.75</field>
            <field name="benchmark_value">0.5</field>
            <field name="industry_average">0.6</field>
            <field name="description">Measures the relative proportion of debt and equity financing</field>
            <field name="sequence">30</field>
        </record>

        <record id="demo_ratio_roe" model="financial.ratio.analysis">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="ratio_category">profitability</field>
            <field name="ratio_name">Return on Equity</field>
            <field name="formula">Net Income / Average Shareholders Equity × 100</field>
            <field name="ratio_value">18.5</field>
            <field name="comparative_ratio_value">16.2</field>
            <field name="benchmark_value">15.0</field>
            <field name="industry_average">12.5</field>
            <field name="description">Measures the return generated on shareholders equity</field>
            <field name="sequence">40</field>
        </record>

        <record id="demo_ratio_roa" model="financial.ratio.analysis">
            <field name="statement_id" ref="demo_balance_sheet_2024"/>
            <field name="ratio_category">profitability</field>
            <field name="ratio_name">Return on Assets</field>
            <field name="formula">Net Income / Average Total Assets × 100</field>
            <field name="ratio_value">11.2</field>
            <field name="comparative_ratio_value">9.8</field>
            <field name="benchmark_value">10.0</field>
            <field name="industry_average">8.5</field>
            <field name="description">Measures how efficiently assets are used to generate profit</field>
            <field name="sequence">50</field>
        </record>

        <!-- Demo Income Statement -->
        <record id="demo_income_statement_2024" model="financial.statement.analytics">
            <field name="name">Income Statement with Analytics 2024</field>
            <field name="statement_type">income_statement</field>
            <field name="reporting_period">annual</field>
            <field name="date_from">2024-01-01</field>
            <field name="date_to">2024-12-31</field>
            <field name="comparative_period">True</field>
            <field name="comparative_date_from">2023-01-01</field>
            <field name="comparative_date_to">2023-12-31</field>
            <field name="enable_analytics">True</field>
            <field name="enable_charts">True</field>
            <field name="enable_drill_down">True</field>
            <field name="state">generated</field>
        </record>

        <!-- Demo Income Statement Lines -->
        <record id="demo_income_revenue" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_income_statement_2024"/>
            <field name="name">Revenue</field>
            <field name="line_type">header</field>
            <field name="line_section">revenue</field>
            <field name="sequence">10</field>
            <field name="bold">True</field>
            <field name="current_amount">2500000.00</field>
            <field name="comparative_amount">2200000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_income_cost_sales" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_income_statement_2024"/>
            <field name="name">Cost of Sales</field>
            <field name="line_type">line</field>
            <field name="line_section">cost_of_sales</field>
            <field name="sequence">20</field>
            <field name="current_amount">-1600000.00</field>
            <field name="comparative_amount">-1450000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_income_gross_profit" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_income_statement_2024"/>
            <field name="name">Gross Profit</field>
            <field name="line_type">subtotal</field>
            <field name="line_section">gross_profit</field>
            <field name="sequence">30</field>
            <field name="bold">True</field>
            <field name="current_amount">900000.00</field>
            <field name="comparative_amount">750000.00</field>
        </record>

        <record id="demo_income_operating_expenses" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_income_statement_2024"/>
            <field name="name">Operating Expenses</field>
            <field name="line_type">header</field>
            <field name="line_section">operating_expenses</field>
            <field name="sequence">40</field>
            <field name="bold">True</field>
            <field name="current_amount">-400000.00</field>
            <field name="comparative_amount">-375000.00</field>
            <field name="is_expandable">True</field>
        </record>

        <record id="demo_income_net_income" model="financial.statement.line.analytics">
            <field name="statement_id" ref="demo_income_statement_2024"/>
            <field name="name">Net Income</field>
            <field name="line_type">total</field>
            <field name="line_section">net_income</field>
            <field name="sequence">100</field>
            <field name="bold">True</field>
            <field name="underline">True</field>
            <field name="current_amount">500000.00</field>
            <field name="comparative_amount">375000.00</field>
        </record>

    </data>
</odoo>
