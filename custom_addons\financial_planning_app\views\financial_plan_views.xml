<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Financial Plan Tree View -->
    <record id="view_financial_planning_plan_tree" model="ir.ui.view">
        <field name="name">financial.planning.plan.tree</field>
        <field name="model">financial.planning.plan</field>
        <field name="arch" type="xml">
            <tree string="Financial Plans" decoration-success="state=='active'" decoration-info="state=='approved'" decoration-muted="state=='cancelled'">
                <field name="name"/>
                <field name="launch_date"/>
                <field name="total_target_population" widget="float" digits="[16,1]"/>
                <field name="potential_market_size" widget="monetary"/>
                <field name="target_revenue_year_1" widget="monetary"/>
                <field name="annual_revenue_growth_rate" widget="percentage"/>
                <field name="forecast_count"/>
                <field name="state" widget="badge" decoration-success="state=='active'" decoration-info="state=='approved'" decoration-warning="state=='review'" decoration-muted="state=='cancelled'"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <!-- Financial Plan Form View -->
    <record id="view_financial_planning_plan_form" model="ir.ui.view">
        <field name="name">financial.planning.plan.form</field>
        <field name="model">financial.planning.plan</field>
        <field name="arch" type="xml">
            <form string="Financial Plan">
                <header>
                    <button name="action_generate_forecasts" string="Generate Forecasts" type="object" class="btn-primary" invisible="state in ['cancelled']"/>
                    <button name="action_approve" string="Approve" type="object" class="btn-secondary" invisible="state != 'review'"/>
                    <button name="action_activate" string="Activate" type="object" class="btn-success" invisible="state != 'approved'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,review,approved,active"/>
                </header>
                
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Financial Plan Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Basic Information">
                            <field name="description"/>
                            <field name="launch_date"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="base_currency_id"/>
                        </group>
                        <group string="Planning Period">
                            <field name="planning_start_date" readonly="1"/>
                            <field name="planning_end_date" readonly="1"/>
                            <field name="planning_months" readonly="1"/>
                            <field name="historical_months" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Target Markets">
                            <group>
                                <group string="Geographic Scope">
                                    <field name="continent_ids" widget="many2many_tags"/>
                                    <field name="country_ids" widget="many2many_tags"/>
                                    <field name="city_ids" widget="many2many_tags"/>
                                </group>
                                <group string="Market Metrics">
                                    <field name="total_target_population" widget="float" digits="[16,1]"/>
                                    <field name="target_population_coverage" widget="percentage"/>
                                    <field name="potential_market_size" widget="monetary"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Financial Targets">
                            <group>
                                <group string="Investment &amp; Revenue">
                                    <field name="initial_investment" widget="monetary"/>
                                    <field name="target_revenue_year_1" widget="monetary"/>
                                    <field name="target_revenue_year_5" widget="monetary"/>
                                    <field name="revenue_per_capita" widget="monetary"/>
                                </group>
                                <group string="Growth Assumptions">
                                    <field name="annual_revenue_growth_rate" widget="percentage"/>
                                    <field name="market_penetration_rate" widget="percentage"/>
                                    <field name="customer_acquisition_cost" widget="monetary"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Risk Assessment">
                            <group>
                                <field name="risk_level"/>
                                <field name="notes"/>
                            </group>
                        </page>
                        
                        <page string="Forecasts">
                            <field name="forecast_count" invisible="1"/>
                            <div invisible="forecast_count == 0" class="alert alert-info">
                                No forecasts generated yet. Click "Generate Forecasts" to create monthly projections.
                            </div>
                            <field name="forecast_ids" nolabel="1" invisible="forecast_count == 0">
                                <tree editable="bottom">
                                    <field name="period_date"/>
                                    <field name="period_type"/>
                                    <field name="months_from_launch"/>
                                    <field name="target_population" widget="float" digits="[16,1]"/>
                                    <field name="market_penetration" widget="percentage"/>
                                    <field name="revenue" widget="monetary"/>
                                    <field name="gross_profit" widget="monetary"/>
                                    <field name="net_cash_flow" widget="monetary"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Financial Plan Kanban View -->
    <record id="view_financial_planning_plan_kanban" model="ir.ui.view">
        <field name="name">financial.planning.plan.kanban</field>
        <field name="model">financial.planning.plan</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="launch_date"/>
                <field name="state"/>
                <field name="total_target_population"/>
                <field name="potential_market_size"/>
                <field name="base_currency_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div>Launch: <field name="launch_date"/></div>
                                        <div>Population: <field name="total_target_population" widget="float" digits="[16,1]"/>M</div>
                                        <div>Market Size: <field name="potential_market_size" widget="monetary"/></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Financial Plan Search View -->
    <record id="view_financial_planning_plan_search" model="ir.ui.view">
        <field name="name">financial.planning.plan.search</field>
        <field name="model">financial.planning.plan</field>
        <field name="arch" type="xml">
            <search string="Financial Plans">
                <field name="name"/>
                <field name="launch_date"/>
                <field name="country_ids"/>
                <field name="city_ids"/>
                <filter string="Active Plans" name="active_plans" domain="[('state', '=', 'active')]"/>
                <filter string="Approved Plans" name="approved_plans" domain="[('state', '=', 'approved')]"/>
                <filter string="Draft Plans" name="draft_plans" domain="[('state', '=', 'draft')]"/>
                <separator/>
                <filter string="This Year" name="this_year" domain="[('launch_date', '&gt;=', datetime.datetime.now().strftime('%Y-01-01')), ('launch_date', '&lt;=', datetime.datetime.now().strftime('%Y-12-31'))]"/>
                <filter string="Next Year" name="next_year" domain="[('launch_date', '&gt;=', (datetime.datetime.now() + relativedelta(years=1)).strftime('%Y-01-01')), ('launch_date', '&lt;=', (datetime.datetime.now() + relativedelta(years=1)).strftime('%Y-12-31'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Launch Date" name="group_launch_date" context="{'group_by': 'launch_date'}"/>
                    <filter string="Company" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Financial Plan Action -->
    <record id="action_financial_planning_plan" model="ir.actions.act_window">
        <field name="name">Financial Plans</field>
        <field name="res_model">financial.planning.plan</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_financial_planning_plan_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first financial plan!
            </p>
            <p>
                Financial plans help you forecast revenue, expenses, and growth based on 
                population demographics and market analysis. Create comprehensive 5-year 
                plans with monthly forecasts.
            </p>
        </field>
    </record>

</odoo>
