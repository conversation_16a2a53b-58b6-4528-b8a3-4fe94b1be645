from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from core.models import Company, Partner, Currency, Country
from ..serializers.core import (
    CompanySerializer, PartnerSerializer, CurrencySerializer, CountrySerializer
)


class CompanyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing companies.
    Provides CRUD operations for company management.
    """
    queryset = Company.objects.all()
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['currency_id', 'country_id']
    search_fields = ['name', 'email', 'phone']
    ordering_fields = ['name', 'create_date']
    ordering = ['name']
    
    @extend_schema(
        summary="Get company statistics",
        description="Returns statistics for the company including partner counts, etc."
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get company statistics"""
        company = self.get_object()
        
        # Calculate statistics
        partner_count = Partner.objects.filter(company_id=company).count()
        
        stats = {
            'partner_count': partner_count,
            'currency': company.currency_id.name if company.currency_id else None,
            'country': company.country_id.name if company.country_id else None,
        }
        
        return Response(stats)


class PartnerViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing partners (customers, vendors, etc.).
    Provides CRUD operations for partner management.
    """
    queryset = Partner.objects.all()
    serializer_class = PartnerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_company', 'supplier_rank', 'customer_rank', 'country_id']
    search_fields = ['name', 'email', 'phone', 'vat']
    ordering_fields = ['name', 'create_date', 'customer_rank', 'supplier_rank']
    ordering = ['name']
    
    @extend_schema(
        summary="Get customers only",
        description="Returns only partners that are customers (customer_rank > 0)"
    )
    @action(detail=False, methods=['get'])
    def customers(self, request):
        """Get customers only"""
        customers = self.queryset.filter(customer_rank__gt=0)
        page = self.paginate_queryset(customers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(customers, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="Get vendors only",
        description="Returns only partners that are vendors (supplier_rank > 0)"
    )
    @action(detail=False, methods=['get'])
    def vendors(self, request):
        """Get vendors only"""
        vendors = self.queryset.filter(supplier_rank__gt=0)
        page = self.paginate_queryset(vendors)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(vendors, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        summary="Get partner statistics",
        description="Returns statistics for the partner including order counts, amounts, etc."
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get partner statistics"""
        partner = self.get_object()
        
        # Import here to avoid circular imports
        try:
            from sales.models import SaleOrder
            sale_order_count = SaleOrder.objects.filter(partner_id=partner).count()
            total_sales = SaleOrder.objects.filter(partner_id=partner).aggregate(
                total=models.Sum('amount_total')
            )['total'] or 0
        except ImportError:
            sale_order_count = 0
            total_sales = 0
        
        try:
            from purchases.models import PurchaseOrder
            purchase_order_count = PurchaseOrder.objects.filter(partner_id=partner).count()
            total_purchases = PurchaseOrder.objects.filter(partner_id=partner).aggregate(
                total=models.Sum('amount_total')
            )['total'] or 0
        except ImportError:
            purchase_order_count = 0
            total_purchases = 0
        
        stats = {
            'sale_order_count': sale_order_count,
            'total_sales': float(total_sales),
            'purchase_order_count': purchase_order_count,
            'total_purchases': float(total_purchases),
            'is_customer': partner.customer_rank > 0,
            'is_vendor': partner.supplier_rank > 0,
        }
        
        return Response(stats)


class CurrencyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing currencies.
    Provides CRUD operations for currency management.
    """
    queryset = Currency.objects.all()
    serializer_class = CurrencySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'symbol']
    ordering_fields = ['name', 'create_date']
    ordering = ['name']
    
    @extend_schema(
        summary="Get active currencies",
        description="Returns only active currencies"
    )
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active currencies only"""
        active_currencies = self.queryset.filter(active=True)
        serializer = self.get_serializer(active_currencies, many=True)
        return Response(serializer.data)


class CountryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing countries.
    Provides CRUD operations for country management.
    """
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code']
    ordering_fields = ['name', 'code']
    ordering = ['name']
    
    @extend_schema(
        summary="Search countries by code",
        description="Search countries by their ISO code",
        parameters=[
            OpenApiParameter(
                name='code',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='ISO country code to search for'
            )
        ]
    )
    @action(detail=False, methods=['get'])
    def by_code(self, request):
        """Search countries by code"""
        code = request.query_params.get('code', '').upper()
        if not code:
            return Response({'error': 'Code parameter is required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        countries = self.queryset.filter(code__icontains=code)
        serializer = self.get_serializer(countries, many=True)
        return Response(serializer.data)
