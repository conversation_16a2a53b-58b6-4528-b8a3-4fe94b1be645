<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_form_group_fix" model="ir.ui.view">
        <field name="name">account.account.form.group.fix</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <field name="group_id" position="replace">
                <field name="group_id" readonly="0" force_save="1" 
                       options="{'no_create': False, 'no_open': False}"
                       placeholder="Select Account Group..."/>
            </field>
        </field>
    </record>

    <record id="view_account_list_group_fix" model="ir.ui.view">
        <field name="name">account.account.list.group.fix</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_list"/>
        <field name="arch" type="xml">
            <field name="group_id" position="replace">
                <field name="group_id" readonly="0" optional="show"/>
            </field>
        </field>
    </record>
</odoo>
