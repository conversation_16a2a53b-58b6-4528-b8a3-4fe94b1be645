# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_qweb_signer
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-08-17 02:52+0000\n"
"PO-Revision-Date: 2017-08-17 02:52+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Polish (https://www.transifex.com/oca/teams/23907/pl/)\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: report_qweb_signer
#: model_terms:ir.ui.view,arch_db:report_qweb_signer.report_partner_demo_document
msgid "<span>This is a sample report for testing PDF certificates.</span>"
msgstr ""

#. module: report_qweb_signer
#: model_terms:ir.ui.view,arch_db:report_qweb_signer.report_partner_demo_document
msgid "<strong>Partner:</strong>"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__allow_only_one
msgid "Allow only one document"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__action_report_ids
msgid "Allowed reports"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__path
msgid "Certificate file path"
msgstr ""

#. module: report_qweb_signer
#: model_terms:ir.ui.view,arch_db:report_qweb_signer.view_company_form
msgid "Certificates (PDF signing)"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model,name:report_qweb_signer.model_res_company
msgid "Companies"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__company_id
msgid "Company"
msgstr "Firma"

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__create_uid
msgid "Created by"
msgstr "Utworzone przez"

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__create_date
msgid "Created on"
msgstr "Utworzono"

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_ir_actions_report__display_name
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__display_name
#: model:ir.model.fields,field_description:report_qweb_signer.field_res_company__display_name
msgid "Display Name"
msgstr "Wyświetlana nazwa "

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__domain
msgid "Domain"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__domain
msgid "Domain for filtering if sign or not the document"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__endesive_certificate_mail
msgid "E-mail address to include in PDF digital signature."
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields.selection,name:report_qweb_signer.selection__report_certificate__signing_method__endesive
msgid "Endesive"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__attachment
msgid ""
"Filename used to store signed document as attachment. Keep empty to not save "
"signed document."
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_ir_actions_report__id
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__id
#: model:ir.model.fields,field_description:report_qweb_signer.field_res_company__id
msgid "ID"
msgstr "ID"

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__allow_only_one
msgid ""
"If True, this certificate can not be useb to sign a PDF from several "
"documents."
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields.selection,name:report_qweb_signer.selection__report_certificate__signing_method__java
msgid "Java"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_ir_actions_report____last_update
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate____last_update
#: model:ir.model.fields,field_description:report_qweb_signer.field_res_company____last_update
msgid "Last Modified on"
msgstr "Ostatnio modyfikowano"

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__write_uid
msgid "Last Updated by"
msgstr "Ostatnio modyfikowane przez"

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__write_date
msgid "Last Updated on"
msgstr "Ostatnia zmiana"

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__endesive_certificate_location
msgid "Location to include in digital signature (typically, a city name). "
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__model_id
msgid "Model"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__model_id
msgid "Model where apply this certificate"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__name
msgid "Name"
msgstr "Nazwa"

#. module: report_qweb_signer
#: model:ir.actions.act_window,name:report_qweb_signer.action_report_certificate
#: model:ir.ui.menu,name:report_qweb_signer.menu_report_certificate
msgid "PDF certificates"
msgstr ""

#. module: report_qweb_signer
#: model_terms:ir.ui.view,arch_db:report_qweb_signer.view_report_certificate_form
msgid "PDF report certificate"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_res_company__report_certificate_ids
msgid "PDF report certificates"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__password_file
msgid "Password file path"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__path
msgid "Path to PKCS#12 certificate file"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__password_file
msgid "Path to certificate password file"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__endesive_certificate_reason
msgid "Reason text to include in digital signature."
msgstr ""

#. module: report_qweb_signer
#: model:ir.model,name:report_qweb_signer.model_ir_actions_report
#, fuzzy
msgid "Report Action"
msgstr "Raport"

#. module: report_qweb_signer
#: model:ir.model,name:report_qweb_signer.model_report_certificate
msgid "Report Certificate"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,help:report_qweb_signer.field_report_certificate__action_report_ids
msgid ""
"Reports to sign for the selected model.No report selected means all reports "
"are allowed."
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__attachment
msgid "Save as attachment"
msgstr ""

#. module: report_qweb_signer
#: code:addons/report_qweb_signer/models/ir_actions_report.py:0
#, python-format
msgid ""
"Saving signed report (PDF): You do not have enough access rights to save "
"attachments"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__sequence
msgid "Sequence"
msgstr "Numeracja"

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__endesive_certificate_mail
msgid "Signature e-mail"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__endesive_certificate_location
msgid "Signature location"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__endesive_certificate_reason
msgid "Signature reason"
msgstr ""

#. module: report_qweb_signer
#: model:ir.model.fields,field_description:report_qweb_signer.field_report_certificate__signing_method
msgid "Signing Method"
msgstr ""

#. module: report_qweb_signer
#: code:addons/report_qweb_signer/models/ir_actions_report.py:0
#, python-format
msgid "Signing report (PDF): Certificate or password file not found"
msgstr ""

#. module: report_qweb_signer
#: code:addons/report_qweb_signer/models/ir_actions_report.py:0
#, python-format
msgid ""
"Signing report (PDF): jPdfSign failed (error code: %s). Message: %s. Output: "
"%s"
msgstr ""

#. module: report_qweb_signer
#: model:ir.actions.report,name:report_qweb_signer.partner_demo_report
msgid "Test PDF certificate"
msgstr ""
