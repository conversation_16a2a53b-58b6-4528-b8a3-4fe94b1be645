<!DOCTYPE html>
<html>
<head>
    <title>IFRS Module Access Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { display: block; margin: 10px 0; padding: 10px; background: #e8f4fd; border-radius: 5px; text-decoration: none; color: #2c3e50; }
        .test-link:hover { background: #d1ecf1; }
        .instructions { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 IFRS Module Access Test</h1>
    
    <div class="instructions">
        <h3>Instructions:</h3>
        <ol>
            <li>Make sure you're logged into Odoo (admin/admin)</li>
            <li>Enable Developer Mode in Settings</li>
            <li>Click the links below to test access</li>
        </ol>
    </div>

    <h3>🎯 Direct Access Links:</h3>
    
    <a href="http://localhost:8069/web#menu_id=menu_ifrs_main" class="test-link" target="_blank">
        📊 IFRS Main Menu
    </a>
    
    <a href="http://localhost:8069/web#action=action_ifrs_financial_statement" class="test-link" target="_blank">
        📋 Financial Statements List
    </a>
    
    <a href="http://localhost:8069/web#action=base.open_module_tree" class="test-link" target="_blank">
        📦 Apps (to check module status)
    </a>
    
    <a href="http://localhost:8069/web#action=base.action_res_users" class="test-link" target="_blank">
        👤 Users (to set permissions)
    </a>

    <h3>🔧 What to Expect:</h3>
    <ul>
        <li><strong>IFRS Main Menu</strong> - Should show the IFRS dashboard</li>
        <li><strong>Financial Statements</strong> - Should show list view to create statements</li>
        <li><strong>Apps</strong> - Search "IFRS" to see module status</li>
        <li><strong>Users</strong> - Set IFRS permissions for admin user</li>
    </ul>

    <h3>🚨 If Links Don't Work:</h3>
    <ol>
        <li>Check you're logged in as admin</li>
        <li>Enable Developer Mode in Settings</li>
        <li>Set user permissions (Users → Administrator → Access Rights)</li>
        <li>Try refreshing the page</li>
    </ol>

    <div class="instructions">
        <h4>📞 Quick Test:</h4>
        <p>If the first link works, your IFRS module is accessible!</p>
        <p>If not, follow the troubleshooting steps above.</p>
    </div>
</body>
</html>
