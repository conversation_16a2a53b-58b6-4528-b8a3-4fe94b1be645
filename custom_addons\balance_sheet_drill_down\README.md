# Balance Sheet with Drill-Down

This module adds drill-down functionality to the **standard Odoo Balance Sheet** report from the `base_accounting_kit` module.

## Features

✅ **Uses Standard Balance Sheet Format** - Exactly like your screenshot  
✅ **Drill-Down Navigation** - Click amounts to see account details  
✅ **Transaction Level Access** - Navigate to individual journal entries  
✅ **Professional Styling** - Clean, clickable interface  
✅ **No Layout Changes** - Keeps the existing Balance Sheet structure  

## How to Use

1. **Install the Module**
   - Go to Apps → Search for "Balance Sheet with Drill-Down"
   - Click Install

2. **Access the Report**
   - Go to Accounting → Reporting → Balance Sheet (Drill-Down)
   - Or use the existing Balance Sheet menu (it will be enhanced automatically)

3. **Drill-Down Navigation**
   - Click on any **blue amount** in the Balance Sheet
   - This opens a new window showing all journal entries for that account
   - You can then click on individual entries to see full transaction details

## What You Get

### Standard Balance Sheet Format
- **ASSETS** section with proper indentation
- **LIABILITIES** section 
- **EQUITY** section
- Same layout as your screenshot

### Enhanced with Drill-Down
- All amounts become **clickable** (shown in blue)
- Hover effects show you can click
- Opens account details in new window
- Shows journal entries filtered by date range
- Navigate to individual transactions

## Technical Details

- **Inherits**: `base_accounting_kit.report_financial` template
- **Adds**: JavaScript click handlers and CSS styling
- **Compatible**: Works with existing Balance Sheet data
- **No Data Changes**: Uses your existing accounting data

## Example Usage

1. Generate Balance Sheet for December 2023
2. See "Current Assets" showing $50,000
3. **Click** on the $50,000 amount
4. New window opens showing all Current Asset account entries
5. Click on any journal entry to see full transaction details

This gives you the **exact format** you showed in your screenshot, but with the ability to drill down to transaction level details!
