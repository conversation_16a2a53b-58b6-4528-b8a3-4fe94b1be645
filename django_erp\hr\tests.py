from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timedelta, date
from django.utils import timezone
from core.models import Company, Country, Currency
from .models import (
    HrD<PERSON>artment, HrJob, HrContractType, HrEmployee, 
    HrContract, HrAttendance, HrLeaveType, HrLeave
)


class HrModelsTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username='hruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.employee_user = User.objects.create_user(
            username='employee',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create country and currency
        self.country = Country.objects.create(
            name='United States',
            code='US',
            create_uid=self.user,
            write_uid=self.user
        )

        self.currency = Currency.objects.create(
            name='USD',
            symbol='$',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create company
        self.company = Company.objects.create(
            name='Test HR Company',
            code='THC',
            currency_id=self.currency,
            country_id=self.country,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create department
        self.department = HrDepartment.objects.create(
            name='Human Resources',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create contract type
        self.contract_type = HrContractType.objects.create(
            name='Full-time',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create job position
        self.job = HrJob.objects.create(
            name='HR Manager',
            department_id=self.department,
            company_id=self.company,
            contract_type_id=self.contract_type,
            create_uid=self.user,
            write_uid=self.user
        )

        # Create employee
        self.employee = HrEmployee.objects.create(
            name='John Doe',
            employee_number='EMP001',
            company_id=self.company,
            department_id=self.department,
            job_id=self.job,
            user_id=self.employee_user,
            work_email='<EMAIL>',
            create_uid=self.user,
            write_uid=self.user
        )

        # Create leave type
        self.leave_type = HrLeaveType.objects.create(
            name='Annual Leave',
            allocation_type='fixed',
            max_leaves=25.0,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )

    def test_department_creation_and_hierarchy(self):
        """Test department creation and hierarchy validation"""
        # Create parent department
        parent_dept = HrDepartment.objects.create(
            name='Operations',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create child department
        child_dept = HrDepartment.objects.create(
            name='IT Operations',
            parent_id=parent_dept,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(child_dept.parent_id, parent_dept)
        self.assertEqual(child_dept.name, 'IT Operations')

    def test_job_position_creation(self):
        """Test job position creation and validation"""
        job = HrJob.objects.create(
            name='Software Developer',
            department_id=self.department,
            company_id=self.company,
            no_of_recruitment=3,
            state='recruit',
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(job.name, 'Software Developer')
        self.assertEqual(job.no_of_recruitment, 3)
        self.assertEqual(job.state, 'recruit')
        self.assertEqual(job.no_of_hired_employee, 0)

    def test_employee_creation_and_validation(self):
        """Test employee creation and validation"""
        employee = HrEmployee.objects.create(
            name='Jane Smith',
            employee_number='EMP002',
            company_id=self.company,
            department_id=self.department,
            job_id=self.job,
            gender='female',
            marital='single',
            work_email='<EMAIL>',
            phone='+1234567890',
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(employee.name, 'Jane Smith')
        self.assertEqual(employee.employee_number, 'EMP002')
        self.assertEqual(employee.gender, 'female')
        self.assertEqual(employee.department_id, self.department)

    def test_employee_manager_validation(self):
        """Test employee manager validation"""
        # Create manager
        manager = HrEmployee.objects.create(
            name='Manager',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Assign manager to employee
        self.employee.parent_id = manager
        self.employee.save()
        
        self.assertEqual(self.employee.parent_id, manager)
        
        # Test self-manager validation
        with self.assertRaises(ValidationError):
            self.employee.parent_id = self.employee
            self.employee.full_clean()

    def test_contract_creation_and_lifecycle(self):
        """Test contract creation and lifecycle management"""
        contract = HrContract.objects.create(
            name='Contract 001',
            employee_id=self.employee,
            company_id=self.company,
            type_id=self.contract_type,
            job_id=self.job,
            department_id=self.department,
            date_start=date.today(),
            wage=Decimal('5000.00'),
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(contract.state, 'draft')
        self.assertEqual(contract.wage, Decimal('5000.00'))
        self.assertEqual(contract.currency_id, self.currency)  # From company
        
        # Test contract start
        contract.action_start()
        self.assertEqual(contract.state, 'open')
        
        # Test contract close
        contract.action_close()
        self.assertEqual(contract.state, 'close')
        self.assertIsNotNone(contract.date_end)

    def test_contract_date_validation(self):
        """Test contract date validation"""
        # Test invalid dates
        with self.assertRaises(ValidationError):
            contract = HrContract(
                name='Invalid Contract',
                employee_id=self.employee,
                company_id=self.company,
                date_start=date.today(),
                date_end=date.today() - timedelta(days=1),  # End before start
                create_uid=self.user,
                write_uid=self.user
            )
            contract.full_clean()

    def test_attendance_tracking(self):
        """Test attendance tracking functionality"""
        # Create check-in
        check_in_time = timezone.now()
        attendance = HrAttendance.objects.create(
            employee_id=self.employee,
            check_in=check_in_time,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(attendance.worked_hours, 0.0)  # No check-out yet
        
        # Check out after 8 hours
        check_out_time = check_in_time + timedelta(hours=8)
        attendance.check_out = check_out_time
        attendance.save()
        
        self.assertEqual(attendance.worked_hours, 8.0)

    def test_attendance_validation(self):
        """Test attendance time validation"""
        check_in_time = timezone.now()
        
        # Test invalid check-out time
        with self.assertRaises(ValidationError):
            attendance = HrAttendance(
                employee_id=self.employee,
                check_in=check_in_time,
                check_out=check_in_time - timedelta(hours=1),  # Before check-in
                create_uid=self.user,
                write_uid=self.user
            )
            attendance.full_clean()

    def test_leave_type_creation(self):
        """Test leave type creation"""
        leave_type = HrLeaveType.objects.create(
            name='Sick Leave',
            allocation_type='no',
            validation_type='manager',
            company_id=self.company,
            color_name='red',
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(leave_type.name, 'Sick Leave')
        self.assertEqual(leave_type.validation_type, 'manager')
        self.assertEqual(leave_type.color_name, 'red')

    def test_leave_request_creation_and_approval(self):
        """Test leave request creation and approval workflow"""
        # Create manager for employee
        manager = HrEmployee.objects.create(
            name='Manager',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        self.employee.parent_id = manager
        self.employee.save()
        
        # Create leave request
        leave_request = HrLeave.objects.create(
            name='Annual Leave Request',
            employee_id=self.employee,
            holiday_status_id=self.leave_type,
            request_date_from=date.today() + timedelta(days=7),
            request_date_to=date.today() + timedelta(days=11),
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        self.assertEqual(leave_request.state, 'draft')
        self.assertEqual(leave_request.number_of_days, 5.0)  # 5 days
        self.assertEqual(leave_request.manager_id, manager)  # Auto-assigned
        
        # Test approval workflow
        leave_request.state = 'confirm'
        leave_request.action_approve()
        self.assertEqual(leave_request.state, 'validate')

    def test_leave_date_validation(self):
        """Test leave request date validation"""
        # Test invalid dates
        with self.assertRaises(ValidationError):
            leave_request = HrLeave(
                name='Invalid Leave',
                employee_id=self.employee,
                holiday_status_id=self.leave_type,
                request_date_from=date.today(),
                request_date_to=date.today() - timedelta(days=1),  # End before start
                company_id=self.company,
                create_uid=self.user,
                write_uid=self.user
            )
            leave_request.full_clean()

    def test_employee_current_contract(self):
        """Test employee current contract retrieval"""
        # Create active contract
        contract = HrContract.objects.create(
            name='Current Contract',
            employee_id=self.employee,
            company_id=self.company,
            date_start=date.today() - timedelta(days=30),
            state='open',
            create_uid=self.user,
            write_uid=self.user
        )
        
        current_contract = self.employee.get_current_contract()
        self.assertEqual(current_contract, contract)

    def test_contract_employee_sync(self):
        """Test contract and employee job/department synchronization"""
        # Create new job and department
        new_dept = HrDepartment.objects.create(
            name='IT Department',
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        new_job = HrJob.objects.create(
            name='Developer',
            department_id=new_dept,
            company_id=self.company,
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Create contract with new job/department
        contract = HrContract.objects.create(
            name='New Contract',
            employee_id=self.employee,
            company_id=self.company,
            job_id=new_job,
            department_id=new_dept,
            date_start=date.today(),
            state='open',  # This should sync employee's job/department
            create_uid=self.user,
            write_uid=self.user
        )
        
        # Refresh employee from database
        self.employee.refresh_from_db()
        
        # Check if employee's job and department were updated
        self.assertEqual(self.employee.job_id, new_job)
        self.assertEqual(self.employee.department_id, new_dept)
