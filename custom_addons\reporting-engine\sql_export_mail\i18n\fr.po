# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sql_export_mail
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 8.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-07-18 13:15+0000\n"
"PO-Revision-Date: 2017-07-18 13:15+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: sql_export_mail
#: model:mail.template,body_html:sql_export_mail.sql_export_mailer
msgid ""
"<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; \">\n"
"\n"
"    <p>You will find the report <t t-out=\"object.name or ''\"></t> as an "
"attachment of the mail.</p>\n"
"\n"
"</div>\n"
"        "
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields,help:sql_export_mail.field_sql_export__mail_user_ids
msgid ""
"Add the users who want to receive the report by e-mail. You need to link the "
"sql query with a cron to send mail automatically"
msgstr ""
"Ajoutez les utilisateurs voulant recevoir le rapport par mail. Vous devez "
"ensuite créer une tâche planifiée pour envoyer le mail automatiquement."

#. module: sql_export_mail
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Create Cron"
msgstr "Créer une tâche planfiée"

#. module: sql_export_mail
#: model:ir.model.fields,field_description:sql_export_mail.field_sql_export__cron_ids
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Crons"
msgstr "Tâches planifiées"

#. module: sql_export_mail
#: model:ir.model.fields.selection,name:sql_export_mail.selection__sql_export__mail_condition__not_empty
msgid "File Not Empty"
msgstr "Fichier non vide."

#. module: sql_export_mail
#. odoo-python
#: code:addons/sql_export_mail/models/sql_export.py:0
#, python-format
msgid ""
"It is not possible to execute and send a query automatically by mail if "
"there are parameters to fill"
msgstr ""
"Il n'est pas possible d'exécuter en envoyer le résultat d'une requête par "
"mail si celle-ci contient des paramètres."

#. module: sql_export_mail
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Mail"
msgstr ""

#. module: sql_export_mail
#: model:ir.model.fields,field_description:sql_export_mail.field_sql_export__mail_condition
#, fuzzy
msgid "Mail Condition"
msgstr "Condition d'envoi par mail"

#. module: sql_export_mail
#: model:mail.template,name:sql_export_mail.sql_export_mailer
msgid "SQL Export"
msgstr ""

#. module: sql_export_mail
#: model:ir.model,name:sql_export_mail.model_sql_export
msgid "SQL export"
msgstr "Export SQL"

#. module: sql_export_mail
#. odoo-python
#: code:addons/sql_export_mail/models/sql_export.py:0
#, python-format
msgid "The user does not have any e-mail address."
msgstr "L'utilisateur selectionné n'a pas d'addresse mail."

#. module: sql_export_mail
#: model:ir.model.fields,field_description:sql_export_mail.field_sql_export__mail_user_ids
msgid "User to notify"
msgstr "Utilisateurs à notifier par mail"

#. module: sql_export_mail
#: model_terms:ir.ui.view,arch_db:sql_export_mail.sql_export_mail_view_form
msgid "Users Notified by e-mail"
msgstr "Utilisateurs notifiés par mail"

#. module: sql_export_mail
#: model:mail.template,subject:sql_export_mail.sql_export_mailer
msgid "{{object.name or ''}}"
msgstr ""

#, fuzzy
#~ msgid ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
#~ "serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; "
#~ "\">\n"
#~ "\n"
#~ "<p>You will find the report ${object.name or ''} as an attachment of the "
#~ "mail.</p>\n"
#~ "\n"
#~ "</div>\n"
#~ "        "
#~ msgstr ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
#~ "serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; "
#~ "\">\n"
#~ "\n"
#~ "<p>Vous trouverez le rapport ${object.name or ''} en pièce jointe de ce "
#~ "mail.</p>\n"
#~ "\n"
#~ "</div>\n"
#~ "    "

#~ msgid "${object.name or ''}"
#~ msgstr "${object.name or ''}"
