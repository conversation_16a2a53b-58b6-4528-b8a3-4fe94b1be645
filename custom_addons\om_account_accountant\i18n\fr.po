# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_accountant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-12 03:13+0000\n"
"PO-Revision-Date: 2022-07-05 23:59+0200\n"
"Last-Translator: <PERSON>ylvain Lc\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"X-Generator: Poedit 3.1\n"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_group_action
#: model:ir.ui.menu,name:om_account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Groupes de comptes"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_account_tag
#: model:ir.ui.menu,name:om_account_accountant.menu_account_tag
msgid "Account Tags"
msgstr "Balises de compte"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.account_type_menu
msgid "Account Types"
msgstr "Types de compte"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_partner_property_form
msgid "Accounting"
msgstr "Comptabilité"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid "Anglo-Saxon Accounting"
msgstr "Comptabilité anglo-saxone"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_bank
msgid "Bank Statements"
msgstr "Relevés bancaires"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_bank_and_cash
msgid "Bank and Cash"
msgstr "Banque et liquidités"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_accounting_statement_cash
msgid "Cash Registers"
msgstr "Caisses"

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_coa_template
msgid "Chart of Accounts Templates"
msgstr "Modèles de plans de comptes"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de Configuration"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_fiscal_position_template
#: model:ir.ui.menu,name:om_account_accountant.menu_account_fiscal_position_template
msgid "Fiscal Position Templates"
msgstr "Modèles de situation financière"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Group By"
msgstr "Grouper par"

#. module: om_account_accountant
#: model:ir.model,name:om_account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Method"
msgstr "Moyen de paiement"

#. module: om_account_accountant
#: model:ir.actions.act_window,name:om_account_accountant.action_account_payment_method
#: model:ir.ui.menu,name:om_account_accountant.menu_account_payment_method
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_form
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_tree
msgid "Payment Methods"
msgstr "Méthodes de paiement"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.view_account_payment_method_search
msgid "Payment Type"
msgstr "Type de paiement"

#. module: om_account_accountant
#: model:ir.actions.server,name:om_account_accountant.action_account_reconciliation
msgid "Reconcile"
msgstr "Lettrer"

#. module: om_account_accountant
#: model_terms:ir.ui.view,arch_db:om_account_accountant.res_config_settings_view_form
msgid ""
"Record the cost of a good as an expense when this good is\n"
"                                invoiced to a final customer (instead of "
"recording the cost as soon\n"
"                                as the product is received in stock)."
msgstr ""
"Enregistrer le coût d’un bien comme une dépense lorsque ce bien est\n"
"                                facturé à un client final (au lieu "
"d’enregistrer le coût dès que possible)\n"
"                                au fur et à mesure que le produit est reçu "
"en stock)."

#. module: om_account_accountant
#: model:ir.model.fields,help:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid ""
"Record the cost of a good as an expense when this good is invoiced to a "
"final customer."
msgstr ""
"Enregistrez le coût d’un bien comme une dépense lorsque ce bien est facturé "
"à un client final."

#. module: om_account_accountant
#: model:ir.ui.menu,name:om_account_accountant.menu_account_templates
msgid "Templates"
msgstr "Modèles"

#. module: om_account_accountant
#: model:ir.model.fields,field_description:om_account_accountant.field_res_config_settings__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Utiliser la comptabilité anglo-saxone"
