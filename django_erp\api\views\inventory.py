from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

# Placeholder for inventory API views
# TODO: Implement full inventory API

class ProductViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class ProductTemplateViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class StockMoveViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class StockPickingViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None
