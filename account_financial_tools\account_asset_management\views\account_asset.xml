<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record model="ir.ui.view" id="account_asset_view_form">
        <field name="name">account.asset.form</field>
        <field name="model">account.asset</field>
        <field name="arch" type="xml">
            <form string="Asset">
                <header>
                    <button
                        name="validate"
                        string="Confirm Asset"
                        type="object"
                        class="oe_highlight"
                        invisible="state != 'draft'"
                    />
                    <button
                        name="set_to_draft"
                        invisible="state not in ['open','close']"
                        string="Set to Draft"
                        type="object"
                        groups="account.group_account_manager"
                    />
                    <button
                        name="remove"
                        string="Remove"
                        type="object"
                        groups="account.group_account_manager"
                        invisible="state not in ['open', 'close']"
                        help="Asset removal."
                    />
                    <field
                        name="state"
                        widget="statusbar"
                        statusbar_visible="draft,open,close,removed"
                    />
                </header>
                <sheet>
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <div class="oe_button_box" name="button_box">
                        <button
                            name="open_entries"
                            string="Journal Entries"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-bars"
                        />
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" />
                        <h1>
                            <field
                                name="name"
                                class="oe_inline"
                                readonly="state in ['open', 'close', 'removed']"
                            />
                        </h1>
                    </div>
                    <group>
                        <group id="header_left_group">
                            <field name="company_id" invisible="1" />
                            <field
                                name="code"
                                readonly="state in ['open', 'close', 'removed']"
                            />
                            <field
                                name="company_id"
                                widget="selection"
                                groups="base.group_multi_company"
                            />
                            <field
                                name="currency_id"
                                groups="base.group_multi_currency"
                                invisible="1"
                            />
                            <field name="move_line_check" invisible="1" />
                        </group>
                        <group id="header_right_group">
                            <field name="depreciation_base" />
                            <field name="value_depreciated" />
                            <field name="value_residual" />
                            <field name="active" invisible="1" />
                        </group>
                    </group>
                    <notebook colspan="4">
                        <page string="General">
                            <group>
                                <group>
                                    <field
                                        name="purchase_value"
                                        readonly="move_line_check or state != 'draft'"
                                    />
                                    <field
                                        name="date_start"
                                        readonly="state != 'draft'"
                                    />
                                </group>
                                <group>
                                    <field
                                        name="salvage_value"
                                        readonly="state != 'draft'"
                                    />
                                    <field name="date_remove" />
                                </group>
                            </group>
                            <group string="Other Information">
                                <group>
                                    <field
                                        name="profile_id"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field name="group_ids" widget="many2many_tags" />
                                </group>
                                <group>
                                    <field
                                        name="partner_id"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field
                                        name="analytic_distribution"
                                        groups="analytic.group_analytic_accounting"
                                        widget="analytic_distribution"
                                    />
                                </group>
                            </group>
                            <group>
                                <group string="Depreciation Dates">
                                    <field
                                        name="method_time"
                                        required="1"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field
                                        name="method_number"
                                        invisible="method_time not in ['number', 'year']"
                                        required="method_time in ['number', 'year']"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field
                                        name="method_period"
                                        required="1"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field
                                        name="method_end"
                                        invisible="method_time in ['number']"
                                        required="method_time == 'end'"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field name="days_calc" />
                                    <field
                                        name="use_leap_years"
                                        invisible="days_calc"
                                    />
                                </group>
                                <group string="Depreciation Method">
                                    <field
                                        name="method"
                                        required="1"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field
                                        name="method_progress_factor"
                                        invisible="method in ['linear', 'linear-limit']"
                                        required="method in ['degressive', 'degr-linear', 'degr-limit']"
                                        readonly="state in ['open', 'close', 'removed']"
                                    />
                                    <field
                                        name="prorata"
                                        readonly="method_time != 'year' or state in ['open', 'close', 'removed']"
                                    />
                                    <field name="carry_forward_missed_depreciations" />
                                </group>
                            </group>
                        </page>
                        <page string="Depreciation Board">
                            <div>
                                <button
                                    type="object"
                                    name="compute_depreciation_board"
                                    string="Compute"
                                    icon="fa-gears"
                                    invisible="state in ['close', 'removed']"
                                />
                            </div>
                            <field
                                name="depreciation_line_ids"
                                mode="tree"
                                options="{'reload_on_button': true}"
                                readonly="state in ['open', 'close', 'removed']"
                            >
                                <tree
                                    decoration-info="(move_check == False) and (init_entry == False)"
                                    create="false"
                                >
                                    <field name="type" />
                                    <field name="line_date" />
                                    <field name="line_days" sum="Total Days" />
                                    <field name="depreciated_value" readonly="1" />
                                    <field name="amount" />
                                    <field name="remaining_value" readonly="1" />
                                    <field name="init_entry" string="Init" />
                                    <field name="move_check" />
                                    <field name="parent_state" column_invisible="1" />
                                    <field name="currency_id" column_invisible="1" />
                                    <button
                                        name="create_move"
                                        icon="fa-cog"
                                        string="Create Move"
                                        type="object"
                                        invisible="init_entry or move_check or parent_state != 'open'"
                                    />
                                    <button
                                        name="open_move"
                                        icon="fa-folder-open-o"
                                        string="View Move"
                                        type="object"
                                        invisible="not move_check"
                                    />
                                    <button
                                        name="unlink_move"
                                        icon="fa-times"
                                        string="Delete/Reverse Move"
                                        type="object"
                                        confirm="Are you sure ?"
                                        groups="account.group_account_manager"
                                        invisible="not move_check"
                                    />
                                </tree>
                                <form string="Asset Line">
                                    <group>
                                        <group>
                                            <field name="parent_state" invisible="1" />
                                            <field
                                                name="depreciation_base"
                                                invisible="1"
                                            />
                                            <field name="currency_id" invisible="1" />
                                            <field name="type" />
                                            <field name="name" />
                                            <field
                                                name="amount"
                                                readonly="move_check"
                                            />
                                            <field
                                                name="init_entry"
                                                readonly="move_check or type == 'create'"
                                            />
                                            <field name="move_id" />
                                            <field name="move_check" />
                                        </group>
                                        <group>
                                            <field
                                                name="line_date"
                                                readonly="move_check"
                                            />
                                            <field
                                                name="depreciated_value"
                                                readonly="1"
                                            />
                                            <field
                                                name="remaining_value"
                                                readonly="1"
                                            />
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="History">
                            <field name="account_move_line_ids" readonly="1">
                                <tree>
                                    <field name="date" />
                                    <field name="move_id" />
                                    <field name="journal_id" optional="show" />
                                    <field name="account_id" />
                                    <field name="partner_id" optional="show" />
                                    <field name="ref" />
                                    <field name="name" optional="hide" />
                                    <field name="debit" sum="1" />
                                    <field name="credit" sum="1" />
                                </tree>
                            </field>
                        </page>
                        <page string="Notes">
                            <field
                                name="note"
                                placeholder="Add an internal note here..."
                            />
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" />
                    <field name="activity_ids" />
                    <field name="message_ids" />
                </div>
            </form>
        </field>
    </record>
    <record model="ir.ui.view" id="account_asset_view_tree">
        <field name="name">account.asset.tree</field>
        <field name="model">account.asset</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="code" optional="show" />
                <field name="depreciation_base" optional="show" />
                <field name="value_depreciated" optional="show" />
                <field name="value_residual" optional="show" />
                <field name="date_start" optional="show" />
                <field name="date_remove" optional="show" />
                <field name="profile_id" optional="show" />
                <field name="method" optional="hide" />
                <field name="prorata" optional="hide" />
                <field name="group_ids" widget="many2many_tags" optional="hide" />
                <field
                    name="company_id"
                    groups="base.group_multi_company"
                    optional="show"
                />
                <field
                    name="state"
                    widget="badge"
                    decoration-success="state == 'open'"
                    decoration-warning="state == 'removed'"
                    decoration-info="state == 'draft'"
                    decoration-muted="state == 'close'"
                />
                <field name="currency_id" column_invisible="1" />
            </tree>
        </field>
    </record>
    <record id="account_asset_view_search" model="ir.ui.view">
        <field name="name">account.asset.search</field>
        <field name="model">account.asset</field>
        <field name="arch" type="xml">
            <search string="Account Asset">
                <filter
                    string="Draft"
                    name="draft"
                    domain="[('state', '=', 'draft')]"
                />
                <filter
                    string="Running"
                    name="running"
                    domain="[('state', '=', 'open')]"
                />
                <filter
                    string="Close"
                    name="close"
                    domain="[('state', '=', 'close')]"
                />
                <filter
                    string="Removed"
                    name="removed"
                    domain="[('state', '=', 'removed')]"
                />
                <separator />
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active', '=', False)]"
                />
                <separator />
                <field name="name" />
                <field name="code" />
                <field name="date_start" />
                <field name="profile_id" />
                <field name="group_ids" />
                <field
                    name="partner_id"
                    filter_domain="[('partner_id', 'child_of', self)]"
                />
                <group name="groupby">
                    <filter
                        string="Profile"
                        name="profile"
                        domain=""
                        context="{'group_by': 'profile_id'}"
                    />
                    <filter
                        string="Status"
                        name="state_groupby"
                        domain=""
                        context="{'group_by': 'state'}"
                    />
                    <filter
                        string="Computation Method"
                        name="method_groupby"
                        domain=""
                        context="{'group_by': 'method'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="account_asset_action" model="ir.actions.act_window">
        <field name="name">Assets</field>
        <field name="res_model">account.asset</field>
        <field name="view_mode">tree,form</field>
    </record>
    <record id="act_entries_open" model="ir.actions.act_window">
        <field name="name">Journal Items</field>
        <field name="binding_model_id" ref="model_account_asset" />
        <field name="binding_view_types">form</field>
        <field name="res_model">account.move.line</field>
        <field
            name="domain"
        >['|',('asset_id','in',context.get('active_ids')),('asset_id','=','id')]</field>
    </record>
</odoo>
