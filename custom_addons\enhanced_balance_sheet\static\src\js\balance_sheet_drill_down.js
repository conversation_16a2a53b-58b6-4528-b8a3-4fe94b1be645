/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * Enhanced Balance Sheet Drill-Down Component
 */
class BalanceSheetDrillDown extends Component {
    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.notification = useService("notification");
        
        this.state = useState({
            loading: false,
            expandedLines: new Set(),
        });
    }

    /**
     * Handle drill-down to account level
     */
    async drillDownAccounts(lineId, lineName) {
        this.state.loading = true;
        
        try {
            const result = await this.orm.call(
                "enhanced.balance.sheet.line",
                "action_drill_down_accounts",
                [lineId]
            );
            
            if (result) {
                result.name = `Accounts - ${lineName}`;
                await this.action.doAction(result);
            }
        } catch (error) {
            this.notification.add(
                `Error opening accounts: ${error.message}`,
                { type: "danger" }
            );
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Handle drill-down to ledger level
     */
    async drillDownLedger(lineId, lineName) {
        this.state.loading = true;
        
        try {
            const result = await this.orm.call(
                "enhanced.balance.sheet.line",
                "action_drill_down_ledger",
                [lineId]
            );
            
            if (result) {
                result.name = `Ledger - ${lineName}`;
                await this.action.doAction(result);
            }
        } catch (error) {
            this.notification.add(
                `Error opening ledger: ${error.message}`,
                { type: "danger" }
            );
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Handle drill-down to journal entries level
     */
    async drillDownEntries(lineId, lineName) {
        this.state.loading = true;
        
        try {
            const result = await this.orm.call(
                "enhanced.balance.sheet.line",
                "action_drill_down_entries",
                [lineId]
            );
            
            if (result) {
                result.name = `Journal Entries - ${lineName}`;
                await this.action.doAction(result);
            }
        } catch (error) {
            this.notification.add(
                `Error opening journal entries: ${error.message}`,
                { type: "danger" }
            );
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Toggle line expansion for detailed view
     */
    toggleLineExpansion(lineId) {
        if (this.state.expandedLines.has(lineId)) {
            this.state.expandedLines.delete(lineId);
        } else {
            this.state.expandedLines.add(lineId);
        }
    }

    /**
     * Check if line is expanded
     */
    isLineExpanded(lineId) {
        return this.state.expandedLines.has(lineId);
    }

    /**
     * Format currency amount
     */
    formatCurrency(amount, currency) {
        if (!currency) return amount.toFixed(2);
        
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency.name || 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    /**
     * Get CSS class for line type
     */
    getLineClass(lineType, section, bold, underline) {
        let classes = [`line-${lineType}`];
        
        if (section) {
            classes.push(`section-${section}`);
        }
        
        if (bold) {
            classes.push('font-weight-bold');
        }
        
        if (underline) {
            classes.push('underline');
        }
        
        return classes.join(' ');
    }

    /**
     * Get variance color class
     */
    getVarianceClass(variance) {
        if (variance > 0) return 'text-success';
        if (variance < 0) return 'text-danger';
        return 'text-muted';
    }

    /**
     * Export balance sheet to Excel
     */
    async exportToExcel(balanceSheetId) {
        this.state.loading = true;
        
        try {
            const result = await this.orm.call(
                "enhanced.balance.sheet",
                "action_export_excel",
                [balanceSheetId]
            );
            
            if (result && result.url) {
                // Download the file
                const link = document.createElement('a');
                link.href = result.url;
                link.download = result.filename || 'balance_sheet.xlsx';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.notification.add(
                    "Balance Sheet exported successfully!",
                    { type: "success" }
                );
            }
        } catch (error) {
            this.notification.add(
                `Error exporting to Excel: ${error.message}`,
                { type: "danger" }
            );
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Print balance sheet report
     */
    async printReport(balanceSheetId) {
        this.state.loading = true;
        
        try {
            const result = await this.orm.call(
                "enhanced.balance.sheet",
                "action_print_report",
                [balanceSheetId]
            );
            
            if (result) {
                await this.action.doAction(result);
            }
        } catch (error) {
            this.notification.add(
                `Error printing report: ${error.message}`,
                { type: "danger" }
            );
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Validate balance sheet (Assets = Liabilities + Equity)
     */
    validateBalance(totalAssets, totalLiabilities, totalEquity) {
        const tolerance = 0.01; // Allow small rounding differences
        const difference = Math.abs(totalAssets - (totalLiabilities + totalEquity));
        
        return {
            isBalanced: difference <= tolerance,
            difference: difference,
            message: difference <= tolerance 
                ? "Balance Sheet is balanced ✓" 
                : `Balance Sheet is out of balance by ${difference.toFixed(2)}`
        };
    }

    /**
     * Refresh balance sheet data
     */
    async refreshData(balanceSheetId) {
        this.state.loading = true;
        
        try {
            await this.orm.call(
                "enhanced.balance.sheet",
                "action_generate_report",
                [balanceSheetId]
            );
            
            // Reload the current view
            window.location.reload();
            
            this.notification.add(
                "Balance Sheet data refreshed successfully!",
                { type: "success" }
            );
        } catch (error) {
            this.notification.add(
                `Error refreshing data: ${error.message}`,
                { type: "danger" }
            );
        } finally {
            this.state.loading = false;
        }
    }
}

BalanceSheetDrillDown.template = "enhanced_balance_sheet.DrillDown";

// Register the component
registry.category("actions").add("balance_sheet_drill_down", BalanceSheetDrillDown);

export default BalanceSheetDrillDown;
