# PowerShell script to install wkhtmltopdf for Odoo PDF generation

Write-Host "Installing wkhtmltopdf for Odoo PDF generation..." -ForegroundColor Green

# Download URL for wkhtmltopdf Windows 64-bit
$downloadUrl = "https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.msvc2015-win64.exe"
$installerPath = "$env:TEMP\wkhtmltox-installer.exe"

try {
    # Download the installer
    Write-Host "Downloading wkhtmltopdf installer..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    
    # Run the installer
    Write-Host "Running installer..." -ForegroundColor Yellow
    Start-Process -FilePath $installerPath -ArgumentList "/S" -Wait
    
    # Add to PATH
    Write-Host "Adding to system PATH..." -ForegroundColor Yellow
    $wkhtmlPath = "C:\Program Files\wkhtmltopdf\bin"
    
    # Get current PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    
    # Check if already in PATH
    if ($currentPath -notlike "*$wkhtmlPath*") {
        # Add to PATH
        $newPath = $currentPath + ";" + $wkhtmlPath
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
        Write-Host "Added to system PATH successfully!" -ForegroundColor Green
    } else {
        Write-Host "Already in system PATH!" -ForegroundColor Green
    }
    
    # Clean up
    Remove-Item $installerPath -Force
    
    Write-Host "Installation completed successfully!" -ForegroundColor Green
    Write-Host "Please restart your command prompt and Odoo server." -ForegroundColor Cyan
    
} catch {
    Write-Host "Error during installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please install manually from: https://wkhtmltopdf.org/downloads.html" -ForegroundColor Yellow
}

Write-Host "Press any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
