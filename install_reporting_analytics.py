#!/usr/bin/env python3
"""
Script to install Reporting and Analytics modules in Odoo
"""

import subprocess
import sys
import time

def run_odoo_command(modules, action="init"):
    """Run Odoo command to install/update modules"""
    if isinstance(modules, list):
        modules = ",".join(modules)
    
    cmd = [
        "py", "-3.13", "odoo-bin",
        "--config=odoo.conf",
        f"--{action}={modules}",
        "--stop-after-init",
        "--without-demo=all"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ Successfully installed/updated: {modules}")
            return True
        else:
            print(f"❌ Error installing {modules}:")
            if result.stderr:
                print(result.stderr)
            if result.stdout:
                print(result.stdout)
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout installing {modules}")
        return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("📊 Installing Odoo Reporting and Analytics Modules...")
    
    # Core reporting and analytics modules
    core_reporting_modules = [
        "spreadsheet",
        "spreadsheet_account", 
        "spreadsheet_dashboard",
        "spreadsheet_dashboard_account",
        "board"
    ]
    
    # Dashboard modules for different business areas
    dashboard_modules = [
        "spreadsheet_dashboard_purchase",
        "spreadsheet_dashboard_sale", 
        "spreadsheet_dashboard_hr_expense",
        "spreadsheet_dashboard_hr_timesheet",
        "spreadsheet_dashboard_stock_account",
        "spreadsheet_dashboard_website_sale"
    ]
    
    # Additional analytics modules
    analytics_modules = [
        "analytic",
        "base_automation"
    ]
    
    print("Installing core reporting modules...")
    success = run_odoo_command(core_reporting_modules, "init")
    if success:
        print("✅ Core reporting modules installed!")
    else:
        print("⚠️ Some core modules may have failed")
    
    time.sleep(3)
    
    print("\nInstalling dashboard modules...")
    for module in dashboard_modules:
        print(f"\n📈 Installing {module}...")
        success = run_odoo_command(module, "init")
        if not success:
            print(f"⚠️ Failed to install {module}, continuing...")
        time.sleep(2)
    
    print("\nInstalling analytics modules...")
    success = run_odoo_command(analytics_modules, "init")
    if success:
        print("✅ Analytics modules installed!")
    
    print("\n🎉 Reporting and Analytics installation complete!")
    print("\n📋 What you now have:")
    print("• 📊 Spreadsheet - Create custom reports and dashboards")
    print("• 📈 Dashboard - Visual business intelligence")
    print("• 📋 Board - Customizable dashboards")
    print("• 💰 Account Analytics - Financial reporting")
    print("• 🛒 Sales Analytics - Sales performance tracking")
    print("• 💼 Purchase Analytics - Procurement insights")
    print("• ⏰ Time Analytics - Time tracking reports")
    print("• 💸 Expense Analytics - Expense reporting")
    
    print("\n🚀 Next steps:")
    print("1. Start Odoo: py -3.13 odoo-bin --config=odoo.conf")
    print("2. Open browser: http://localhost:8069")
    print("3. Login and go to 'Reporting' or 'Dashboard' menu")
    print("4. Create custom reports and dashboards")

if __name__ == "__main__":
    main()
