from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

# Placeholder for accounting API views
# TODO: Implement full accounting API

class AccountViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class JournalViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class MoveViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None

class PaymentViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = None
    serializer_class = None
