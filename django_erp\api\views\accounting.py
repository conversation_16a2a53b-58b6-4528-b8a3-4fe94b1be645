from rest_framework import viewsets, serializers
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

# Placeholder serializer for API documentation
class PlaceholderSerializer(serializers.Serializer):
    message = serializers.CharField(default="This endpoint is not yet implemented")

# Placeholder for accounting API views
# TODO: Implement full accounting API

class AccountViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Accounting API endpoints will be implemented soon"})

class JournalViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Journal API endpoints will be implemented soon"})

class MoveViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Account Move API endpoints will be implemented soon"})

class PaymentViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = PlaceholderSerializer

    def list(self, request):
        return Response({"message": "Payment API endpoints will be implemented soon"})
