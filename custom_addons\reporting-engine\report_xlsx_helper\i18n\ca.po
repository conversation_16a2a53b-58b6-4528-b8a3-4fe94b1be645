# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* report_xlsx_helper
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2022-06-15 18:05+0000\n"
"Last-Translator: jabe<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"%(__name__)s, _write_line : programming error detected while processing "
"col_specs_section %(col_specs_section)s, column %(col)s"
msgstr ""
"%(__name__)s, _write_line : error de programació detectat en processar "
"col_specs_section %(col_specs_section)s, columna %(col)s"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/models/ir_actions_report.py:0
#, python-format
msgid "%s model was not found"
msgstr "No s'ha trobat el model %s"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ", cellvalue %s"
msgstr ""

#. module: report_xlsx_helper
#: model:ir.model,name:report_xlsx_helper.model_report_report_xlsx_abstract
#, fuzzy
msgid "Abstract XLSX Report"
msgstr "Informe XLSX abstracte"

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"Excel Sheet name '%(name)s' contains unsupported special characters: "
"'%(special_chars)s'."
msgstr ""
"Error de programació:\n"
"\n"
"El full Excel amb nom '%(name)s' contè caràcters especials no soportats: "
"'%(special_chars)s'."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"Excel Sheet name '%(name)s' should not exceed %(max_chars)s characters."
msgstr ""
"Error de programació:\n"
"\n"
"El full d'Excel de nom '%(name)s' no hauria d'excedir els %(max_chars)s "
"caràcters."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"The '%s' column is not defined in the worksheet column specifications."
msgstr ""
"Error de programació:\n"
"\n"
"La columna '%s' no està definida a les especificacions de columna del full "
"de càlcul."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"The '%s' column is not defined the worksheet column specifications."
msgstr ""
"Error de programació:\n"
"\n"
"La columna '%s' no està definida a les especificacions de columna del full "
"de càlcul."

#. module: report_xlsx_helper
#. odoo-python
#: code:addons/report_xlsx_helper/report/report_xlsx_abstract.py:0
#, python-format
msgid ""
"Programming Error:\n"
"\n"
"The 'title' parameter is mandatory when calling the '_write_ws_title' method."
msgstr ""
"Error de programació:\n"
"\n"
"El paràmetre 'títol' és obligatori si es crida el mètode '_write_ws_title'."

#. module: report_xlsx_helper
#: model:ir.model,name:report_xlsx_helper.model_ir_actions_report
msgid "Report Action"
msgstr "Acció d'informe"

#. module: report_xlsx_helper
#: model:ir.model,name:report_xlsx_helper.model_report_report_xlsx_helper_test_partner_xlsx
#, fuzzy
msgid "Test Partner XLSX Report"
msgstr "Informe XLSX partner prova"
