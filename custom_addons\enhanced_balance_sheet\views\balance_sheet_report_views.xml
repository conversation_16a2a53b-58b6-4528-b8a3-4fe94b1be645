<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Balance Sheet Line Form View -->
        <record id="view_enhanced_balance_sheet_line_form" model="ir.ui.view">
            <field name="name">enhanced.balance.sheet.line.form</field>
            <field name="model">enhanced.balance.sheet.line</field>
            <field name="arch" type="xml">
                <form string="Balance Sheet Line">
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="line_type"/>
                                <field name="section"/>
                                <field name="sequence"/>
                            </group>
                            <group>
                                <field name="bold"/>
                                <field name="underline"/>
                                <field name="indent_level"/>
                                <field name="is_expandable"/>
                            </group>
                        </group>
                        
                        <group string="Amounts">
                            <group>
                                <field name="current_amount" widget="monetary"/>
                                <field name="comparative_amount" widget="monetary"/>
                            </group>
                            <group>
                                <field name="variance_amount" widget="monetary"/>
                                <field name="variance_percentage" widget="percentage"/>
                            </group>
                        </group>
                        
                        <group string="Account Configuration">
                            <field name="account_types"/>
                            <field name="account_ids" widget="many2many_tags"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Balance Sheet Line Tree View -->
        <record id="view_enhanced_balance_sheet_line_tree" model="ir.ui.view">
            <field name="name">enhanced.balance.sheet.line.tree</field>
            <field name="model">enhanced.balance.sheet.line</field>
            <field name="arch" type="xml">
                <tree string="Balance Sheet Lines" decoration-bf="bold == True" 
                      decoration-it="line_type == 'subtotal'" decoration-danger="line_type == 'header'">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="line_type"/>
                    <field name="section"/>
                    <field name="current_amount" widget="monetary"/>
                    <field name="comparative_amount" widget="monetary"/>
                    <field name="variance_amount" widget="monetary"/>
                    <field name="variance_percentage" widget="percentage"/>
                    <field name="bold" invisible="1"/>
                    <field name="is_expandable"/>
                </tree>
            </field>
        </record>

        <!-- Quick Create Balance Sheet Wizard -->
        <record id="view_balance_sheet_wizard" model="ir.ui.view">
            <field name="name">balance.sheet.wizard</field>
            <field name="model">enhanced.balance.sheet</field>
            <field name="arch" type="xml">
                <form string="Create Balance Sheet Report">
                    <sheet>
                        <div class="oe_title">
                            <h1>Create Balance Sheet Report</h1>
                            <p>Generate a professional balance sheet with segregated liabilities and equity sections.</p>
                        </div>
                        
                        <group>
                            <group>
                                <field name="name" placeholder="e.g., Balance Sheet - December 2024"/>
                                <field name="company_id" options="{'no_create': True}"/>
                                <field name="date_to" string="As at Date"/>
                            </group>
                            <group>
                                <field name="target_move"/>
                                <field name="show_zero_balance"/>
                                <field name="comparative_period"/>
                                <field name="comparative_date_to" invisible="not comparative_period" 
                                       string="Comparative As at Date"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_generate_report" type="object" string="Generate Report" 
                                class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Quick Create Action -->
        <record id="action_balance_sheet_wizard" model="ir.actions.act_window">
            <field name="name">Create Balance Sheet</field>
            <field name="res_model">enhanced.balance.sheet</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_balance_sheet_wizard"/>
            <field name="target">new</field>
            <field name="context">{'default_name': 'Balance Sheet Report'}</field>
        </record>

    </data>
</odoo>
