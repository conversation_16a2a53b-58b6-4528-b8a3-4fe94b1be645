<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Enhanced Balance Sheet Form View -->
        <record id="view_enhanced_balance_sheet_form" model="ir.ui.view">
            <field name="name">enhanced.balance.sheet.form</field>
            <field name="model">enhanced.balance.sheet</field>
            <field name="arch" type="xml">
                <form string="Enhanced Balance Sheet">
                    <header>
                        <button name="action_generate_report" type="object" string="Generate Report" 
                                class="btn-primary" invisible="state == 'generated'"/>
                        <button name="action_print_report" type="object" string="Print Report" 
                                class="btn-secondary" invisible="state != 'generated'"/>
                        <button name="action_export_excel" type="object" string="Export to Excel" 
                                class="btn-secondary" invisible="state != 'generated'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,generated"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Balance Sheet Report Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="company_id" options="{'no_create': True}"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="date_from"/>
                                <field name="date_to"/>
                            </group>
                            <group>
                                <field name="target_move"/>
                                <field name="show_zero_balance"/>
                                <field name="comparative_period"/>
                                <field name="comparative_date_from" invisible="not comparative_period"/>
                                <field name="comparative_date_to" invisible="not comparative_period"/>
                            </group>
                        </group>
                        
                        <!-- Balance Check Section -->
                        <group string="Balance Verification" invisible="state != 'generated'">
                            <group>
                                <field name="total_assets" widget="monetary"/>
                                <field name="total_liabilities" widget="monetary"/>
                                <field name="total_equity" widget="monetary"/>
                            </group>
                            <group>
                                <field name="balance_check" widget="monetary" 
                                       decoration-success="balance_check == 0"
                                       decoration-danger="balance_check != 0"/>
                                <div invisible="balance_check == 0" class="alert alert-warning">
                                    <strong>Warning:</strong> Balance Sheet does not balance! 
                                    Assets should equal Liabilities + Equity.
                                </div>
                            </group>
                        </group>
                        
                        <!-- Balance Sheet Lines -->
                        <notebook invisible="state != 'generated'">
                            <page string="Balance Sheet Report">
                                <field name="line_ids" nolabel="1">
                                    <tree decoration-bf="bold == True" decoration-it="line_type == 'subtotal'"
                                          decoration-danger="line_type == 'header'" decoration-success="line_type == 'total'"
                                          create="false" edit="false" delete="false">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name" string="Description"/>
                                        <field name="line_type" invisible="1"/>
                                        <field name="section" invisible="1"/>
                                        <field name="bold" invisible="1"/>
                                        <field name="underline" invisible="1"/>
                                        <field name="current_amount" widget="monetary" sum="Total Current"/>
                                        <field name="comparative_amount" widget="monetary" sum="Total Comparative" 
                                               invisible="not parent.comparative_period"/>
                                        <field name="variance_amount" widget="monetary" 
                                               invisible="not parent.comparative_period"/>
                                        <field name="variance_percentage" widget="percentage"
                                               invisible="not parent.comparative_period"/>
                                        <field name="is_expandable" invisible="1"/>
                                        <button name="action_drill_down_accounts" type="object" 
                                                string="View Accounts" icon="fa-search"
                                                invisible="not is_expandable"/>
                                        <button name="action_drill_down_ledger" type="object" 
                                                string="View Ledger" icon="fa-list"
                                                invisible="not is_expandable"/>
                                        <button name="action_drill_down_entries" type="object" 
                                                string="View Entries" icon="fa-book"
                                                invisible="not is_expandable"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Enhanced Balance Sheet Tree View -->
        <record id="view_enhanced_balance_sheet_tree" model="ir.ui.view">
            <field name="name">enhanced.balance.sheet.tree</field>
            <field name="model">enhanced.balance.sheet</field>
            <field name="arch" type="xml">
                <tree string="Enhanced Balance Sheet Reports">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <field name="state"/>
                    <field name="total_assets" widget="monetary"/>
                    <field name="total_liabilities" widget="monetary"/>
                    <field name="total_equity" widget="monetary"/>
                    <field name="balance_check" widget="monetary" 
                           decoration-success="balance_check == 0"
                           decoration-danger="balance_check != 0"/>
                </tree>
            </field>
        </record>

        <!-- Enhanced Balance Sheet Search View -->
        <record id="view_enhanced_balance_sheet_search" model="ir.ui.view">
            <field name="name">enhanced.balance.sheet.search</field>
            <field name="model">enhanced.balance.sheet</field>
            <field name="arch" type="xml">
                <search string="Enhanced Balance Sheet">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Generated" name="generated" domain="[('state', '=', 'generated')]"/>
                    <filter string="Balanced" name="balanced" domain="[('balance_check', '=', 0)]"/>
                    <filter string="Unbalanced" name="unbalanced" domain="[('balance_check', '!=', 0)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Company" name="group_company" context="{'group_by': 'company_id'}"/>
                        <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'date_to'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Enhanced Balance Sheet Action -->
        <record id="action_enhanced_balance_sheet" model="ir.actions.act_window">
            <field name="name">Enhanced Balance Sheet</field>
            <field name="res_model">enhanced.balance.sheet</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Enhanced Balance Sheet Report!
                </p>
                <p>
                    Generate professional balance sheet reports with separate sections for 
                    Liabilities and Equity, complete with drill-down capabilities from 
                    account level to ledger entries.
                </p>
            </field>
        </record>

    </data>
</odoo>
