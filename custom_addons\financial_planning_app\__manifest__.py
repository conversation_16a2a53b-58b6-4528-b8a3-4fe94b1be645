# -*- coding: utf-8 -*-
{
    'name': 'Financial Planning App with Demographics',
    'version': '********.0',
    'category': 'Accounting/Financial Planning',
    'summary': 'Advanced Financial Planning with Population Demographics and Growth Forecasting',
    'description': """
        Financial Planning App with Demographics
        
        This comprehensive financial planning application provides:
        
        🌍 **Geographic & Demographic Features:**
        - Global population data by continent, country, and city
        - Annual growth rate tracking by country
        - Demographic-based financial forecasting
        - Population trend analysis
        
        📊 **Financial Planning Features:**
        - 60-month financial planning (5 years)
        - Historical data support (-6 months from launch)
        - Monthly, quarterly, and annual projections
        - Growth rate calculations and forecasting
        
        📈 **Advanced Analytics:**
        - Population-based market sizing
        - Demographic trend analysis
        - Financial performance correlation with population data
        - Multi-currency support for global planning
        
        🎯 **Key Capabilities:**
        - Launch date planning with pre-launch analysis
        - Country-wise growth rate management
        - City-level demographic planning
        - Automated financial projections
        - Interactive dashboards and reports
        
        Perfect for businesses planning global expansion, demographic analysis,
        and population-based financial forecasting.
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'account',
        'analytic',
        'spreadsheet_dashboard',
        'board',
    ],
    'data': [
        # Security
        'security/financial_planning_security.xml',
        'security/ir.model.access.csv',
        
        # Data
        'data/continent_data.xml',
        'data/country_data.xml',
        'data/financial_planning_data.xml',
        
        # Views
        'views/continent_views.xml',
        'views/country_views.xml',
        'views/city_views.xml',
        'views/population_data_views.xml',
        'views/financial_plan_views.xml',
        'views/growth_rate_views.xml',
        'views/financial_forecast_views.xml',
        'views/dashboard_views.xml',
        
        # Wizards
        'wizard/financial_planning_wizard_views.xml',
        'wizard/population_import_wizard_views.xml',
        
        # Reports
        'reports/financial_planning_reports.xml',
        'reports/financial_planning_templates.xml',
        
        # Menu
        'views/financial_planning_menu.xml',
    ],
    'demo': [
        'demo/demo_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'financial_planning_app/static/src/css/financial_planning.css',
            'financial_planning_app/static/src/js/financial_planning_dashboard.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    'sequence': 10,
}
