# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* accounting_pdf_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-07 07:04+0000\n"
"PO-Revision-Date: 2022-07-07 07:04+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid ": General ledger"
msgstr "Головна книга"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid ": Trial Balance"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<span>Not due</span>"
msgstr "<span>Без строку</span>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Company:</strong>"
msgstr "<strong>Компанія:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date from :</strong>"
msgstr "<strong>З :</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Date to :</strong>"
msgstr "<strong>По :</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Display Account:</strong>"
msgstr "<strong>Показувати рахунки:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Display Account</strong>"
msgstr "<strong>Показувати рахунки:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Сортовано за:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Journal:</strong>"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Journals:</strong>"
msgstr "<strong>Журнали:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Partner's:</strong>"
msgstr "<strong>Партнера:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Period Length (days)</strong>"
msgstr "Тривалість періоду (днів)"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "<strong>Purchase</strong>"
msgstr "<strong>Купівля</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "<strong>Sorted By:</strong>"
msgstr "<strong>Сортовано за:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "<strong>Start Date:</strong>"
msgstr "<strong>Дата початку: </strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Відповідні проведення:</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Разом</strong>"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Account"
msgstr "Рахунок"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_aged_trial_balance
msgid "Account Aged Trial balance Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_account_report
msgid "Account Common Account Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_common_partner_report
msgid "Account Common Partner Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_partner_ledger
msgid "Account Partner Ledger"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_print_journal
msgid "Account Print Journal"
msgstr "Журнал друку рахунку"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_financial_report
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__children_ids
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__account_report_id
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_reports
msgid "Account Reports"
msgstr "Бухгалтерський звіт"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Account Total"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_type
msgid "Account Type"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_accounting_report
msgid "Accounting Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_ids
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__accounts
msgid "Accounts"
msgstr "Рахунки"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_balance_view
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_aged_partner_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_trial_balance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Aged Partner Balance"
msgstr "Баланс по днях"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr "Баланс по днях звіт"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_payable
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_payable
msgid "Aged Payable"
msgstr "Кредиторська заборгованість по днях"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_aged_receivable
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_aged_receivable
msgid "Aged Receivable"
msgstr "Дебіторська заборгованість по днях"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__all
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__all
msgid "All"
msgstr "Всі"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Entries"
msgstr "Всі записи"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All Posted Entries"
msgstr "Всі опубліковані проведення"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "All accounts"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "All accounts'"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Analytic Account"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_assets0
msgid "Assets"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_audit_reports
msgid "Audit Reports"
msgstr "Аудиторські звіти"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__0
msgid "Automatic formatting"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Balance"
msgstr "Сальдо"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_balancesheet0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_bs
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_bs
msgid "Balance Sheet"
msgstr "Баланс"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Base Amount"
msgstr "Базова сума"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
msgid "Cancel"
msgstr "Скасувати"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Code"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__label_filter
msgid "Column Label"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__company_id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__company_id
msgid "Company"
msgstr "Компанія"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Comparison"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__create_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_uid
msgid "Created by"
msgstr "Створено"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__create_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__create_date
msgid "Created on"
msgstr "Створено"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Credit"
msgstr "Кредит"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Currency"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_date
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_date
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Date"
msgstr "Дата"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from_cmp
msgid "Date From"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to_cmp
msgid "Date To"
msgstr "По"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Date:"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.accounting_report_view
msgid "Dates"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "Debit"
msgstr "Дебет"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_account
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_account
msgid "Display Accounts"
msgstr "Показувати рахунки"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__debit_credit
msgid "Display Debit/Credit Columns"
msgstr "Показувати колонки Дт/Кт"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_financial__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_general_ledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_journal__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_partnerledger__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_tax__display_name
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_trialbalance__display_name
msgid "Display Name"
msgstr "Відобразити назву"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_flat
msgid "Display children flat"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__detail_with_hierarchy
msgid "Display children with hierarchy"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__display_detail
msgid "Display details"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Drill Down Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Dynamic Accounting Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__enable_filter
msgid "Enable Comparison"
msgstr "Увімкнути порівняння"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__date_to
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_to
msgid "End Date"
msgstr "Кінцева дата"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Enhanced Financial Reports"
msgstr "Покращені фінансові звіти"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Записи сортовані за"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Entry Label"
msgstr "Позначка входу"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Excel Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_expense0
msgid "Expense"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__filter_cmp
msgid "Filter by"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_financial
msgid "Financial Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid "Financial Report Style"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_financial_report_tree
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_financial
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_legal_statement
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_reports_settings
msgid "Financial Reports"
msgstr "Фінансові звіти"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Financial Reports in Excel"
msgstr "Фінансові звіти у Excel"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__sign
msgid ""
"For accounts that are typically more debited than credited and that you "
"would like to print as negative amounts in your reports, you should reverse "
"the sign of the balance; e.g.: Expense account. The same applies for "
"accounts that are typically more credited than debited and that you would "
"like to print as positive amounts in your reports; e.g.: Income account."
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#: code:addons/accounting_pdf_reports/report/report_financial.py:0
#: code:addons/accounting_pdf_reports/report/report_general_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_journal.py:0
#: code:addons/accounting_pdf_reports/report/report_partner_ledger.py:0
#: code:addons/accounting_pdf_reports/report/report_tax.py:0
#: code:addons/accounting_pdf_reports/report/report_trial_balance.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_general_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_general_ledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_ledger
msgid "General Ledger"
msgstr "Головна книга"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_report_general_ledger
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_general_ledger
msgid "General Ledger Report"
msgstr "Головна книга звіт"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Group By"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_financial__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_general_ledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_journal__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_partnerledger__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_tax__id
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_trialbalance__id
msgid "ID"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid ""
"If you selected date, this field allow you to add a row to display the "
"amount of debit/credit/balance that precedes the filter you've set."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__initial_balance
msgid "Include Initial Balances"
msgstr "Додати початкові залишки"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_income0
msgid "Income"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid ""
"It adds the currency column on report if the currency differs from the "
"company currency."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__5
msgid "Italic Text (smaller)"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "JRNL"
msgstr "Журнал"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal"
msgstr "Журнал"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__sortby__sort_journal_partner
msgid "Journal & Partner"
msgstr "Журналам та Контрагентам"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_journal
msgid "Journal Audit Report"
msgstr "Журнал аудиторських звітів"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Journal Entry Number"
msgstr "Номер запису в журналі"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
msgid "Journal and Partner"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Journal:"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__journal_ids
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__journal_ids
msgid "Journals"
msgstr "Журнали"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_print_journal_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_print_journal
msgid "Journals Audit"
msgstr "Аудит журналів"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_journal_entries
msgid "Journals Entries"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Label"
msgstr "Мітка"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_financial____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_general_ledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_journal____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_partnerledger____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_tax____last_update
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_report_accounting_pdf_reports_report_trialbalance____last_update
msgid "Last Modified on"
msgstr "Останні зміни"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__write_uid
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__write_date
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__level
msgid "Level"
msgstr ""

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liability0
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_liabilitysum0
msgid "Liability"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__1
msgid "Main Title 1 (bold, underlined)"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Move"
msgstr "Рух"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_financial
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Name"
msgstr "Назва"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Net"
msgstr "База"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__accounting_report__filter_cmp__filter_no
msgid "No Filters"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__display_detail__no_detail
msgid "No detail"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__4
msgid "Normal Text"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__parent_id
msgid "Parent"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Parent Report"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Partner"
msgstr "Партнер"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_partner_ledger_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_partnerledger
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_partner_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Partner Ledger"
msgstr "Розрахунки з партнерами"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_partnerledger
msgid "Partner Ledger Report"
msgstr "Розрахунки з партнерами звіт"

#. module: accounting_pdf_reports
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_finance_partner_reports
msgid "Partner Reports"
msgstr "Звіти по партнерах"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__result_selection
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__result_selection
msgid "Partner's"
msgstr "Партнерів"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Partner:"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Partners"
msgstr "Партнери"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Payable Accounts"
msgstr "Кредиторська заборгованість"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__period_length
msgid "Period Length (days)"
msgstr "Тривалість періоду (днів)"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__1
msgid "Preserve balance sign"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.res_config_settings_view_form
msgid "Preview financial reports without downloading"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
msgid "Print"
msgstr "Друк"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""
"Стовпець валюти, якщо валюта відрізняється від валюти компанії при друку "
"звіту."

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitloss_toreport0
msgid "Profit (Loss) to report"
msgstr "Прибуток (витрати) у звіт"

#. module: accounting_pdf_reports
#: model:account.financial.report,name:accounting_pdf_reports.account_financial_report_profitandloss0
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_report_pl
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report_pl
msgid "Profit and Loss"
msgstr "Доходи та витрати"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable Accounts"
msgstr "Дебіторська заборгованість"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_aged_trial_balance__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_partner_report__result_selection__customer_supplier
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_partner_ledger__result_selection__customer_supplier
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Receivable and Payable Accounts"
msgstr "Дебіторська та Кредиторська заборгованість"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__reconciled
msgid "Reconciled Entries"
msgstr "Узгоджені записи"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_partnerledger
msgid "Ref"
msgstr "Посилання"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal_entries
msgid "Reference:"
msgstr "Посилання:"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_form
msgid "Report"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__name
msgid "Report Name"
msgstr "Назва звіту"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.account_aged_balance_view
msgid "Report Options"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.view_account_financial_report_search
msgid "Report Type"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__account_report_id
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__account_report
msgid "Report Value"
msgstr "Значення звіту"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__sign__-1
msgid "Reverse balance sign"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Sale"
msgstr "Продаж"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sequence
msgid "Sequence"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__sign
msgid "Sign on Reports"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__6
msgid "Smallest Text"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__sortby
msgid "Sort by"
msgstr "Сортувати за"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__date_from
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__date_from
msgid "Start Date"
msgstr "Початкова дата"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_aged_trial_balance__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_balance_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_account_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_common_partner_report__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_general_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_tax_report_wizard__target_move
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_accounting_report__target_move
msgid "Target Moves"
msgstr "Вибрати проведення"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax"
msgstr "Податок"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Amount"
msgstr "Сума податків"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_journal
msgid "Tax Declaration"
msgstr "Податкова декларація"

#. module: accounting_pdf_reports
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_account_tax
#: model:ir.model,name:accounting_pdf_reports.model_account_tax_report_wizard
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_tax
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_account_report
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_tax
msgid "Tax Report"
msgstr "Податковий звіт"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_tax_report
msgid "Tax Reports"
msgstr "Податкові звіти"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__label_filter
msgid ""
"This label will be displayed on report to show the balance computed for the "
"given comparison filter."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_accounting_report__debit_credit
msgid ""
"This option allows you to get more details about the way your balances are "
"computed. Because it is space consuming, we do not allow to use it while "
"doing a comparison."
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__2
msgid "Title 2 (bold)"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__style_overwrite__3
msgid "Title 3 (bold, smaller)"
msgstr ""

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_agedpartnerbalance
msgid "Total"
msgstr "Разом"

#. module: accounting_pdf_reports
#: model:ir.actions.act_window,name:accounting_pdf_reports.action_account_balance_menu
#: model:ir.actions.report,name:accounting_pdf_reports.action_report_trial_balance
#: model:ir.ui.menu,name:accounting_pdf_reports.menu_general_balance_report
msgid "Trial Balance"
msgstr "Пробний баланс"

#. module: accounting_pdf_reports
#: model:ir.model,name:accounting_pdf_reports.model_account_balance_report
#: model:ir.model,name:accounting_pdf_reports.model_report_accounting_pdf_reports_report_trialbalance
msgid "Trial Balance Report"
msgstr "Пробний баланс звіт"

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_financial_report__type
msgid "Type"
msgstr "Тип"

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/report/report_aged_partner.py:0
#, python-format
msgid "Unknown Partner"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_financial_report__type__sum
msgid "View"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_print_journal__amount_currency
#: model:ir.model.fields,field_description:accounting_pdf_reports.field_account_report_partner_ledger__amount_currency
msgid "With Currency"
msgstr "З валютою"

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__not_zero
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__not_zero
msgid "With balance is not equal to 0"
msgstr "З не нульовим сальдо"

#. module: accounting_pdf_reports
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With balance not equal to zero"
msgstr ""

#. module: accounting_pdf_reports
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_balance_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_common_account_report__display_account__movement
#: model:ir.model.fields.selection,name:accounting_pdf_reports.selection__account_report_general_ledger__display_account__movement
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_general_ledger
#: model_terms:ir.ui.view,arch_db:accounting_pdf_reports.report_trialbalance
msgid "With movements"
msgstr "По яких був рух"

#. module: accounting_pdf_reports
#: model:ir.model.fields,help:accounting_pdf_reports.field_account_financial_report__style_overwrite
msgid ""
"You can set up here the format you want this record to be displayed. If you "
"leave the automatic formatting, it will be computed based on the financial "
"reports hierarchy (auto-computed field 'level')."
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/account_general_ledger.py:0
#, python-format
msgid "You must define a Start Date"
msgstr ""

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#, python-format
msgid "You must set a period length greater than 0."
msgstr "Обраний період повинен бути більшим за 0."

#. module: accounting_pdf_reports
#: code:addons/accounting_pdf_reports/wizard/aged_partner.py:0
#, python-format
msgid "You must set a start date."
msgstr ""
