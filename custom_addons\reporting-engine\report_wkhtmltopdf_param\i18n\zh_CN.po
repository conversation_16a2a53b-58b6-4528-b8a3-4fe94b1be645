# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_wkhtmltopdf_param
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-08-17 02:52+0000\n"
"PO-Revision-Date: 2017-08-17 02:52+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Chinese (China) (https://www.transifex.com/oca/teams/23907/"
"zh_CN/)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__create_uid
msgid "Created by"
msgstr "创建者"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__create_date
msgid "Created on"
msgstr "创建时间"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat__custom_params
msgid "Custom Parameters"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,help:report_wkhtmltopdf_param.field_report_paperformat__custom_params
msgid "Custom Parameters passed forward as wkhtmltopdf command arguments"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: report_wkhtmltopdf_param
#. odoo-python
#: code:addons/report_wkhtmltopdf_param/models/report_paperformat.py:0
#, python-format
msgid "Failed to create a PDF using the provided parameters."
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__id
msgid "ID"
msgstr "ID"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__name
msgid "Name"
msgstr "名称"

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__paperformat_id
msgid "Paper Format"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model,name:report_wkhtmltopdf_param.model_report_paperformat
msgid "Paper Format Config"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model,name:report_wkhtmltopdf_param.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,help:report_wkhtmltopdf_param.field_report_paperformat_parameter__name
msgid "The command argument name. Remember to add prefix -- or -"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model.fields,field_description:report_wkhtmltopdf_param.field_report_paperformat_parameter__value
msgid "Value"
msgstr ""

#. module: report_wkhtmltopdf_param
#: model:ir.model,name:report_wkhtmltopdf_param.model_report_paperformat_parameter
msgid "wkhtmltopdf parameters"
msgstr ""

#~ msgid "Last Modified on"
#~ msgstr "最后修改时间"
